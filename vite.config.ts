import vue from '@vitejs/plugin-vue'
import fs from 'fs'
import { resolve } from 'path'
import autoImport from 'unplugin-auto-import/vite'
import { defineConfig, loadEnv } from 'vite'
import { lazyImport } from 'vite-plugin-lazy-import'

// 将驼峰命名转换为短横线命名
function pascalToKebab(pascal: string) {
  return pascal.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase()
}

// 动态生成别名配置
function generateAlias() {
  const baseAlias = {
    lodash: resolve(__dirname, '.', 'node_modules/lodash-es'),
    '@': resolve(__dirname, '.', 'src'),
    '@c': resolve(__dirname, '.', 'src/components'),
    '@v': resolve(__dirname, '.', 'src/views'),
  }

  const packagesPath = resolve(__dirname, 'packages')
  const dirs = fs.readdirSync(packagesPath).filter(file => file !== 'dist' && fs.statSync(resolve(packagesPath, file)).isDirectory())

  const dynamicAlias: Record<string, string> = {}

  dirs.forEach(dir => {
    const dirPath = resolve(packagesPath, dir)
    const hasSubDirs = fs.readdirSync(dirPath).some(file => fs.statSync(resolve(dirPath, file)).isDirectory())
    if (hasSubDirs) {
      const subDirs = fs.readdirSync(dirPath).filter(file => fs.statSync(resolve(dirPath, file)).isDirectory())
      subDirs.forEach(subDir => {
        const kebabName = pascalToKebab(dir)
        const subKebabName = pascalToKebab(subDir)
        dynamicAlias[`@mh-${kebabName}/${subKebabName}`] = resolve(packagesPath, dir, subDir, 'index.ts')
      })
    }
  })

  return { ...baseAlias, ...dynamicAlias }
}

export default defineConfig(async ({ command, mode }: any): Promise<any> => {
  const env = loadEnv(mode, process.cwd(), '')
  const { VITE_APP_ALIAS = '' } = env

  const { default: unocss } = await import('unocss/vite')

  return {
    assetsDir: '',
    base: `/${VITE_APP_ALIAS}`,
    build: {
      outDir: `dist/${VITE_APP_ALIAS}`,
      rollupOptions: {
        external: ['vue', 'vue-router', 'crypto-js', 'dayjs', 'ant-design-vue', '@surely-vue/table', '@ant-design/icons-vue', 'lodash-es', 'axios', '@idmy/core', '@idmy/antd'],
        plugins: [],
      },
      target: 'es2015',
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            'color-text-4': 'var(--color-neutral-6)',
          },
          javascriptEnabled: true,
        },
      },
    },
    plugins: [
      vue({ script: { defineModel: true } }),
      lazyImport({ resolvers: [] }),
      unocss(),
      autoImport({
        defaultExportByFilename: true,
        dts: 'auto-imports.d.ts',
        imports: [
          'vue',
          'vue-router',
          { '@idmy/core': ['fmt', 'cfg', 'sleep', 'useLoading', 'useCache', 'http', 'newError', 'Dialog', 'Modal', 'Message', 'dayjs', 'Data', 'back', 'to', 'currentRouter', 'currentRoute', 'emitter', 'clearObject'] },
          { 'lodash-es': ['isNil', 'isString', 'isArray', 'isNumber', 'isArray', 'cloneDeep', 'isEmpty'] },
        ],
        vueTemplate: true,
      }),
    ],
    resolve: {
      alias: generateAlias(),
    },
    server: {
      host: '0.0.0.0',
      port: 8002,
    },
  }
})
