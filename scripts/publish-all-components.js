const { execSync } = require('child_process')
const { readdirSync, statSync } = require('fs')
const { resolve, join } = require('path')
const { existsSync } = require('fs')

// 标准化路径分隔符
const normalizePath = path => path.replace(/\\/g, '/')

// 递归获取所有组件目录
function findComponents(dir, basePath = '') {
  const components = []
  const files = readdirSync(dir)

  for (const file of files) {
    const filePath = join(dir, file)
    const stat = statSync(filePath)

    if (stat.isDirectory()) {
      if (['node_modules', 'dist', '.tmp'].includes(file)) {
        continue
      }

      // 检查是否是组件目录（包含 package.json）
      if (existsSync(join(filePath, 'package.json'))) {
        components.push(normalizePath(join(basePath, file)))
      } else {
        // 递归查找子目录
        components.push(...findComponents(filePath, join(basePath, file)))
      }
    }
  }

  return components
}

// 获取所有组件目录
const componentsDir = resolve(__dirname, '../packages')
const components = findComponents(componentsDir)

// 发布每个组件
console.log('开始发布所有组件...\n')

// 逐个发布组件
for (const component of components) {
  console.log(`\n开始处理组件: ${component}`)
  try {
    // 发布组件
    console.log(`发布组件 ${component}...`)
    execSync(`pnpm publish:component ${component}`, {
      stdio: 'inherit',
    })

    console.log(`组件 ${component} 处理成功！`)
  } catch (error) {
    console.error(`组件 ${component} 处理失败:`, error)
    process.exit(1)
  }
}

console.log('\n所有组件处理完成！')
