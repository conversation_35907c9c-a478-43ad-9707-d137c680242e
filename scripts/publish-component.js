const { execSync } = require('child_process')
const { readdirSync, statSync, copyFileSync, mkdirSync, existsSync, rmSync, readFileSync, writeFileSync } = require('fs')
const { resolve, join, dirname } = require('path')
const { unlinkSync } = require('node:fs')

// 获取要发布的组件路径
const componentPath = process.argv[2]
if (!componentPath) {
  console.error('请指定要发布的组件路径！')
  console.error('示例: node scripts/publish-component.js Hip/FeeCat')
  console.error('示例: node scripts/publish-component.js Hip  # 发布 Hip 目录下的所有组件')
  process.exit(1)
}

// 确保目标目录存在
const ensureDir = dir => {
  if (!existsSync(dir)) {
    mkdirSync(dir, { recursive: true })
  }
}

// 复制文件
const copyFile = (src, dest) => {
  if (existsSync(src)) {
    ensureDir(dirname(dest))
    copyFileSync(src, dest)
  }
}

// 复制目录
const copyDir = (src, dest) => {
  if (existsSync(src)) {
    ensureDir(dest)
    const files = readdirSync(src)
    files.forEach(file => {
      const srcPath = join(src, file)
      const destPath = join(dest, file)
      if (statSync(srcPath).isDirectory()) {
        copyDir(srcPath, destPath)
      } else {
        copyFileSync(srcPath, destPath)
      }
    })
  }
}

// 复制类型声明文件
const copyDtsFiles = (src, dest) => {
  if (existsSync(src)) {
    ensureDir(dest)
    const files = readdirSync(src)
    files.forEach(file => {
      const srcPath = join(src, file)
      const destPath = join(dest, file)
      if (statSync(srcPath).isDirectory()) {
        copyDtsFiles(srcPath, destPath)
      } else if (file.endsWith('.d.ts')) {
        copyFile(srcPath, destPath)
      }
    })
  }
}

// 清理目录
const cleanDir = dir => {
  if (existsSync(dir)) {
    rmSync(dir, { recursive: true, force: true })
  }
}

// 处理 workspace 依赖版本
const processWorkspaceDeps = packageJson => {
  const deps = { ...packageJson.dependencies }
  const devDeps = { ...packageJson.devDependencies }
  const processedDeps = {}
  const processedDevDeps = {}

  // 从包名中提取组件路径
  const getComponentPath = packageName => {
    const parts = packageName.split('/')
    if (parts.length >= 2) {
      const scope = parts[0].replace('@', '') // 移除 @ 前缀
      // 将组件名转换为大驼峰格式，如 visit-form -> VisitForm
      const componentName = parts[1]
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join('')
      // 处理多段的 scope 名称，如 mh-inpatient-hsd
      const scopeParts = scope.split('-')
      if (scopeParts.length >= 3 && scopeParts[0] === 'mh') {
        // 对于 mh-inpatient-hsd 这样的情况，转换为 InpatientHsd
        const componentScope = scopeParts
          .slice(1)
          .map(part => part.charAt(0).toUpperCase() + part.slice(1))
          .join('')
        return `${componentScope}/${componentName}`
      } else if (scopeParts.length >= 2) {
        const componentScope = scopeParts[1].charAt(0).toUpperCase() + scopeParts[1].slice(1)
        return `${componentScope}/${componentName}`
      }
    }
    return null
  }

  // 处理 dependencies
  for (const [name, version] of Object.entries(deps)) {
    if (version.startsWith('workspace:')) {
      const componentPath = getComponentPath(name)
      if (componentPath) {
        try {
          const depPackageJson = JSON.parse(readFileSync(resolve(__dirname, '../packages', componentPath, 'package.json'), 'utf-8'))
          let processedVersion = depPackageJson.version
          // 如果设置了发布后缀，给依赖也加上对应的后缀
          if (process.env.PUBLISH_SUFFIX) {
            processedVersion = `${processedVersion}-${process.env.PUBLISH_SUFFIX}`
          }
          processedDeps[name] = `^${processedVersion}`
        } catch (error) {
          console.error(`无法解析 workspace 包 ${name} 的版本:`, error)
          processedDeps[name] = version
        }
      } else {
        processedDeps[name] = version
      }
    } else {
      processedDeps[name] = version
    }
  }

  // 处理 devDependencies
  for (const [name, version] of Object.entries(devDeps)) {
    if (version.startsWith('workspace:')) {
      const componentPath = getComponentPath(name)
      if (componentPath) {
        try {
          const depPackageJson = JSON.parse(readFileSync(resolve(__dirname, '../packages', componentPath, 'package.json'), 'utf-8'))
          let processedVersion = depPackageJson.version
          // 如果设置了发布后缀，给依赖也加上对应的后缀
          if (process.env.PUBLISH_SUFFIX) {
            processedVersion = `${processedVersion}-${process.env.PUBLISH_SUFFIX}`
          }
          processedDevDeps[name] = `^${processedVersion}`
        } catch (error) {
          console.error(`无法解析 workspace 包 ${name} 的版本:`, error)
          processedDevDeps[name] = version
        }
      } else {
        processedDevDeps[name] = version
      }
    } else {
      processedDevDeps[name] = version
    }
  }

  // 如果设置了发布后缀，给当前包的版本也加上对应的后缀
  if (process.env.PUBLISH_SUFFIX) {
    packageJson.version = `${packageJson.version}-${process.env.PUBLISH_SUFFIX}`
  }

  return {
    ...packageJson,
    dependencies: processedDeps,
    devDependencies: processedDevDeps,
  }
}

const tsConfig = {
  compilerOptions: {
    declaration: true,
    emitDeclarationOnly: true,
    preserveSymlinks: false,
    target: 'esnext',
    module: 'esnext',
    outDir: 'dist',
    allowJs: true,
    strict: false,
    jsx: 'preserve',
    moduleResolution: 'node',
    skipLibCheck: true,
    esModuleInterop: false,
    allowSyntheticDefaultImports: true,
    forceConsistentCasingInFileNames: true,
    useDefineForClassFields: true,
    sourceMap: true,
    lib: ['esnext', 'dom', 'dom.iterable', 'scripthost'],
    baseUrl: '.',
    types: ['node', 'vite/client'],
  },
  include: ['../../auto-imports.d.ts', './**/*.ts', './**/*.d.ts', './**/*.tsx', './**/*.vue'],
  exclude: ['node_modules', 'dist'],
}

function buildTypes(normalizedPath) {
  try {
    // 将 tsConfig 内容写到 packages/${normalizedPath}/tsconfig.json 文件中
    writeFileSync(resolve(__dirname, '../packages', normalizedPath, 'tsconfig.json'), JSON.stringify(tsConfig, null, 2))
    execSync(`vue-tsc --project packages/${normalizedPath}/tsconfig.json --emitDeclarationOnly --outDir dist/${normalizedPath} --declarationDir dist/${normalizedPath}`, {
      stdio: 'inherit',
      env: { ...process.env },
    })
  } catch (e) {
    console.info(e)
  } finally {
    // 删除 tsConfig 文件
    unlinkSync(resolve(__dirname, '../packages', normalizedPath, 'tsconfig.json'))
  }
}

// 发布单个组件
const publishComponent = componentPath => {
  // 统一路径分隔符为 /
  const normalizedPath = componentPath.replace(/\\/g, '/')
  console.log(`\n开始发布组件: ${normalizedPath}`)

  try {
    // 设置环境变量
    process.env.COMPONENT = normalizedPath

    // 1. 构建组件（除非设置了跳过构建）
    if (!process.env.SKIP_BUILD) {
      console.log(`构建组件 ${normalizedPath}...`)
      if (process.argv.includes('--no-publish')) {
        execSync('pnpm run build:es', {
          stdio: 'inherit',
          env: { ...process.env },
        })
      } else {
        execSync('pnpm run build:all', {
          stdio: 'inherit',
          env: { ...process.env },
        })
      }
    }

    // 2. 准备发布目录
    const sourceDir = resolve(__dirname, '../packages', normalizedPath)
    const tempDir = resolve(__dirname, '../.tmp', normalizedPath)
    // 修改构建目录路径为项目根目录下的 dist
    const distDir = resolve(__dirname, '../dist', normalizedPath)

    // 检查组件目录是否存在
    if (!existsSync(sourceDir)) {
      console.error(`错误：组件 ${normalizedPath} 不存在！`)
      process.exit(1)
    }

    buildTypes(normalizedPath)

    // 清理并创建临时目录
    cleanDir(tempDir)
    ensureDir(tempDir)

    // 复制文件到临时目录
    // 复制并处理 package.json
    const packageJson = JSON.parse(readFileSync(join(sourceDir, 'package.json'), 'utf-8'))
    const processedPackageJson = processWorkspaceDeps(packageJson)
    writeFileSync(join(tempDir, 'package.json'), JSON.stringify(processedPackageJson, null, 2))

    // 复制构建产物
    console.log('开始复制构建产物...')

    // 检查构建目录是否存在
    if (!existsSync(distDir)) {
      console.error(`错误：构建目录 ${distDir} 不存在！请先运行 pnpm run build:all`)
      process.exit(1)
    }

    // 列出构建目录内容
    console.log('构建目录内容:', readdirSync(distDir))

    // 复制所有构建产物
    console.log('复制所有构建产物...')
    copyDir(distDir, tempDir)

    // 复制源文件的 README.md（如果存在）
    const readmeFile = join(sourceDir, 'README.md')
    if (existsSync(readmeFile)) {
      console.log('复制 README.md...')
      copyFile(readmeFile, join(tempDir, 'README.md'))
    }

    // 检查临时目录内容
    console.log('临时目录内容:', readdirSync(tempDir))
    if (process.argv.includes('--no-publish')) {
      return
    }

    // 3. 进入临时目录并发布
    console.log(`发布组件 ${normalizedPath}...`)
    process.chdir(tempDir)

    execSync('pnpm publish --no-git-checks --access public', { stdio: 'inherit' })

    // 4. 返回原目录并清理临时目录
    process.chdir(resolve(__dirname, '..'))
    cleanDir(tempDir)

    console.log(`\n组件 ${normalizedPath} 发布成功！`)
  } catch (error) {
    console.error(`组件 ${normalizedPath} 发布失败:`, error)
    throw error
  }
}

// 发布目录下的所有组件
const publishDirectory = dirPath => {
  const normalizedPath = dirPath.replace(/\\/g, '/')
  console.log(`\n开始发布目录: ${normalizedPath}`)

  const fullPath = resolve(__dirname, '../packages', normalizedPath)

  // 检查目录是否存在
  if (!existsSync(fullPath) || !statSync(fullPath).isDirectory()) {
    console.error(`错误：目录 ${normalizedPath} 不存在！`)
    process.exit(1)
  }

  // 获取目录下的所有子目录
  const components = readdirSync(fullPath).filter(file => {
    const filePath = join(fullPath, file)
    return statSync(filePath).isDirectory() && existsSync(join(filePath, 'package.json')) // 确保是组件目录
  })

  if (components.length === 0) {
    console.error(`错误：目录 ${normalizedPath} 下没有找到可发布的组件！`)
    process.exit(1)
  }

  console.log(`找到 ${components.length} 个组件：`, components)

  // 发布每个组件
  components.forEach(component => {
    const componentPath = `${normalizedPath}/${component}`
    try {
      publishComponent(componentPath)
    } catch (error) {
      console.error(`组件 ${componentPath} 发布失败，继续发布其他组件...`)
    }
  })

  console.log(`\n目录 ${normalizedPath} 下的所有组件发布完成！`)
}

// 开始发布流程
try {
  const fullPath = resolve(__dirname, '../packages', componentPath)

  // 检查路径是否存在
  if (!existsSync(fullPath)) {
    console.error(`错误：路径 ${componentPath} 不存在！`)
    process.exit(1)
  }

  // 检查是否是目录
  const isDirectory = statSync(fullPath).isDirectory()

  // 如果是目录，检查是否包含 package.json
  const hasPackageJson = existsSync(join(fullPath, 'package.json'))

  // 如果既是目录又包含 package.json，说明是组件目录
  if (isDirectory && hasPackageJson) {
    publishComponent(componentPath)
  }
  // 如果只是目录，说明是包含多个组件的目录
  else if (isDirectory) {
    publishDirectory(componentPath)
  }
  // 如果是文件，报错
  else {
    console.error(`错误：${componentPath} 不是有效的组件或目录！`)
    process.exit(1)
  }
} catch (error) {
  // 如果发生错误，确保我们返回到原始目录
  try {
    process.chdir(resolve(__dirname, '..'))
  } catch (e) {
    // 忽略切换目录的错误
  }
  console.error(`发布失败:`, error)
  process.exit(1)
}
