{
  "compilerOptions": {
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@c/*": [
        "./src/components/*"
      ],
      "@v/*": [
        "./src/views/*"
      ],
      "@mh-base/core": [
        "./packages/Base/Core"
      ],
      "@mh-base/keymap": [
        "./packages/Base/Keymap"
      ],
      "@mh-base/modal": [
        "./packages/Base/Modal"
      ],
      "@mh-bcs/bill": [
        "./packages/Bcs/Bill"
      ],
      "@mh-bcs/cash": [
        "./packages/Bcs/Cash"
      ],
      "@mh-bcs/cash-check": [
        "./packages/Bcs/CashCheck"
      ],
      "@mh-bcs/cash-payment": [
        "./packages/Bcs/CashPayment"
      ],
      "@mh-bcs/deposit": [
        "./packages/Bcs/Deposit"
      ],
      "@mh-bcs/help": [
        "./packages/Bcs/Help"
      ],
      "@mh-bcs/pay": [
        "./packages/Bcs/Pay"
      ],
      "@mh-bcs/print": [
        "./packages/Bcs/Print"
      ],
      "@mh-bcs/settle": [
        "./packages/Bcs/Settle"
      ],
      "@mh-bcs/util": [
        "./packages/Bcs/Util"
      ],
      "@mh-hip/art-cat": [
        "./packages/Hip/ArtCat"
      ],
      "@mh-hip/art-sub-type": [
        "./packages/Hip/ArtSubType"
      ],
      "@mh-hip/disease": [
        "./packages/Hip/Disease"
      ],
      "@mh-hip/fee-cat": [
        "./packages/Hip/FeeCat"
      ],
      "@mh-hip/fee-type": [
        "./packages/Hip/FeeType"
      ],
      "@mh-hip/medical-type": [
        "./packages/Hip/MedicalType"
      ],
      "@mh-hip/org": [
        "./packages/Hip/Org"
      ],
      "@mh-hip/payment-type": [
        "./packages/Hip/PaymentType"
      ],
      "@mh-hip/relationship": [
        "./packages/Hip/Relationship"
      ],
      "@mh-hip/route": [
        "./packages/Hip/Route"
      ],
      "@mh-hip/util": [
        "./packages/Hip/Util"
      ],
      "@mh-hsd/base": [
        "./packages/Hsd/Base"
      ],
      "@mh-hsd/recipe": [
        "./packages/Hsd/Recipe"
      ],
      "@mh-hsd/report": [
        "./packages/Hsd/Report"
      ],
      "@mh-hsd/selector": [
        "./packages/Hsd/Selector"
      ],
      "@mh-hsd/util": [
        "./packages/Hsd/Util"
      ],
      "@mh-hsd/visit": [
        "./packages/Hsd/Visit"
      ],
      "@mh-inpatient-hsd/billing-entry": [
        "./packages/InpatientHsd/BillingEntry"
      ],
      "@mh-inpatient-hsd/oe-apply": [
        "./packages/InpatientHsd/OeApply"
      ],
      "@mh-inpatient-hsd/oe-exec": [
        "./packages/InpatientHsd/OeExec"
      ],
      "@mh-inpatient-hsd/oe-fee-change": [
        "./packages/InpatientHsd/OeFeeChange"
      ],
      "@mh-inpatient-hsd/order-fee-change": [
        "./packages/InpatientHsd/OrderFeeChange"
      ],
      "@mh-inpatient-hsd/section-route-consumable": [
        "./packages/InpatientHsd/SectionRouteConsumable"
      ],
      "@mh-inpatient-hsd/selector": [
        "./packages/InpatientHsd/Selector"
      ],
      "@mh-inpatient-hsd/util": [
        "./packages/InpatientHsd/Util"
      ],
      "@mh-inpatient-hsd/visit-form": [
        "./packages/InpatientHsd/VisitForm"
      ],
      "@mh-inpatient-hsd/visit-info": [
        "./packages/InpatientHsd/VisitInfo"
      ],
      "@mh-inpatient-hsd/wm-bill-detail": [
        "./packages/InpatientHsd/WmBillDetail"
      ],
      "@mh-mi/card-reader": [
        "./packages/Mi/CardReader"
      ],
      "@mh-mi/dip": [
        "./packages/Mi/Dip"
      ],
      "@mh-mi/util": [
        "./packages/Mi/Util"
      ],
      "@mh-wm/batch-adjust": [
        "./packages/Wm/BatchAdjust"
      ],
      "@mh-wm/bills-track-code": [
        "./packages/Wm/BillsTrackCode"
      ],
      "@mh-wm/count": [
        "./packages/Wm/Count"
      ],
      "@mh-wm/inpatient-track-code": [
        "./packages/Wm/InpatientTrackCode"
      ],
      "@mh-wm/maker": [
        "./packages/Wm/Maker"
      ],
      "@mh-wm/pharmacy": [
        "./packages/Wm/Pharmacy"
      ],
      "@mh-wm/recipe-track-code": [
        "./packages/Wm/RecipeTrackCode"
      ],
      "@mh-wm/req-component": [
        "./packages/Wm/ReqComponent"
      ],
      "@mh-wm/scm-cust": [
        "./packages/Wm/ScmCust"
      ],
      "@mh-wm/track-code": [
        "./packages/Wm/TrackCode"
      ],
      "@mh-wm/util": [
        "./packages/Wm/Util"
      ]
    },
    "preserveSymlinks": false,
    "target": "esnext",
    "module": "esnext",
    "outDir": "dist",
    "allowJs": true,
    "strict": false,
    "jsx": "preserve",
    "moduleResolution": "node",
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "useDefineForClassFields": true,
    "declaration": false,
    "sourceMap": true,
    "lib": [
      "esnext",
      "dom",
      "dom.iterable",
      "scripthost"
    ],
    "baseUrl": ".",
    "types": [
      "node",
      "vite/client",
      "vue"
    ]
  },
  "include": [
    "auto-imports.d.ts",
    "vite.config.ts",
    "types/**/*.d.ts",
    "global.d.ts",
    "packages/**/*.ts",
    "packages/**/*.d.ts",
    "packages/**/*.tsx",
    "packages/**/*.vue",
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ],
}