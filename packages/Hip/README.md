# 自动配置 tsconfig.json paths
运行 `scripts/generate-tsconfig-paths.js` tsconfig.json paths 会自动添加对应的配置

# 发布该目录下全部组件
pnpm publish:component Hip

# 组件说明
### 工具类
pnpm publish:component Hip/Util

### 条目分类
"@mh-hip/art-cat": "^1.0.0",
import { StockReqCat } from '@mh-hip/art-cat'
pnpm publish:component Hip/ArtCat
<StockReqCat v-model="cat" type="Select" @change="onSearch" style="width: 120px" ml-8px/>

### 条目亚类
"@mh-hip/art-sub-type": "^1.0.0",
import { ArtSubTypeDict } from '@mh-hip/art-sub-type'
pnpm publish:component Hip/ArtSubType
<ArtSubTypeDict v-model="subType" type="Select" @change="onSearch" style="width: 120px" ml-8px/>

### 给药途径
"@mh-hip/route": "^1.0.0",
import { RouteDict } from '@mh-hip/route'
pnpm publish:component Hip/Route
<RouteDict v-model="routeId" type="Select" @change="onSearch" style="width: 120px" ml-8px/>
