//  配置
export const dictConfigs = {
  // 获取基础数据
  mdiBasicData: {
    url: '/hip-base/basicdata/findAll'
  },
  // 证件类型
  certtype: {
    url: "/hip-base/certtype/findAll",
    label: "certTypeName",
    value: "certTypeId",
  },
  // 给药途径
  routetype: {
    url: '/hip-base/routetype/findAll',
    label: 'routeName',
    value: 'routeId'
  },
  // 医保险种
  insurancetype: {
    url: "/hip-base/insurancetype/findAll",
    label: "insuranceName",
    value: "insuranceTypeId",
  },
  // 医保付费方式
  paymenttype: {
    url: "/hip-base/paymenttype/findAll",
    label: "paymentName",
    value: "paymentId",
  },
  // 住院类型
  inpatienttype: {
    url: "/hip-base/inpatienttype/findAll",
    label: "inpatientName",
    value: "inpatientId",
  },
  // 与患者关系
  relationship: {
    url: "/hip-base/relationship/findAll",
    label: "relationshipName",
    value: "relationshipId",
  },
  // 医疗类别
  medicaltype: {
    url: "/hip-base/medicaltype/findAll",
    label: "medTypeName",
    value: "medTypeId",
  },
  // 人员类别
  persontype: {
    url: "/hip-base/persontype/findAll",
    label: "psnTypeName",
    value: "psnTypeId",
  },
  // 入院途径
  admissionway: {
    url: "/hip-base/admissionway/findAll",
    label: "admissionWayName",
    value: "admissionWayId",
  },
  bed: {
    url: '/hip-base/bed/findAll',
  },
  bedWithOrgItemPrice: {
    url: '/hip-base/bed/findAllWithOrgItemPrice',
  },
  // 条目列表
  article: {
    url: '/hip-base/article/pageByOrg',
    label: 'name',
    value: 'id'
  },
  // 机构物价条目
  orgItemPrice: {
    url: '/hip-base/orgitemprice/pageByOrg',
    label: 'name',
    value: 'id'
  },
  // 出院方式
  dischargeWay: {
    url: '/hip-base/dischargeway/findAll',
    label: 'dischargeWayName',
    value: 'dischargeWayId'
  },
  // 样本类型
  specimenType: {
    url: '/hip-base/specimentype/findAll',
    label: 'specimenTypeName',
    value: 'specimenTypeId'
  },
  // 护理操作
  careAction: {
    url: '/hip-base/careaction/findAll',
    label: 'specimenTypeName',
    value: 'specimenTypeId'
  },
  // 费用类别
  feetype: {
    url: '/hip-base/feetype/findAll',
    label: 'feeTypeName',
    value: 'feeTypeId'
  },
  // 自定义费别
  udfType: {
    url: '/hip-base/orgarttype/findAll',
    label: 'udfTypeName',
    value: 'udfTypeId'
  },
  // 慢特备案查询
  getPatientMiDiseaseLsByIdcertNo: {
    url: "/hip-base/patient/getPatientMiDiseaseLsByIdcertNo"
  }
}

const api = {}
Object.keys(dictConfigs).forEach((key: string) => {
  api[key] = dictConfigs[key]['url']
})
// 暴露url 方便其他地方使用
export const apiUrl = {
  ...api
}
export function getDictData(dictType: string = "certtype", params:any = {}) {
  if (!apiUrl[dictType]) {
    console.error(`接口地址未配置:${dictType}`);
    return { data: [] };
  }
  return http.post(apiUrl[dictType], params, { appKey: 'hip' })
}
