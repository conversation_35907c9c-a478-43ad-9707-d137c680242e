import { Data, http } from '@idmy/core'

export async function allPaymentTypes(convert = false): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/paymenttype/findAll', {}, { appKey: 'hip' })
  if (convert) {
    return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.paymentCode, id: row.paymentId, value: row.paymentCode, label: row.paymentName, data: row }))
  } else {
    return arr
  }
}

export async function allInsuranceTypes(convert = false): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/insurancetype/findAll', {}, { appKey: 'hip' })
  if (convert) {
    return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.insuranceCode, id: row.insuranceTypeId, value: row.insuranceTypeId, label: row.insuranceName, data: row }))
  } else {
    return arr
  }
}

export async function allMedicalTypes(convert = false): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/medicaltype/findAll', {}, { appKey: 'hip' })
  if (convert) {
    return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.medTypeCode, id: row.medTypeId, value: row.medTypeId, label: row.medTypeName, data: row }))
  } else {
    return arr
  }
}

export async function allMiPaymentMethods(convert = false): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/mipaymethod/findAll', {}, { appKey: 'hip' })
  if (convert) {
    return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.miPaymethodCode, id: row.miPaymethodId, value: row.miPaymethodCode, label: row.miPaymethodName, data: row }))
  } else {
    return arr
  }
}

export async function allFeeCats(convert = false): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/feecat/findAll', {}, { appKey: 'hip' })
  if (convert) {
    return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.feeCatCode, id: row.feeCatId, value: row.feeCatId, label: row.feeCatName, data: row }))
  } else {
    return arr
  }
}

export async function allArtSubTypes(convert = false): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/artsubtype/findAll', {}, { appKey: 'hip' })
  if (convert) {
    return arr.map(row => ({ code: row.subtypeCode, id: row.subtypeId, value: row.subtypeId, label: row.subtypeName, data: row }))
  } else {
    return arr
  }
}

export async function allRoutetype(convert = false): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/routetype/findAll', {}, { appKey: 'hip' })
  if (convert) {
    return arr.map(row => ({ code: row.routeCode, id: row.routeId, value: row.routeId, label: row.routeName, data: row }))
  } else {
    return arr
  }
}

// 需库存管理的分类
export async function stockReqCats(convert = false): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/cattype/findStockEnabledAll', {}, { appKey: 'hip' })
  if (convert) {
    return arr.map(row => ({ code: row.catCode, id: row.catTypeId, value: row.catTypeId, label: row.catName, data: row }))
  } else {
    return arr
  }
}

export async function allFeeTypes(convert = false): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/feetype/findAll', {}, { appKey: 'hip' })
  if (convert) {
    return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.feeTypeCode, id: row.feTypeId, value: row.feeTypeId, label: row.feeTypeName, data: row }))
  } else {
    return arr
  }
}

export async function allRelationships(convert = false): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/relationship/findAll', {}, { appKey: 'hip' })
  if (convert) {
    return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.relationshipCode, id: row.relationshipId, value: row.relationshipId, label: row.relationshipName, data: row }))
  } else {
    return arr
  }
}

export async function allOrgs(convert = false): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/org/findAll', {}, { appKey: 'hip' })
  if (convert) {
    return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.orgCode, id: row.orgId, value: row.orgId, label: row.orgName, data: row }))
  } else {
    return arr
  }
}
export function findDeptClinicianLsApi(params: any) {
  return http.post('/hip-base/clinician/findDeptLsByClinician', params, { appKey: 'hip' })
}

export function findSectionNurseLsApi(params: any) {
  return http.post('/hip-base/nurseSection/listBySectionId', params, { appKey: 'hip' })
}

export function mdiBasicDataApi(params: any) {
  return http.post('/hip-base/basicdata/findAll', params, { appKey: 'hip' })
}

export type HipType = 'PaymentType' | 'InsuranceType' | 'MedicalType' | 'MiPaymentMethod' | 'FeeCat' | 'FeeType' | 'Org'

const map = {
  PaymentType: () => allPaymentTypes(true),
  InsuranceType: () => allInsuranceTypes(true),
  MedicalType: () => allMedicalTypes(true),
  MiPaymentMethod: () => allMiPaymentMethods(true),
  FeeCat: () => allFeeCats(true),
  FeeType: () => allFeeTypes(true),
  Org: () => allOrgs(true),
}

export function listDictsByHipType(type: HipType) {
  return map[type]
}
