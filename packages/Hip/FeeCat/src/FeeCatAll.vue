<script setup lang="ts">
import { Api, Data } from '@idmy/core'
import { allFeeCats } from '@mh-hip/util'
import { Table } from 'ant-design-vue'

const columns: Data[] = [
  {
    align: 'center',
    customRender: ({ index }: Data) => index + 1,
    dataIndex: 'no',
    fixed: 'left',
    title: '#',
    width: 45,
  },
  {
    align: 'center',
    dataIndex: 'feeCatId',
    title: '发票费用类别代号',
  },
  {
    align: 'center',
    dataIndex: 'feeCatCode',
    title: '发票费用类别编码',
  },
  {
    align: 'center',
    dataIndex: 'feeCatName',
    title: '发票费用类别',
  },
]
</script>

<template>
  <Api :load="() => allFeeCats()" v-slot="{ output, loading }" type="Array">
    <Table rowKey="feeCatId" :columns="columns" :dataSource="output as any[]" :loading="loading" :pagination="false">
      <template #bodyCell="{ column: col, record: row }"> </template>
    </Table>
  </Api>
</template>

<style scoped lang="less"></style>
