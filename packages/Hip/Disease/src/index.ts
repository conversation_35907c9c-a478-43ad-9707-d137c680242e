import { App } from 'vue'
import DiseaseSelector from './DiseaseSelector.vue'
import DiseaseSelectorModal from './DiseaseSelectorModal.vue'
import DiseaseDict from './DiseaseDict.vue'

export { DiseaseSelector, DiseaseSelectorModal, DiseaseDict }

/**
 * 选择病种的工厂函数
 * @param options 配置选项
 * @returns 返回适合的病种选择组件
 */
export function useDiseaseSelector(options: {
  // 模式: 'modal' - 弹窗模式, 'selector' - 页面嵌入模式, 'dict' - 字典选择模式
  mode?: 'modal' | 'selector' | 'dict',
  // 其他配置项，比如patientId等
  [key: string]: any
}) {
  const { mode = 'dict', ...props } = options

  // 根据模式选择组件
  switch (mode) {
    case 'modal':
      return {
        component: DiseaseSelectorModal,
        props
      }
    case 'selector':
      return {
        component: DiseaseSelector,
        props
      }
    case 'dict':
    default:
      return {
        component: DiseaseDict,
        props
      }
  }
}

/**
 * 注册所有组件
 * @param app Vue应用实例
 */
export function install(app: App) {
  app.component('DiseaseSelector', DiseaseSelector)
  app.component('DiseaseSelectorModal', DiseaseSelectorModal)
  app.component('DiseaseDict', DiseaseDict)
}

export default {
  install
}
