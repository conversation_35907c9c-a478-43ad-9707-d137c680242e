<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { getPatientDiseases, getDiseasePage, getDictData } from '@mh-hip/util'
import { Table, Tabs, Radio, Form, FormItem, Input, Button } from 'ant-design-vue'
import type { PaginationProps } from 'ant-design-vue'

const props = defineProps({
  // 弹窗模式
  isModal: {
    type: Boolean,
    default: false
  },
  // 患者ID
  patientId: {
    type: Number,
    default: undefined
  },
  // 当前选择的病种
  modelValue: {
    type: [Number, String],
    default: undefined
  }
})

// 向父组件发送选择的值
const emit = defineEmits(['update:modelValue', 'select'])

// 已选择的病种
const selectedDiseaseCode = ref(props.modelValue)

// 视图类型：个人/全部
const viewType = ref('personal')

// 加载状态
const loading = ref(false)

// 病种类别
const diseaseCats = ref<any[]>([])
// 当前选择的病种类别
const activeDiseaseCatId = ref<number>()

const keyword  = ref<string>('')

// 病种列表
const diseases = ref<any[]>([])

// 表格列定义
const columns = [
  {
    title: '病种编码',
    dataIndex: 'diseaseCode',
    key: 'diseaseCode',
    width: 120
  },
  {
    title: '病种名称',
    dataIndex: 'diseaseName',
    key: 'diseaseName',
    width: 200
  },
  {
    title: '起始日期',
    dataIndex: 'dateStarted',
    key: 'dateStarted',
    align: 'center',
    width: 120
  },
  {
    title: '终止日期',
    dataIndex: 'dateEnded',
    key: 'dateEnded',
    align: 'center',
    width: 120
  }
]

// 监听modelValue变化
watch(
  () => props.modelValue,
  val => {
    selectedDiseaseCode.value = val
  }
)

// 加载病种类别
const loadDiseaseCats = async () => {
  try {
    loading.value = true
    const data = await getDictData('mdiBasicData', { dataTypeLs: ['diseaseCat'] })
    diseaseCats.value = data['diseaseCat']
    if (diseaseCats.value && diseaseCats.value.length > 0) {
      activeDiseaseCatId.value = diseaseCats.value[0].diseaseCatId
      await loadDiseases(diseaseCats.value[0].diseaseCatId)
    }
  } catch (error) {
    console.error('加载病种类别失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载病种列表
const loadDiseases = async (diseaseCatId: number) => {
  try {
    loading.value = true
    const params = {
      S_EQ_t_mi_disease__Disease_Cat_ID: diseaseCatId,
      S_LIKE_t_mi_disease__Disease_Name: keyword.value,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }
    const data: any = await getDiseasePage(params)
    const list = data.list ?? []
    if (list && list.length > 0) {
      list.forEach(item => {
        item.diseaseCode = item.miCode
      })
    }
    diseases.value = list
    pagination.total = data.total ?? 0
  } catch (error) {
    console.error('加载病种列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载患者病种
const loadPatientDiseases = async () => {
  if (!props.patientId) {
    diseases.value = []
    return
  }

  try {
    loading.value = true
    const data: any = await getPatientDiseases({ patientId: props.patientId })
    diseases.value = data ?? []
  } catch (error) {
    console.error('加载患者病种失败:', error)
    diseases.value = []
  } finally {
    loading.value = false
  }
}

// 切换病种类别
const handleTabChange = async (key: string) => {
  const diseaseCatId = Number(key)
  activeDiseaseCatId.value = diseaseCatId
  await loadDiseases(diseaseCatId)
}

// 切换视图类型
const handleViewTypeChange = (e: any) => {
  viewType.value = e.target.value
  selectedDiseaseCode.value = ''
  if (viewType.value === 'personal') {
    loadPatientDiseases()
  } else {
    loadDiseaseCats()
  }
  handleSelect({})
}

// 选择病种
const handleSelect = (record: any) => {
  selectedDiseaseCode.value = record.diseaseCode
  emit('update:modelValue', record.diseaseCode)
  emit('select', record)
}

const rowSelection = computed(() => {
  return {
    type: 'radio',
    selectedRowKeys: selectedDiseaseCode.value ? [selectedDiseaseCode.value] : [],
    onChange: (selectedRowKeys: any) => {
      if (selectedRowKeys.length > 0) {
        selectedDiseaseCode.value = selectedRowKeys[0]
        const record = diseases.value.find(item => item.diseaseCode === selectedRowKeys[0])
        if (record) {
          handleSelect(record)
        }
      } else {
        selectedDiseaseCode.value = ''
        handleSelect({})
      }
    }
  }
})

const customRow = (record: any) => {
  return {
    onClick: () => {
      handleSelect(record)
    }
  }
}

const pagination = reactive<PaginationProps>({
  pageSize: 10,
  current: 1,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: total => `共：${total} 条`,
  onChange(current, pageSize) {
    pagination.pageSize = pageSize
    pagination.current = current
    loadDiseases(activeDiseaseCatId.value)
  },
})

// 初始化
onMounted(async () => {
  if (viewType.value === 'personal') {
    await loadPatientDiseases()
  } else {
    await loadDiseaseCats()
  }
})

// 暴露方法给父组件
defineExpose({
  loadDiseaseCats,
  loadDiseases,
  loadPatientDiseases
})
</script>

<template>
  <div class="disease-selector">
    <!-- 视图类型选择 -->
    <div class="view-type-selector">
      <Radio.Group v-model:value="viewType" button-style="solid" @change="handleViewTypeChange">
        <Radio.Button value="personal">个人病种</Radio.Button>
        <Radio.Button value="all">全部病种</Radio.Button>
      </Radio.Group>
      <Form v-if="viewType === 'all'" layout="inline" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" style="margin-top: 5px">
        <FormItem label="病种名称">
          <Input v-model:value="keyword" placeholder="请输入病种编码/名称查询" allowClear />
        </FormItem>
        <FormItem>
          <Button type="primary" @click="loadDiseases(activeDiseaseCatId)">查询</Button>
        </FormItem>
      </Form>
    </div>

    <!-- 个人病种视图 -->
    <div v-if="viewType === 'personal'" class="personal-view">
      <Table
        :dataSource="diseases"
        :columns="columns"
        :loading="loading"
        :rowKey="(record: any) => record.diseaseCode"
        :row-selection="rowSelection"
        :custom-row="customRow"
        :pagination="null"
      />
    </div>

    <!-- 全部病种视图 -->
    <div v-else class="all-view">
      <Tabs v-if="diseaseCats.length > 0" v-model:activeKey="activeDiseaseCatId" @change="handleTabChange">
        <Tabs.TabPane v-for="cat in diseaseCats" :key="cat.diseaseCatId" :tab="cat.diseaseCatName">
          <Table
            :dataSource="diseases"
            :columns="columns"
            :loading="loading"
            :rowKey="(record: any) => record.diseaseCode"
            :row-selection="rowSelection"
            :custom-row="customRow"
            :pagination="pagination"
            :scroll="{ y: '200px' }"
          />
        </Tabs.TabPane>
      </Tabs>
    </div>
  </div>
</template>

<style lang="less" scoped>
.disease-selector {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.view-type-selector {
  margin-bottom: 16px;
}

.personal-view, .all-view {
  flex: 1;
  overflow: auto;
}
</style>
