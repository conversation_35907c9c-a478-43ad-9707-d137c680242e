<script setup lang="ts">
import { Api, Data } from '@idmy/core'
import { allFeeTypes } from '@mh-hip/util'
import { Table } from 'ant-design-vue'

const columns: Data[] = [
  {
    align: 'center',
    customRender: ({ index }: Data) => index + 1,
    dataIndex: 'no',
    fixed: 'left',
    title: '#',
    width: 45,
  },
  {
    align: 'center',
    dataIndex: 'feeTypeId',
    title: '费用类别代号',
  },
  {
    align: 'center',
    dataIndex: 'feeTypeCode',
    title: '费用类别编码',
  },
  {
    align: 'center',
    dataIndex: 'feeTypeName',
    title: '费用类别名称',
  },
]
</script>

<template>
  <Api :load="() => allFeeTypes()" v-slot="{ output, loading }" type="Array">
    <Table rowKey="feeTypeId" :columns="columns" :dataSource="output as any[]" :loading="loading" :pagination="false">
      <template #bodyCell="{ column: col, record: row }"> </template>
    </Table>
  </Api>
</template>
