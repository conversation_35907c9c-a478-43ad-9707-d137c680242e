# Org组件

组织机构相关组件，包括：

1. OrgSelect：组织机构选择组件，支持选择所有组织机构或仅当前用户有权限的组织机构
2. OrgDept：部门选择组件，支持选择指定组织机构下的所有部门
3. OrgDoctor：医生选择组件，支持选择指定组织机构下的所有医生

## 特性

- 基于ant-design-vue的Select组件实现
- 支持单选和多选模式
- 支持显示所有组织机构或仅显示当前用户有权限的组织机构
- 支持搜索过滤
- 当own属性变化时自动刷新数据

## 依赖

- `ant-design-vue`: UI组件库

## 安装

```bash
# 安装组件
pnpm publish:component Hip/Org
```

### package.json依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-hip/org": "^1.0.8"
  }
}
```

## 使用方法

### 引入组件

```javascript
import { OrgSelect, OrgDept, OrgDoctor } from '@mh-hip/org'
```

### OrgSelect 基本用法

```vue
<template>
  <!-- 基础用法 - 显示所有组织机构 -->
  <OrgSelect v-model="orgId" style="width: 200px" />

  <!-- 仅显示当前用户有权限的组织机构 -->
  <OrgSelect v-model="userOrgId" :own="true" style="width: 200px" />

  <!-- 多选模式 -->
  <OrgSelect v-model="orgIds" multiple />
</template>

<script setup>
import { OrgSelect } from '@mh-hip/org'
import { ref } from 'vue'

// 单选值
const orgId = ref()
const userOrgId = ref()

// 多选值
const orgIds = ref([])
</script>
```

### OrgDept 基本用法

```vue
<template>
  <!-- 基础用法 - 选择指定组织机构下的部门 -->
  <OrgDept v-model="deptId" :orgId="orgId" style="width: 200px" />

  <!-- 不指定组织机构ID，使用当前用户token中的orgId -->
  <OrgDept v-model="currentUserDeptId" style="width: 200px" />

  <!-- 多选模式 -->
  <OrgDept v-model="deptIds" :orgId="orgId" multiple />
</template>

<script setup>
import { OrgDept } from '@mh-hip/org'
import { ref } from 'vue'

// 组织机构ID
const orgId = ref(1)

// 单选值
const deptId = ref()
const currentUserDeptId = ref()

// 多选值
const deptIds = ref([])
</script>
```

### OrgDoctor 基本用法

```vue
<template>
  <!-- 基础用法 - 选择指定组织机构下的医生 -->
  <OrgDoctor v-model="doctorId" :orgId="orgId" style="width: 200px" />

  <!-- 不指定组织机构ID，使用当前用户token中的orgId -->
  <OrgDoctor v-model="currentUserDoctorId" style="width: 200px" />

  <!-- 多选模式 -->
  <OrgDoctor v-model="doctorIds" :orgId="orgId" multiple />
</template>

<script setup>
import { OrgDoctor } from '@mh-hip/org'
import { ref } from 'vue'

// 组织机构ID
const orgId = ref(1)

// 单选值
const doctorId = ref()
const currentUserDoctorId = ref()

// 多选值
const doctorIds = ref([])
</script>
```

## 组件属性

### OrgSelect 属性

| 属性名 | 类型 | 默认值 | 说明 |
| ----- | ---- | ----- | ---- |
| v-model | Number/Array | - | 绑定值，多选时为数组 |
| own | Boolean | false | 是否仅显示当前用户有权限的组织机构 |
| multiple | Boolean | false | 是否多选 |

### OrgDept 属性

| 属性名 | 类型 | 默认值 | 说明 |
| ----- | ---- | ----- | ---- |
| v-model | Number/Array | - | 绑定值，多选时为数组 |
| orgId | Number | undefined | 组织机构ID，不传时使用当前用户token中的orgId |
| clinicianId | Number | undefined | 医生ID，传入时查询该医生对应的部门列表 |
| own | Boolean | false | 是否只显示当前用户的部门，为true时会获取当前用户的医师信息 |
| multiple | Boolean | false | 是否多选 |

### OrgDoctor 属性

| 属性名 | 类型 | 默认值 | 说明 |
| ----- | ---- | ----- | ---- |
| v-model | Number/Array | - | 绑定值，多选时为数组 |
| orgId | Number | undefined | 组织机构ID，不传时使用当前用户token中的orgId |
| multiple | Boolean | false | 是否多选 |
| showCode | Boolean | true | 是否在医生名称后显示医师编码，在医保贯标场景下建议开启，便于识别医师是否已维护贯标编码 |

## API说明

### OrgSelect 组件

组件内部使用了两个不同的API：

1. 当`own=false`时，调用`/hip-base/org/findAll`获取所有组织机构
2. 当`own=true`时，调用`/eam/user/org/list`获取当前用户有权限的组织机构

当`own`属性值发生变化时，组件会自动重新加载对应的组织机构列表。

### OrgDept 组件

组件内部使用API：

- 当`own`属性为true时：
  - 先调用`/microhis-hsd/clinician/clinicianInfo`获取当前用户的医师信息
  - 然后调用`/hip-base/clinician/findDeptLsByClinician`获取该医师对应的部门列表
  - 如果同时传入了`clinicianId`属性，该属性会被忽略，并在控制台打印警告
  - 如果提供了`orgId`属性，则传入该参数
- 当`own`属性为false且`clinicianId`属性不为空时：
  - 调用`/hip-base/clinician/findDeptLsByClinician`获取该医生对应的部门列表
  - 必须传入`clinicianId`参数
  - 如果同时提供了`orgId`属性，也会传入该参数
- 当`own`属性为false且`clinicianId`属性为空时：
  - 调用`/hip-base/orgdept/findAll`获取部门列表
  - 如果提供了`orgId`属性，则传入该参数
  - 如果没有提供`orgId`属性，则API会使用当前用户token中的orgId
- 使用`deptCode`作为部门ID
- 在显示内容中展示部门名称和部门代码，格式为：`部门名称 (部门代码)`

当`orgId`、`clinicianId`或`own`属性值发生变化时，组件会自动重新加载对应的部门列表。

### OrgDoctor 组件

组件内部使用API：

- 调用`/hip-base/clinician/findDoctorListByOrgId`获取医生列表
- 如果提供了`orgId`属性，则传入该参数
- 如果没有提供`orgId`属性，则API会使用当前用户token中的orgId
- 使用`clinicianId`作为医生ID
- 当`showCode=true`时，在显示内容中展示医生名称和医师编码，格式为：`医生名称 [医师编码]`
- 当`showCode=false`时，仅显示医生名称
- 在医保贯标场景下建议开启`showCode`，便于识别医师是否已维护贯标编码

当`orgId`属性值发生变化时，组件会自动重新加载对应的医生列表。

## 示例页面

访问`/hip/Org/all`路径可以查看所有组件的示例和使用方法。
