# KeyMap 组件开发计划

本文档基于 `DESIGN.md` 中的详细设计，提供了 KeyMap 组件的开发工作内容拆解和实现步骤，用于指导后续开发工作。

## 1. 组件结构搭建

### 1.1 创建基础文件结构
```bash
packages/Base/KeyMap/
├── index.ts                # 导出组件和类型
├── package.json            # 包配置
├── README.md               # 使用文档
└── src/
    ├── index.ts            # 组件入口
    └── index.vue           # 组件实现
```

### 1.2 定义数据模型
```typescript
// 在index.ts中定义并导出
export interface KeyMapItem {
  btnKey: string;    // 功能键名（唯一标识）
  btnDesc: string;   // 功能描述
  bindKey: string;   // 绑定的快捷键
}
```

### 1.3 配置package.json
```json
{
  "name": "@mh-base/keymap",
  "version": "1.0.0",
  "description": "键盘快捷键管理组件",
  "main": "dist/index.js",
  "module": "dist/index.esm.js",
  "types": "dist/index.d.ts",
  "dependencies": {
    "vue": "^3.2.0",
    "ant-design-vue": "^3.2.0"
  }
}
```

## 2. 组件基础实现

### 2.1 组件属性和事件定义
```typescript
// props定义
const props = defineProps({
  pageKey: {
    type: String,
    required: true
  },
  functionKeys: {
    type: Array as PropType<KeyMapItem[]>,
    default: () => []
  }
})

// 事件定义
const emit = defineEmits(['update', 'reset'])
```

### 2.2 用户ID获取实现
```typescript
import { Currents } from '../../Util/Auth'

// 获取当前用户ID
const userId = computed(() => Currents.id || 'default')
```

### 2.3 存储键名构建
```typescript
// 构建localStorage存储键名
const storageKey = computed(() => `mh_keymap_${props.pageKey}_${userId.value}`)
```

## 3. 数据管理实现

### 3.1 状态管理
```typescript
// 当前快捷键配置
const keyMapItems = ref<KeyMapItem[]>([])

// 抽屉可见性
const drawerVisible = ref(false)

// 当前正在编辑的项
const editingItem = ref<KeyMapItem | null>(null)
```

### 3.2 配置加载实现
```typescript
// 从localStorage加载配置
const loadKeyMap = () => {
  try {
    const storedData = localStorage.getItem(storageKey.value)
    if (storedData) {
      keyMapItems.value = JSON.parse(storedData)
      return true
    }
  } catch (error) {
    console.error('加载快捷键配置失败:', error)
  }
  return false
}
```

### 3.3 配置存储实现
```typescript
// 保存配置到localStorage
const saveKeyMap = () => {
  try {
    localStorage.setItem(storageKey.value, JSON.stringify(keyMapItems.value))
    emit('update', keyMapItems.value)
    return true
  } catch (error) {
    console.error('保存快捷键配置失败:', error)
    return false
  }
}
```

### 3.4 首次加载初始化
```typescript
// 初始化配置
const initKeyMap = () => {
  // 尝试从localStorage加载
  const loaded = loadKeyMap()

  // 如果没有找到配置，使用传入的functionKeys作为初始数据
  if (!loaded && props.functionKeys.length > 0) {
    keyMapItems.value = [...props.functionKeys]
    saveKeyMap()
  }
}

// 组件挂载时初始化
onMounted(() => {
  initKeyMap()
})
```

## 4. 抽屉界面实现

### 4.1 抽屉组件结构
```vue
<template>
  <Drawer
    title="快捷键设置"
    placement="right"
    :width="400"
    :visible="drawerVisible"
    @close="closeDrawer"
  >
    <Table :dataSource="keyMapItems" :columns="columns">
      <!-- 表格列定义 -->
    </Table>

    <template #footer>
      <div style="text-align: right">
        <Button style="margin-right: 8px" @click="resetToDefault">重置</Button>
        <Button type="primary" @click="closeDrawer">确定</Button>
      </div>
    </template>
  </Drawer>
</template>
```

### 4.2 表格列定义
```typescript
// 表格列定义
const columns = [
  {
    title: '功能',
    dataIndex: 'btnDesc',
    key: 'btnDesc'
  },
  {
    title: '快捷键',
    dataIndex: 'bindKey',
    key: 'bindKey',
    customRender: ({ record }) => {
      return h('div', [
        h('span', record.bindKey),
        h(Button, {
          type: 'link',
          size: 'small',
          onClick: () => startEdit(record)
        }, '修改')
      ])
    }
  }
]
```

### 4.3 抽屉控制方法
```typescript
// 打开抽屉
const openDrawer = () => {
  drawerVisible.value = true
}

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false
  editingItem.value = null
}

// 暴露方法
defineExpose({
  openDrawer,
  closeDrawer,
  getKeyMap: () => keyMapItems.value,
  resetToDefault
})
```

## 5. 快捷键录入实现

### 5.1 快捷键编辑模态框
```vue
<template>
  <Modal
    title="设置快捷键"
    :visible="!!editingItem"
    @cancel="cancelEdit"
    @ok="confirmEdit"
  >
    <div>
      <p>功能: {{ editingItem?.btnDesc }}</p>
      <p>当前快捷键: {{ editingItem?.bindKey }}</p>
      <div class="key-input-container" tabindex="0" @keydown="handleKeyDown">
        <p>请按下新的快捷键组合</p>
        <div class="key-display">{{ newKeyCombo }}</div>
      </div>
    </div>
  </Modal>
</template>
```

### 5.2 快捷键录入逻辑
```typescript
// 当前编辑的新快捷键
const newKeyCombo = ref('')

// 开始编辑
const startEdit = (item: KeyMapItem) => {
  editingItem.value = item
  newKeyCombo.value = ''
}

// 取消编辑
const cancelEdit = () => {
  editingItem.value = null
  newKeyCombo.value = ''
}

// 确认编辑
const confirmEdit = () => {
  if (editingItem.value && newKeyCombo.value) {
    // 检查冲突
    if (checkKeyConflict(newKeyCombo.value)) {
      message.error('快捷键冲突，请重新设置')
      return
    }

    // 更新快捷键
    const index = keyMapItems.value.findIndex(item => item.btnKey === editingItem.value!.btnKey)
    if (index !== -1) {
      keyMapItems.value[index].bindKey = newKeyCombo.value
      saveKeyMap()
      message.success('快捷键设置成功')
    }

    // 关闭编辑
    editingItem.value = null
    newKeyCombo.value = ''
  }
}
```

### 5.3 按键捕获和组合字符串构建
```typescript
// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  // 阻止默认行为
  event.preventDefault()

  // 构建按键组合字符串
  let keyCombo = ''

  // 添加修饰键
  if (event.ctrlKey) keyCombo += 'Ctrl + '
  if (event.altKey) keyCombo += 'Alt + '
  if (event.shiftKey) keyCombo += 'Shift + '
  if (event.metaKey) keyCombo += 'Meta + '

  // 添加主键
  if (event.key === ' ') {
    keyCombo += 'Space'
  } else if (event.key.length === 1) {
    keyCombo += event.key.toUpperCase()
  } else {
    keyCombo += event.key
  }

  // 更新显示
  newKeyCombo.value = keyCombo.trim()
}
```

### 5.4 快捷键冲突检测
```typescript
// 检查快捷键冲突
const checkKeyConflict = (keyCombo: string) => {
  // 排除当前编辑项
  const otherItems = keyMapItems.value.filter(item => item.btnKey !== editingItem.value?.btnKey)

  // 检查是否有冲突
  return otherItems.some(item => item.bindKey === keyCombo)
}
```

## 6. 双击Shift触发实现

### 6.1 Shift键双击检测
```typescript
// 上一次Shift按下的时间
let lastShiftKeyTime = 0

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  // 检测Shift键双击
  if (event.key === 'Shift') {
    const now = Date.now()
    const timeDiff = now - lastShiftKeyTime

    // 如果在300ms内连续按下两次Shift键
    if (timeDiff < 300) {
      // 检查页面可见性
      if (document.visibilityState === 'visible') {
        openDrawer()
      }

      // 重置时间
      lastShiftKeyTime = 0
    } else {
      // 记录时间
      lastShiftKeyTime = now
    }
  }
}
```

### 6.2 页面可见性检测
```typescript
// 检查组件是否在可见页面中
const isVisible = ref(true)

// 监听页面可见性变化
onMounted(() => {
  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown)

  // 添加可见性变化监听
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

// 处理可见性变化
const handleVisibilityChange = () => {
  isVisible.value = document.visibilityState === 'visible'
}

// 组件卸载时移除监听
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})
```

## 7. 重置功能实现

```typescript
// 重置为默认配置
const resetToDefault = () => {
  // 使用传入的functionKeys重置
  keyMapItems.value = [...props.functionKeys]

  // 保存到localStorage
  saveKeyMap()

  // 触发重置事件
  emit('reset')

  // 提示用户
  message.success('已重置为默认配置')
}
```

## 8. 组件方法暴露

```typescript
// 暴露组件方法
defineExpose({
  // 打开抽屉
  openDrawer,

  // 关闭抽屉
  closeDrawer,

  // 获取当前快捷键配置
  getKeyMap: () => keyMapItems.value,

  // 重置为默认配置
  resetToDefault
})
```

## 9. 组件导出

### 9.1 src/index.ts
```typescript
import KeyMap from './index.vue'
export { KeyMapItem } from './types'
export default KeyMap
```

### 9.2 index.ts
```typescript
import KeyMap from './src/index.vue'
export { KeyMapItem } from './src/types'
export default KeyMap
```

## 10. 测试要点

1. 双击Shift触发抽屉显示
2. 快捷键录入和显示
3. 快捷键冲突检测
4. 配置存储和加载
5. 多页面场景处理
6. 用户特定配置

## 11. 注意事项

1. 确保双击Shift触发机制的可靠性
2. 确保快捷键录入的用户体验
3. 确保配置存储和加载的稳定性
4. 确保多页面场景的正确处理
5. 确保组件的性能和兼容性
