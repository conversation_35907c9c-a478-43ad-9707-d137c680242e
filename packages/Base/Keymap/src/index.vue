<template>
  <!-- 快捷键设置抽屉 -->
  <Drawer title="快捷键设置" placement="right" :width="400" :visible="drawerVisible" @close="closeDrawer">
    <Table :dataSource="keyMapItems" :columns="columns" :pagination="false">
      <!-- 表格列在代码中定义 -->
    </Table>

    <template #footer>
      <div style="text-align: right">
        <Button type="primary" @click="closeDrawer">确定</Button>
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, h } from 'vue'
import { Drawer, Table, Button } from 'ant-design-vue'
import type { PropType } from 'vue'
import { Currents } from '@mh-base/core'
import { KeyMapItem } from './types'

// 组件属性定义
const props = defineProps({
  /**
   * 页面标识，用于区分不同页面的快捷键配置
   */
  pageKey: {
    type: String,
    required: true,
  },

  /**
   * 预定义的功能键配置
   */
  functionKeys: {
    type: Array as PropType<KeyMapItem[]>,
    default: () => [],
  },
})

// 组件事件定义
const emit = defineEmits([])

// 获取当前用户ID
const userId = computed(() => Currents.id || 'default')

// 构建localStorage存储键名
const storageKey = computed(() => `mh_keymap_${props.pageKey}_${userId.value}`)

// 当前快捷键配置
const keyMapItems = ref<KeyMapItem[]>([])

// 抽屉可见性
const drawerVisible = ref(false)

// 上一次Shift按下的时间
let lastShiftKeyTime = 0

// 检查组件是否在可见页面中
const isVisible = ref(true)

// 表格列定义
const columns = computed(() => [
  {
    title: '功能',
    dataIndex: 'btnDesc',
    key: 'btnDesc',
    width: '60%',
  },
  {
    title: '快捷键',
    dataIndex: 'bindKey',
    key: 'bindKey',
    width: '40%',
    customRender: ({ record }: { record: KeyMapItem }) => {
      return h('div', [h('span', record.bindKey)])
    },
  },
])

// 初始化配置
const initKeyMap = () => {
  // 直接使用传入的functionKeys作为数据
  if (props.functionKeys.length > 0) {
    console.log(`KeyMap: 初始化配置 - pageKey: ${props.pageKey}, 使用传入的默认配置`)
    keyMapItems.value = JSON.parse(JSON.stringify(props.functionKeys))
  }
}

// 打开抽屉
const openDrawer = () => {
  drawerVisible.value = true
}

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false
}

// 处理全局键盘事件
const handleGlobalKeyDown = (event: KeyboardEvent) => {
  // 如果是在输入框中，不处理快捷键
  if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement || event.target instanceof HTMLSelectElement || (event.target as HTMLElement)?.isContentEditable) {
    return
  }

  // 检测Shift键双击
  if (event.key === 'Shift') {
    const now = Date.now()
    const timeDiff = now - lastShiftKeyTime

    // 如果在300ms内连续按下两次Shift键
    if (timeDiff < 300) {
      // 检查页面可见性和是否可编辑
      if (document.visibilityState === 'visible' && isVisible.value) {
        // 即使不可编辑，也允许打开抽屉查看配置，但无法修改
        openDrawer()
      }

      // 重置时间
      lastShiftKeyTime = 0
    } else {
      // 记录时间
      lastShiftKeyTime = now
    }
  }
}

// 处理可见性变化
const handleVisibilityChange = () => {
  isVisible.value = document.visibilityState === 'visible'
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化配置
  initKeyMap()

  // 添加键盘事件监听
  window.addEventListener('keydown', handleGlobalKeyDown)

  // 添加可见性变化监听
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

// 组件卸载时移除监听
onUnmounted(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})

// 暴露组件方法
defineExpose({
  // 打开抽屉
  openDrawer,

  // 关闭抽屉
  closeDrawer,

  // 获取当前快捷键配置
  getKeyMap: () => keyMapItems.value,
})
</script>

<style scoped>
/* 无需样式 */
</style>
