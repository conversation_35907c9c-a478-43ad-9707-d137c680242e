
<script setup lang="ts">
import * as antDesignVue from 'ant-design-vue'
import { computed } from 'vue'
const { Tag: ATag }  = antDesignVue
import { oeCat, oeStatus, sample } from './tagConfig'
defineOptions({
  name: 'baseTag'
})
const props = defineProps({
  status: {
    type: Number,
    default: null
  },
  // oeStatus
  type: {
    type: String,
    default: 'oeStatus'
  },
  text: {
    type: String,
    default: ''
  }
})
const alltags = {
  oeStatus,
  oeCat,
  sample
}

const rowItem = computed(() => {
  const status: number = Number(props.status)
  if (!alltags[props.type]) return {}
  const row:any = alltags[props.type].find((tag:any) => tag.status === status)
  // 针对待停 做样式优化
  if (props.type === 'oeStatus' && row) {
    row.style = {
      fontSize: status === 3 ? '11px' : '12px',
      paddingInline: status === 3 ? '3px' : '7px'
    }
  }
  return row || null
})

</script>
<template>
  <a-tag v-if="rowItem" :color="rowItem.color" :style="rowItem.style">{{ text || rowItem.label }}</a-tag>
</template>
