# BaseButton 基础按钮组件

BaseButton组件是对ant-design-vue的Button组件的扩展，增加了键盘快捷键功能。

## 特性

- 支持所有ant-design-vue Button组件的属性和事件
- 支持通过functionKey属性指定按钮的快捷键
- 支持单按键（如F5）或组合按键（如Ctrl + S）
- 支持在按钮文本中显示或隐藏快捷键信息
- 当按下对应的快捷键时，自动触发按钮的点击事件
- 智能可见性检测：只有当按钮在视口中可见时才会监听键盘事件，提高性能

## 安装

BaseButton组件是@mh-base/core包的一部分，可以通过以下方式安装：

```bash
# 安装组件
pnpm publish:component Base/Core
```

### package.json依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-base/core": "^1.0.21"
  }
}
```

## 使用方法

### 引入组件

```javascript
import { BaseButton } from '@mh-base/core'
```

### 基本用法

```vue
<template>
  <!-- 基础用法 - 带快捷键的按钮 -->
  <BaseButton type="primary" functionKey="F5" @click="handleRefresh">
    刷新
  </BaseButton>

  <!-- 组合键示例 -->
  <BaseButton type="primary" functionKey="Ctrl + S" @click="handleSave">
    保存
  </BaseButton>
</template>

<script setup>
import { BaseButton } from '@mh-base/core'
import { message } from 'ant-design-vue'

// 处理刷新按钮点击
const handleRefresh = () => {
  message.success('刷新成功')
}

// 处理保存按钮点击
const handleSave = () => {
  message.success('保存成功')
}
</script>
```

### 隐藏快捷键显示

```vue
<template>
  <!-- 显示快捷键 -->
  <BaseButton type="primary" functionKey="F5" @click="handleRefresh">
    刷新
  </BaseButton>

  <!-- 隐藏快捷键 -->
  <BaseButton
    type="primary"
    functionKey="F5"
    :showFunctionKey="false"
    @click="handleRefresh"
  >
    刷新
  </BaseButton>
</template>
```

### 不同类型的按钮

```vue
<template>
  <Space>
    <BaseButton type="primary" functionKey="P" @click="handlePrint">
      打印
    </BaseButton>

    <BaseButton type="default" functionKey="D" @click="handleDefault">
      默认
    </BaseButton>

    <BaseButton type="dashed" functionKey="A" @click="handleDashed">
      虚线
    </BaseButton>

    <BaseButton type="link" functionKey="L" @click="handleLink">
      链接
    </BaseButton>

    <BaseButton type="text" functionKey="T" @click="handleText">
      文本
    </BaseButton>
  </Space>
</template>
```

## 组件属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| functionKey | 绑定的快捷键，支持单按键或组合按键 | string | '' |
| showFunctionKey | 是否在按钮文本中显示快捷键信息 | boolean | true |

除了上述特有属性外，BaseButton组件还支持ant-design-vue Button组件的所有属性，如type、size、disabled等。

## 组件事件

BaseButton组件支持ant-design-vue Button组件的所有事件，如click、mouseenter、mouseleave等。

## 注意事项

1. 当按下与functionKey匹配的按键时，会自动触发按钮的click事件，效果与直接点击按钮相同。
2. 如果按钮处于禁用状态（disabled=true）或加载状态（loading=true），则按下快捷键不会触发点击事件。
3. 当多个按钮绑定相同的快捷键时，可能会导致冲突，请避免在同一页面中为多个按钮绑定相同的快捷键。
4. 组合键使用 "+" 连接，如 "Ctrl + S"、"Alt + F4" 等。
5. 按钮使用Intersection Observer API检测自身是否在视口中可见，只有在可见时才会监听键盘事件，这可以避免在按钮不可见时仍然触发快捷键功能，提高性能和用户体验。
