<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'
import { AutoComplete, Pagination } from 'ant-design-vue'
import { PropType, ref, computed, h } from 'vue'

const modelValue: any = defineModel('value')

defineOptions({
  name: 'baseAutoComplete',
})

const $emit = defineEmits(['onSearch', 'onSelect', 'pageChange', 'onClear'])

const props = defineProps({
  keyColumn: {
    type: String,
    default: 'value',
  },
  columns: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  options: {
    type: Array,
    default: () => [],
  },
  pagination: {
    type: Object,
    default: () => ({}),
  },
})

const onSearch = useDebounceFn((value: string) => $emit('onSearch', value), 600)

const onSelect = (value: string) => {
  const item = props.options.find((item: any) => item[props.keyColumn] === value)
  if (item) {
    $emit('onSelect', item)
  }
}

const onClear = () => {
  $emit('onClear')
}

// 生成符合 ant-design-vue 要求的 options 数组
const computedOptions = computed(() => {
  const result: any[] = []

  // 添加表头
  if (props.options.length > 0) {
    result.push({
      value: '-1',
      disabled: true,
      class: 'custom-art-header',
      label: h('div', { class: 'custom-art-header' },
        props.columns.map(col =>
          h('div', {
            class: 'custom-art-name',
            style: { width: col.width ? col.width + 'px' : '60px' }
          }, col.title)
        )
      )
    })

    // 添加分隔线
    result.push({
      value: '-2',
      disabled: true,
      label: ''
    })
  }

  // 添加数据选项
  props.options.forEach((item: any) => {
    result.push({
      value: item[props.keyColumn],
      label: h('div', { style: 'display: flex;' },
        props.columns.map(col =>
          h('div', {
            class: 'custom-art-name',
            style: { width: col.width ? col.width + 'px' : '60px' }
          }, item[col.dataIndex])
        )
      )
    })
  })

  // 添加分页
  if (props.pagination.pages > 1) {
    result.push({
      value: '-4',
      disabled: true,
      label: h('div', { style: 'height: 50px;' })
    })

    result.push({
      value: '-3',
      disabled: true,
      class: 'custom-art-footer',
      label: h('div', { class: 'text-right flex-auto' },
        h(Pagination, {
          size: 'small',
          current: props.pagination.pageNum,
          pageSize: props.pagination.pageSize,
          total: props.pagination.total,
          showTotal: props.pagination.showTotal,
          showSizeChanger: false,
          onChange: props.pagination.onChange
        })
      )
    })
  }

  return result
})

// 导出 refs
const autoCompleteRef = ref()
defineExpose({
  $refs: () => {
    return autoCompleteRef?.value
  },
})
</script>

<template>
  <AutoComplete
    ref="autoCompleteRef"
    v-model:value="modelValue"
    @select="onSelect"
    @search="onSearch"
    :dropdown-match-select-width="false"
    popupClassName="art-select-container"
    style="min-width: 220px"
    @clear="onClear"
    :options="computedOptions"
  />
</template>
<style lang="less" scoped>
.text-right {
  text-align: right;
}

.flex-auto {
  flex: 1 1 auto;
}
</style>
