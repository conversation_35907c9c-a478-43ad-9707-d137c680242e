<script lang="ts">
import { Data, dayjs, http, Modal, required, setCookie, useLoading } from '@idmy/core'
import { MD5 } from 'crypto-js'
import { reactive, shallowRef } from 'vue'
import { aesEncrypt, getSign } from '../Util/Aes'

interface Token {
  expiresIn: number
  logSeqid: string
  refToken: string
  token: string
  userId: number
  userName: string
  roleLs: string
}

function login({ captcha, key, password, username }: Data): Promise<Token> {
  const timeStamp = dayjs().format('YYYYMMDDHHmmss')
  const loginPwd = aesEncrypt(MD5(password).toString().toUpperCase())
  const loginName = aesEncrypt(username)
  const { sign } = getSign({ captcha, key, loginName, loginPwd, loginType: 0, timeStamp })
  return http.postForm<Token>(
    '/idm/loginByAccount',
    {
      captcha,
      key,
      loginName,
      loginPwd,
      loginType: 0,
      sign,
      timeStamp,
    },
    {
      appKey: 'idm',
      ignoreToken: true,
    }
  )
}
</script>
<script lang="ts" setup>
import { ModalButton } from '@idmy/antd'
import { loginConfig } from '@idmy/core'
import { Form, FormItem, InputPassword, Input } from 'ant-design-vue'

const STORAGE_AUTHORIZE_KEY = 'Authorization'
const STORAGE_REF_AUTHORIZE_KEY = 'RefAuthorization'
const STORAGE_AUTHORIZE_TOKEN_TIME_KEY = 'Authorization_Token_Time'
const STORAGE_AUTHORIZE_REF_TOKEN_TIME_KEY = 'Authorization_RefToken_Time'
const STORAGE_LOG_SEQID = 'logSeqid'

const state = reactive({
  captcha: undefined,
  code: undefined,
  key: undefined, // 图形验证码Key
  mobile: undefined, // 图形验证码值
  password: undefined,
  remember: true,
  type: 'account',
  username: undefined,
})

function set(key: string, val: string) {
  setCookie(key, val)
  localStorage.setItem(key, val)
}

const formRef = shallowRef()

const [onOk, loading] = useLoading(async () => {
  await formRef.value?.validate()
  const { expiresIn, logSeqid, refToken, token } = await login(state)

  localStorage.setItem(STORAGE_AUTHORIZE_KEY, token)
  localStorage.setItem(STORAGE_REF_AUTHORIZE_KEY, refToken)
  localStorage.setItem(STORAGE_LOG_SEQID, logSeqid)

  const tokenTime = dayjs().add(expiresIn, 's').format()
  localStorage.setItem(STORAGE_AUTHORIZE_TOKEN_TIME_KEY, tokenTime)

  const refTokenTime = dayjs()
    .add(expiresIn * 48 * 7, 's')
    .format()
  localStorage.setItem(STORAGE_AUTHORIZE_REF_TOKEN_TIME_KEY, refTokenTime)

  set(loginConfig.tokenName, token)
  set(loginConfig.refreshTokenName, refToken)
  set('tokenTime', tokenTime)
  set('refTokenTime', refTokenTime)
  await Modal.d.ok()
})
</script>

<template>
  <Form ref="formRef" :labelCol="{ lg: { span: 7 }, sm: { span: 7 } }" :model="state" :wrapperCol="{ lg: { span: 15 }, sm: { span: 17 } }">
    <FormItem :rules="required" label="用户名" name="username">
      <Input v-model:value="state.username" />
    </FormItem>
    <FormItem :rules="required" label="密码" name="password">
      <InputPassword v-model:value="state.password" />
    </FormItem>
    <ModalButton :loading="loading" okText="登录" @ok="onOk" />
  </Form>
</template>

<style lang="less" scoped></style>
