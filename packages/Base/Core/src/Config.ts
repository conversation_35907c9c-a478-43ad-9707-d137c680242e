import { appConfig, appendUrlSearch, Data, dayjs, getCookie, http, httpConfig, HttpMethod, HttpRequestConfig, HttpResponseError, login, loginConfig, Modal, removeCookie, setCookie } from '@idmy/core'
import { isNil } from 'lodash-es'
import Login from './Login/index.vue'
import { aesEncrypt, getSign } from './Util/Aes'
import { Currents } from './Util/Auth'

setTimeout(() => {
  console.info(Modal)
}, 3000)

appConfig.dict.cache = false
appConfig.number.currencyUnitValue = 1
appConfig.app.appKeyHeaderName = 'X-Mh-App-Key'
appConfig.app.traceIdHeaderName = 'x-Mh-Trace-Id'
appConfig.app.requestIdHeaderName = 'x-Mh-Request-Id'
loginConfig.tokenIdField = 'userId'
loginConfig.tokenName = 'userToken'
loginConfig.idTokenName = 'userToken'
loginConfig.refreshTokenName = 'refToken'
httpConfig.axios.withCredentials = false
appConfig.page.rows = 'list'

function set(key: string) {
  const val = localStorage.getItem(key)
  if (val) {
    setCookie(key, val)
    return true
  } else {
    return false
  }
}

httpConfig.unauthorizedInterrupt = (content: string): Promise<any> => {
  set('tokenTime')
  set('refTokenTime')
  set(loginConfig.refreshTokenName)
  if (set(loginConfig.tokenName)) {
    location.reload()
  } else {
    return new Promise((resolve: any) => {
      Modal.d.open({
        component: Login,
        xClosable: false,
        width: 2,
        title: '登录',
        onClose: (isOk: boolean) => (isOk ? location.reload() : resolve()),
      })
    })
  }
}

httpConfig.afterResponse = (json, config, response): any => {
  if (response.status === 200) {
    const { msg, code, data } = json
    if (isNil(code) || isNil(msg)) {
      return json
    } else if (code && !isNil(msg)) {
      const e = new HttpResponseError(msg)
      // @ts-ignore
      e.msg = msg
      e.code = code
      e.data = data
      throw e
    } else {
      return data
    }
  } else {
    const { msg, code, data } = json
    const e = new HttpResponseError(msg)
    // @ts-ignore
    e.msg = msg
    e.code = code
    e.data = data
    throw e
  }
}

httpConfig.setHeaders = (config: HttpRequestConfig) => {
  if (!config.headers) return
  if ((appConfig.app.appKeyHeaderName && config.appKey) || appConfig.app.key) {
    config.headers[appConfig.app.appKeyHeaderName] = config.appKey || appConfig.app.key
  }
  const at = login.getToken()
  if (at) {
    config.headers.Authorization = `ph_-1_${at}`
  }
  if (Currents.tenantId) {
    config.headers['X-Mh-Tenant-Id'] = Currents.tenantId
  }
}

httpConfig.beforeRequest = (method: HttpMethod, url: string, params: any, config: HttpRequestConfig, callback) => {
  if ((method === HttpMethod.POST && appConfig.security.encrypt && config.encrypt !== false) || url === '/idm/logout') {
    const data = aesEncrypt(JSON.stringify(params))
    const timeStamp = dayjs().format('YYYYMMDDHHmmss')
    params = getSign({ data, timeStamp })
  }
  return callback(method, url, params, config)
}

loginConfig.getServerRefreshToken = async (refToken: string, config: HttpRequestConfig) => {
  config.appKey = 'idm'
  const timeStamp = dayjs().format('YYYYMMDDHHmmss')
  const params = getSign({ refToken, timeStamp }, { refToken, timeStamp })
  const { token: access_token, refToken: refresh_token } = await http.get<Data>('/idm/refToken', params, config)
  return { access_token, refresh_token, id_token: access_token }
}

loginConfig.isLogin = () => !isNil(getCookie(loginConfig.tokenName))

loginConfig.beforeLogout = () => http.post('/idm/logout', {}, { appKey: 'idm' })

loginConfig.afterLogout = async (options: any): Promise<void> => {
  removeCookie(loginConfig.tokenName)
  const url = encodeURIComponent(options?.url ? options.url : location.href)
  const redirect = appendUrlSearch('redirect', url, loginConfig.loginUrl)
  if (appConfig.env.startsWith('prod')) {
    location.replace(redirect)
  } else {
    location.reload()
  }
}
