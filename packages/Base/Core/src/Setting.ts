import { appConfig, cfg, Data, emitter, http } from '@idmy/core'
import { Currents, TENANT_CHANGE_EVENT } from './Util/Auth'

export const AUTO_SELECT_ORG = Symbol('AUTO_SELECT_ORG')

export async function initOrgSetting() {
  try {
    cfg.setting = await http.post<Data>('/hip-base/orgsetting/infoByUser', {}, { appKey: 'hip', encrypt: true, ignoreError: true })
  } catch (e) {
    if (e.msg?.includes('请选择机构')) {
      emitter.emit(AUTO_SELECT_ORG)
    }
    throw e
  }
}

export async function initTenantCfg() {
  if (appConfig.app.keys.sys) {
    cfg.tenant = await http.post<Data>('/api/sys/cfg/tenant', {}, { encrypt: false, ignoreError: true, appKey: 'sys' })
    console.info('当前租户配置：', cfg.tenant)
  }
}

emitter.on(TENANT_CHANGE_EVENT, () => {
  if (Currents.tenantId) {
    initTenantCfg()
  }
})
