import { dayjs } from '@idmy/core'
import { Modal, RangePicker, Table } from 'ant-design-vue'

Table.props = {
  ...Table.props,
  size: {
    type: String,
    default: 'small',
  },
  bordered: {
    type: Boolean,
    default: true,
  },
}

Modal.props = {
  ...Modal.props,
  okText: {
    type: String,
    default: '确定',
  },
  cancelText: {
    type: String,
    default: '取消',
  },
}

RangePicker.props = {
  ...RangePicker.props,
  placeholder: {
    type: Array,
    default: ['开始日期', '结束日期'],
  },
  presets: {
    type: Array,
    default: [
      {
        label: '今天',
        value: [dayjs().startOf('day'), dayjs().endOf('day')],
      },
      {
        label: '昨天',
        value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')],
      },
      {
        label: '本周',
        value: [dayjs().startOf('week'), dayjs().endOf('week')],
      },
      {
        label: '上周',
        value: [dayjs().subtract(1, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week')],
      },
      {
        label: '本月',
        value: [dayjs().startOf('month'), dayjs().endOf('month')],
      },
      {
        label: '上月',
        value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')],
      },
      {
        label: '最近7天',
        value: [dayjs().subtract(7, 'day').startOf('day'), dayjs().endOf('day')],
      },
      {
        label: '最近30天',
        value: [dayjs().subtract(30, 'day').startOf('day'), dayjs().endOf('day')],
      },
    ],
  },
}
