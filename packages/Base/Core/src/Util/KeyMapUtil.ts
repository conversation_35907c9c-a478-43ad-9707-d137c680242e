import { Currents } from './Auth'

/**
 * 从localStorage中获取指定页面和按钮的快捷键
 * @param pageKey 页面标识
 * @param btnKey 按钮标识
 * @param defaultKey 默认快捷键
 * @returns 快捷键字符串
 */
export const getBindKey = (pageKey: string, btnKey: string, defaultKey: string = ''): string => {
  if (!pageKey || !btnKey) {
    return defaultKey
  }

  try {
    // 获取当前用户ID
    const userId = Currents.id || 'default'
    
    // 构建localStorage存储键名
    const storageKey = `mh_keymap_${pageKey}_${userId}`
    
    // 从localStorage获取配置
    const storedData = localStorage.getItem(storageKey)
    
    if (storedData) {
      const keyMapItems = JSON.parse(storedData)
      
      // 查找匹配的按钮配置
      const matchedItem = keyMapItems.find((item: any) => item.btnKey === btnKey)
      
      if (matchedItem && matchedItem.bindKey) {
        return matchedItem.bindKey
      }
    }
  } catch (error) {
    console.error('获取快捷键配置失败:', error)
  }
  
  // 如果没有找到配置或发生错误，返回默认值
  return defaultKey
}
