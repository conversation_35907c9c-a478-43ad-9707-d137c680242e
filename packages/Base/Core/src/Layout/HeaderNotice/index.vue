<template>
  <Popover :autoAdjustOverflow="true" overlayClassName="notice-popover" placement="bottomRight" trigger="hover">
    <template #content>
      <div class="notice-content">
        <Tabs>
          <TabPane key="notification" tab="通知">
            <div class="notice-list">
              <template v-if="notifications.length">
                <div v-for="item in notifications" :key="item.id" class="notice-item">
                  <NotificationOutlined style="color: #1890ff" />
                  <div class="notice-item-content">
                    <div class="notice-item-title">{{ item.title }}</div>
                    <div class="notice-item-time">{{ item.time }}</div>
                  </div>
                </div>
              </template>
              <Empty v-else description="暂无通知" />
            </div>
          </TabPane>
          <TabPane key="message" tab="消息">
            <div class="notice-list">
              <template v-if="messages.length">
                <div v-for="item in messages" :key="item.id" class="notice-item">
                  <MessageOutlined style="color: #52c41a" />
                  <div class="notice-item-content">
                    <div class="notice-item-title">{{ item.title }}</div>
                    <div class="notice-item-time">{{ item.time }}</div>
                  </div>
                </div>
              </template>
              <Empty v-else description="暂无消息" />
            </div>
          </TabPane>
        </Tabs>
        <div class="notice-footer">
          <div class="notice-clear" @click="clearNotifications">
            <DeleteOutlined />
            <span>清空通知</span>
          </div>
          <a class="notice-viewmore">
            查看更多
            <RightOutlined />
          </a>
        </div>
      </div>
    </template>
    <div class="notice-trigger">
      <Badge :count="noticeCount">
        <BellOutlined style="font-size: 18px; color: rgba(255, 255, 255, 0.85)" />
      </Badge>
    </div>
  </Popover>
</template>

<script lang="ts" setup>
import { BellOutlined, DeleteOutlined, MessageOutlined, NotificationOutlined, RightOutlined } from '@ant-design/icons-vue'
import { Badge, Empty, Popover, TabPane, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

const noticeCount = ref(0)

const notifications = ref([])

const messages = ref([])

const clearNotifications = () => {
  notifications.value = []
  messages.value = []
  noticeCount.value = 0
}
</script>

<style lang="less" scoped>
.notice-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  color: #fff;
  padding: 0 12px;
  height: 100%;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

:deep(.notice-popover) {
  .ant-popover-inner {
    padding: 0;
  }

  .ant-popover-arrow {
    display: none;
  }
}

.notice-content {
  width: 336px;
}

.notice-list {
  max-height: 400px;
  overflow: auto;
}

.notice-item {
  padding: 12px 16px;
  display: flex;
  align-items: flex-start;
  cursor: pointer;

  &:hover {
    background: #f5f5f5;
  }

  .notice-item-content {
    margin-left: 12px;
    flex: 1;

    .notice-item-title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
    }

    .notice-item-time {
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
    }
  }
}

.notice-footer {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgba(0, 0, 0, 0.45);
}

:deep(.ant-empty) {
  margin: 32px 0;
}
</style>
