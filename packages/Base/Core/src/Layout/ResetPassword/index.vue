<script lang="ts">
import { http, Modal, useLoading } from '@idmy/core'
</script>
<script lang="ts" setup>
import { ModalButton } from '@idmy/antd'
import { Form, FormInstance, FormItem, InputPassword } from 'ant-design-vue'
import { Rule } from 'ant-design-vue/es/form'
import { ref, reactive } from 'vue'
import { aesEncrypt } from '../../Util/Aes'

const resetPwd = (oldPwd: string, newPwd: string) => http.post('/idm/resetPwd', { loginPwd: aesEncrypt(oldPwd), newPwd: aesEncrypt(newPwd) }, { appKey: 'idm' })

const formRef = ref<FormInstance>()

const state = reactive({
  oldPwd: undefined,
  password: undefined,
  password2: undefined,
})

async function validatePwd(_: Rule, val: string) {
  if (val === '') {
    return Promise.reject(new Error('请输入新密码'))
  } else {
    if (state.password2 !== '') {
      formRef.value?.validateFields('password2')
    }
    return Promise.resolve()
  }
}

async function validatePwd2(_: Rule, val: string) {
  if (val === '') {
    return Promise.reject(new Error('请再次输入密码!'))
  } else if (val !== state.password) {
    return Promise.reject(new Error('两次密码不一致!'))
  } else {
    return Promise.resolve()
  }
}

const rules: Record<string, Rule[]> = {
  oldPwd: [{ required: true, message: '旧密码错误', trigger: 'change' }],
  password: [{ required: true, validator: validatePwd, trigger: 'change' }],
  password2: [{ required: true, validator: validatePwd2, trigger: 'change' }],
}

const [onOk, loading] = useLoading(async () => {
  const { oldPwd, password } = await formRef.value?.validate()
  await resetPwd(oldPwd, password)
  await Modal.ok()
  formRef.value?.resetFields()
})
</script>

<template>
  <Form ref="formRef" :labelCol="{ lg: { span: 7 }, sm: { span: 7 } }" :model="state" :rules="rules" :wrapperCol="{ lg: { span: 15 }, sm: { span: 17 } }">
    <FormItem label="旧的密码" name="oldPwd">
      <InputPassword v-model:value="state.oldPwd" placeholder="输入旧密码" />
    </FormItem>
    <FormItem label="新的密码" name="password">
      <InputPassword v-model:value="state.password" placeholder="请输入密码" />
    </FormItem>
    <FormItem label="确认密码" name="password2">
      <InputPassword v-model:value="state.password2" placeholder="请再输入一次密码" />
    </FormItem>
    <ModalButton :loading="loading" @ok="onOk" />
  </Form>
</template>
