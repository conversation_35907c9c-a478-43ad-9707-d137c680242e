<script lang="ts">
import { Data, emitter, http, required, setCookie, useLoading } from '@idmy/core'
</script>
<script lang="ts" setup>
import { AUTO_SELECT_ORG, Currents } from '../../../'
import { Form, FormInstance, FormItem, Modal, Select, SelectOption, Button } from 'ant-design-vue'
import { ref, reactive } from 'vue'

interface Org {
  orgId: number
  orgName: string
}

const listCurrentOrgs = (): Promise<Data[]> => http.post<Data[]>('/eam/user/org/list', {}, { appKey: 'eam' })
const changeOrg = (orgId: number): Promise<void> => http.post<void>('/eam/user/chooseOrg', { orgId }, { appKey: 'eam' })

const orgs = ref([])
useLoading(async () => {
  orgs.value = await listCurrentOrgs()
}, true)

const onChangeOrg = async (org: Org) => {
  await changeOrg(org.orgId)
  setCookie('orgId', org.orgId)
  Currents.tenantId = org.orgId
  Currents.tenantName = org.orgName
}

emitter.on(AUTO_SELECT_ORG, () => {
  const first = orgs.value?.[0]
  first && onChangeOrg(first)
})

const visible = ref(false)
const formRef = ref<FormInstance>()
const state = reactive<Org>({
  orgId: undefined,
} as Org)

watchEffect(() => {
  state.orgId = Currents.tenantId
})

const [ok] = useLoading(async () => {
  const { orgId } = await formRef.value?.validateFields()
  const org = orgs.value.find(row => row.orgId === orgId)
  if (org) {
    await onChangeOrg(org)
    visible.value = false
    location.reload()
  }
})

const handleFilter = (val, row) => val && row && row.label.includes(val)
</script>
<template>
  <Button class="m-x-16px" shape="round" type="primary" @click="visible = true" style="background: var(--primary-color-7)">
    <div flex items-center>
      <img alt="" src="./icon_org.svg" />
      <span m-x-4px>{{ Currents.tenantName || '请选择机构' }}</span>
      <img alt="" src="./icon_down.svg" />
    </div>
  </Button>
  <Modal v-model:open="visible" :closable="false" title="机构切换" @ok="ok">
    <Form ref="formRef" :model="state" mt-24px>
      <FormItem :rules="required" name="orgId">
        <Select v-model:value="state.orgId" :allowClear="false" :filterOption="handleFilter" placeholder="请选择一个机构" showSearch>
          <SelectOption v-for="row in orgs" :key="row.orgId" :label="row.orgName + row.orgId" :value="row.orgId"> {{ row.orgName }}「{{ row.orgId }}」</SelectOption>
        </Select>
      </FormItem>
    </Form>
  </Modal>
</template>
