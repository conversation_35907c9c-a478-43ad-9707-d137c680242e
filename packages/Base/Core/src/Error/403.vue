<script lang="ts" setup>
import { Dialog, login, useLoading } from '@idmy/core'
import { Button, Result, Space } from 'ant-design-vue'
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'

const url = computed(() => {
  return `${window.location.protocol}//${window.location.host}`
})

const route = useRoute()
const name = ref('')
useLoading(async () => {}, <PERSON><PERSON><PERSON>(route.query.role))

const onApply = () => Dialog.info({ title: '去申请权限', content: '该功能还未开发……' })
</script>

<template>
  <Result status="403" subTitle="对不起，您没有访问该资源的权限">
    <template v-if="name" #title>
      没有【
      <a @click="onApply()">{{ name }}</a>
      】权限
    </template>
    <template #extra>
      <Space>
        <Button @click="$router.back()">返回</Button>
        <Button type="primary" @click="login.logout({ url })">重新登录</Button>
      </Space>
    </template>
  </Result>
</template>
