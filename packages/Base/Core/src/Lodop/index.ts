import { Data, Message, sleep, useCache } from '@idmy/core'
import { cloneDeep, isArray } from 'lodash-es'

import { getLodop } from './helper'
import './LodopFuncs'
import { getPrintTemplate } from './services'
import { dateFormat, getNestedValue, htmlTempTohtml, imageTempTohtml, isValidIP, strTempToValue, tableTempTohtml } from './tools.js'


export class NonPrinterError extends Error {}

export class LodopPrintError extends Error {}

export interface LodopOptions {
  mode: 'print' | 'design' | 'preview' | 'setup'
}

let LODOP: any = null

function processPrintResult(resolve: any, reject: any) {
  LODOP.On_Return = (_: any, value: string) => {
    const result = value === '0' || value === ''
    if (result) {
      reject(new LodopPrintError())
    } else {
      resolve()
    }
  }
}

export const printerSetting = useCache<any>('printerSetting', {}, -1, { never: true, global: true })

export async function lodapPrint(code: string, data: any, options: LodopOptions = { mode: 'print' }) {
  if (!options.mode) {
    options.mode = 'print'
  }
  if (!isArray(data)) {
    data = [data]
  }
  const template = await getPrintTemplate(code)
  if (!template.title) {
    throw new Error('没有模板')
  }
  if (options.mode === 'print') {
    if (!printerSetting.value[template.title]) {
      Message.info('静默第一次打印，稍后请点击预览界面【设置】，然后选择对应的打印机和配置进行打印。')
      await sleep(3000)
      options.mode = 'preview'
    }
    // @ts-ignore
  } else if (window.lodopMode) {
    // @ts-ignore
    options.mode = window.lodopMode
  }
  console.info(options)
  try {
    await lodapPrint0(template, data, options)
    printerSetting.value[template.title] = true
  } catch (e) {
    if (e instanceof LodopPrintError) {
      throw e
    } else {
      Message.error(e.message)
    }
  }
}

async function lodapPrint0(template: any, data: any[], options: LodopOptions) {
  // eslint-disable-next-line consistent-return
  return new Promise(async (resolve, reject) => {
    if (!LODOP) {
      LODOP = await getLodop()
    }
    // @ts-ignore
    if (window.CLODOP.blOneByone) {
      return reject(new Error('预览窗口已存在。如长时间没弹窗，请检查打印机链接是否正常'))
    }

    LODOP.PRINT_INITA(template.top, template.left, template.width, template.height, template.title)
    LODOP.SET_PRINT_PAGESIZE(template.intOrient, `${template.pageWidth ?? 0}mm`, `${template.pageHeight.pageHeight ?? 0}mm`, '')
    LODOP.SET_SHOW_MODE('LANDSCAPE_DEFROTATED', 1) // 横向打印的预览默认旋转90度（正向显示）

    const tempItems = JSON.parse(template.tempItems)
    const printContent = _TempParser(tempItems, data)
    if (printContent.length > 1) {
      // 打印多份
      printContent.forEach((aPrint, index) => {
        LODOP.NewPageA()
        aPrint.forEach(printItem => {
          _AddPrintItem(LODOP, printItem, index)
        })
      })
    } else {
      // 单份
      printContent[0].forEach(printItem => {
        _AddPrintItem(LODOP, printItem)
      })
    }

    if (options.mode === 'print') {
      processPrintResult(resolve, reject)
      LODOP.PRINT()
    } else if (options.mode === 'setup') {
      LODOP.PRINT_SETUP()
    } else if (options.mode === 'design') {
      LODOP.PRINT_DESIGN()
    } else {
      processPrintResult(resolve, reject)
      LODOP.PREVIEW()
    }
  })
}

/**
 * 获取打印机列表
 */
async function getPrinters(ip) {
  if (ip && !isValidIP(ip)) {
    throw new Error('IP地址格式错误！')
  }
  const LODOP = await getLodop()
  return LODOP.Printers
}

/**
 * 解析模板和数据生成打印项
 * @param {*Array} tempItem 模板打赢项
 * @param {Array} data 打印数据,
 * @return {Array} 若data为null则返回处理后的模板
 */
function _TempParser(tempItem, data) {
  const temp = cloneDeep(tempItem)
  // 修改模板打印项顺序
  // 将自适应高度的打印项（item.style.AutoHeight == true）放在第一项
  const flag = temp.findIndex(item => item.style.AutoHeight)
  if (flag !== -1) {
    const autoItem = temp[flag]
    temp.splice(flag, 1)
    temp.unshift(autoItem)
    // 处理位于自适应打印项下方的打印项
    temp.forEach(item => {
      if (item.name === '_getNowTime') {
        item.value = dateFormat('yyyy-MM-dd hh:mm:ss')
      }
      // 位于自适应高度项下的打印项修改top、left,并添加关联属性（style.LinkedItem）
      if (item.top > autoItem.top && item.style.ItemType === 0) {
        item.top = item.top - autoItem.top - autoItem.height
        item.left -= autoItem.left
        item.style.LinkedItem = 1
      }
    })
  }
  if (data && data.length > 0) {
    // 解析打印模板和数据，生成生成打印内容
    const tempContent = []
    data.forEach(dataItem => {
      const conItem = temp.map(tempItem => {
        const item = cloneDeep(tempItem)
        if (item.name === '_getNowTime') {
          item.value = dateFormat('yyyy-MM-dd hh:mm:ss')
        } else if (item.name) {
          item.defaultValue = getNestedValue(dataItem, item.name)
          item.value = strTempToValue(item.value, item.defaultValue)
        }
        return item
      })
      tempContent.push(conItem)
    })
    return tempContent
  } else {
    return [temp]
  }
}

/**
 * 添加打印项
 * @param {lodop} LODOP 打印实例
 * @param {Object} printItem 打印项内容
 * @param {Number} pageIndex 当前打印页的开始序号
 */
function _AddPrintItem(LODOP, tempItem, pageIndex = 0) {
  const printItem = cloneDeep(tempItem)
  const lodopStyle = _createLodopStyle(printItem.style)

  // 批量打印时，修改关联打印项的关联序号
  if (lodopStyle.LinkedItem === 1) {
    lodopStyle.LinkedItem = 1 + pageIndex
  }
  // 添加打印项
  switch (printItem.type) {
    case 'vertical-line':
      LODOP.ADD_PRINT_LINE(printItem.top, printItem.left, printItem.top + printItem.height, printItem.left, printItem.style.lineType, printItem.style.FontSize)
      break
    case 'horizontal-line':
      LODOP.ADD_PRINT_LINE(printItem.top, printItem.left, printItem.top, printItem.width + printItem.left, printItem.style.lineType, printItem.style.FontSize)
      break
    case 'braid-txt':
      LODOP.ADD_PRINT_TEXT(printItem.top, printItem.left, printItem.width, printItem.height, printItem.value)
      break
    case 'bar-code':
      LODOP.ADD_PRINT_BARCODE(printItem.top, printItem.left, printItem.width, printItem.height, lodopStyle.codeType, printItem.value)
      break
    case 'braid-html':
      {
        const html = htmlTempTohtml(printItem.defaultValue, printItem.style)
        if (lodopStyle.AutoHeight) {
          LODOP.ADD_PRINT_HTM(printItem.top, printItem.left, printItem.width, `BottomMargin:${lodopStyle.BottomMargin}mm`, html)
        } else {
          LODOP.ADD_PRINT_HTM(printItem.top, printItem.left, printItem.width, printItem.height, html)
        }
      }
      break
    case 'braid-table':
      {
        const html = tableTempTohtml(printItem.columns ? printItem.columns : [], printItem.defaultValue, printItem.style, printItem)
        if (lodopStyle.AutoHeight) {
          LODOP.ADD_PRINT_TABLE(printItem.top, printItem.left, printItem.width, `BottomMargin:${lodopStyle.BottomMargin}mm`, html)
        } else {
          LODOP.ADD_PRINT_TABLE(printItem.top, printItem.left, printItem.width, printItem.height, html)
        }
      }
      break
    case 'braid-image':
      {
        const html = imageTempTohtml(printItem.value)
        LODOP.ADD_PRINT_IMAGE(printItem.top, printItem.left, printItem.width, printItem.height, html)
      }
      break
    default:
  }
  // 设置打印项样式
  Object.keys(lodopStyle).forEach(key => {
    LODOP.SET_PRINT_STYLEA(0, key, lodopStyle[key])
  })
  // 设置默认LodopStyle
  const defaultLodopStyle = printItem.lodopStyle
  if (defaultLodopStyle) {
    Object.keys(defaultLodopStyle).forEach(key => {
      LODOP.SET_PRINT_STYLEA(0, key, defaultLodopStyle[key])
    })
  }
}

/**
 * 将模板设计样式转换为lodop样式
 * @param style 模板样式
 * @returns lodop样式对象
 */
function _createLodopStyle(style): Data {
  const lodopStyle = {
    zIndex: style.zIndex,
  }

  for (const key in style) {
    if (['Bold', 'Italic', 'Underline', 'ShowBarText'].indexOf(key) > -1) {
      lodopStyle[key] = style[key] ? 1 : 0
    } else if (key === 'Alignment') {
      lodopStyle[key] = style[key] === 'left' ? 1 : style[key] === 'center' ? 2 : 3
    } else {
      lodopStyle[key] = style[key]
    }
  }

  return lodopStyle
}
