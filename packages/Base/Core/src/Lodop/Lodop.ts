import { Data, Message, sleep, useCache } from '@idmy/core'
import { getLodop } from './helper'
import LodopHtmlCss from './LodopHtml.css?raw'

export class LodopPrintError extends Error {}

export interface LodopOptions {
  title: string
  mode: 'print' | 'design' | 'preview' | 'setup'
  after?: (Lodop: Data) => void
  style?: string
}

let LODOP: any = null

export class LodapPrint {
  static printerSetting = useCache<any>('printerSetting', {}, -1, { never: true, global: true })
  private options: LodopOptions
  private readonly title: string
  private readonly runnable: any

  constructor(options: LodopOptions, runnable: any) {
    this.title = options.title
    this.options = options
    this.runnable = runnable
    if (!options.mode) {
      options.mode = 'print'
    }
  }

  public async start() {
    await this.check()
    await this.print()
    LodapPrint.printerSetting.value[this.title] = true
  }

  private async check() {
    if (this.options.mode === 'print') {
      if (!LodapPrint.printerSetting.value[this.title]) {
        Message.info('静默第一次打印，稍后请点击预览界面【设置】，然后选择对应的打印机和配置进行打印。')
        await sleep(3000)
        this.options.mode = 'preview'
      }
    }
  }

  private async print() {
    return new Promise(async (resolve, reject) => {
      if (!LODOP) {
        LODOP = await getLodop()
      }
      // @ts-ignore
      if (window.CLODOP.blOneByone) {
        return reject(new Error('预览窗口已存在。如长时间没弹窗，请检查打印机链接是否正常'))
      }

      if (this.runnable) {
        await this.runnable()
      }

      LODOP.SET_PRINT_MODE('AUTO_CLOSE_PREWINDOW', 1)
      if (this.options.mode === 'print') {
        this.processPrintResult(resolve, reject)
        LODOP.PRINT()
      } else if (this.options.mode === 'setup') {
        LODOP.PRINT_SETUP()
      } else if (this.options.mode === 'design') {
        LODOP.PRINT_DESIGN()
      } else {
        this.processPrintResult(resolve, reject)
        LODOP.PREVIEW()
      }
    })
  }

  private processPrintResult(resolve: any, reject: any) {
    LODOP.On_Return = (_: any, value: string) => {
      const result = value === '0' || value === ''
      if (result) {
        reject(new LodopPrintError())
      } else {
        resolve()
      }
    }
  }
}

export async function lodapPrintHtml(element: Element, options: LodopOptions) {
  const html = `
		<!doctype html>
		<head>
			<style>
			${LodopHtmlCss}
			</style>
		</head>
		<body>
		<div style="${options.style}">
			${element.outerHTML}
		</div>
		</body>
	</html>
	`
  return new LodapPrint(options, () => {
    LODOP.PRINT_INIT(options.title)
    LODOP.ADD_PRINT_HTM(0, 0, '100%', '100%', html)
    options.after?.(LODOP)
  }).start()
}
