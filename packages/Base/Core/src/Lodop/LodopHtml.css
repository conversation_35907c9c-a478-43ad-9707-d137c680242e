.mb-16px {
  margin-bottom: 16px;
}

.mw-200px {
  min-width: 200px !important;
}

.w-100px {
  width: 100px !important;
}

.bgc-ddd {
  background: #ddd;
}

.bgc-ccc {
  background: #ccc;
}

.ml-8px {
  margin-left: 8px;
}

.bt-000 {
  border-top: 1px solid #000;
}

.bt-ccc {
  border-top: 1px solid #ccc;
}

.bt-ddd {
  border-top: 1px solid #ddd;
}

.h-1px {
  height: 1px;
}

.h-2px {
  height: 2px;
}

.h-8px {
  height: 8px;
}

.h-16px {
  height: 16px;
}

.b {
  font-weight: bold !important;
}

.strong {
  font-weight: bold !important;
}

.bold {
  font-weight: bold !important;
}

.i {
  font-style: italic !important;
}

.italic {
  font-style: italic !important;
}

.nhe {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flex {
  display: flex !important;
}

.flex {
  display: flex !important;
}

.aic {
  align-items: center;
}

.aife {
  align-items: flex-end;
}

.acc {
  align-content: center;
}

.acsb {
  align-content: space-between;
}

.asc {
  align-self: center;
}

.jic {
  justify-items: center;
}

.jcfs {
  justify-content: flex-start;
}

.jcfe {
  justify-content: flex-end;
}

.jcc {
  justify-content: center;
}

.jce {
  justify-content: end;
}

.jcsb {
  justify-content: space-between;
}

.jsc {
  justify-self: center
}

.fdc {
  flex-direction: column;
}

.fdr {
  flex-direction: row;
}

.fwnw {
  flex-wrap: nowrap;
}

.fww {
  flex-wrap: wrap;
}

.f1 {
  flex: 1;
  overflow: hidden;
}

.f2 {
  flex: 2;
  overflow: hidden;
}

.f3 {
  flex: 3;
  overflow: hidden;
}

.fwn {
  font-weight: normal !important;
}

.fsn {
  font-style: normal !important;
}

.tac {
  text-align: center !important;
}

.tal {
  text-align: left !important;
}

.tar {
  text-align: right !important;
}

.taj {
  text-align: justify !important;
}

.tai {
  text-align: inherit !important;
}

.vam {
  vertical-align: middle !important;
}

.vat {
  vertical-align: top !important;
}

.vab {
  vertical-align: bottom !important;
}

.fs0 {
  font-size: 0
}

.fs10 {
  font-size: 10px
}

.fs11 {
  font-size: 11px
}

.fs12 {
  font-size: 12px
}

.fs14 {
  font-size: 14px
}

.fs16 {
  font-size: 16px
}

.fs18 {
  font-size: 18px
}

.fs20 {
  font-size: 20px
}

.fs22 {
  font-size: 22px
}

.fs24 {
  font-size: 24px
}

.fs26 {
  font-size: 26px
}

.fs28 {
  font-size: 28px
}

.fs30 {
  font-size: 30px
}

.fs32 {
  font-size: 32px
}

.fs34 {
  font-size: 34px
}

.fs36 {
  font-size: 36px
}

.fs38 {
  font-size: 38px
}

.fs40 {
  font-size: 40px
}

.fs42 {
  font-size: 42px
}

.fs44 {
  font-size: 44px
}

.fs46 {
  font-size: 46px
}

.fs48 {
  font-size: 48px
}

.fs50 {
  font-size: 50px
}

.lh-26px {
  line-height: 26px
}

.lh-30px {
  line-height: 30px
}

.lh-40px {
  line-height: 40px
}

.mt-8px {
  margin-top: 8px;
}

.mb-8px {
  margin-bottom: 8px;
}

.mr-8px {
  margin-right: 8px;
}

.mr-16px {
  margin-right: 16px;
}

.ml-16px {
  margin-left: 16px;
}

.clear {
  clear: both !important
}

.mc {
  margin: 0 auto;
}

.clear:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0
}

a:hover, article:hover, aside:hover, b:hover, body:hover, button:hover, dd:hover, div:hover, dl:hover, dt:hover, figcaption:hover, figure:hover, footer:hover, h1:hover, h2:hover, h3:hover, h4:hover, h5:hover, h6:hover, header:hover, i:hover, input:hover, li:hover, nav:hover, p:hover, section:hover, select:hover, span:hover, textarea:hover, ul:hover {
  outline: none
}

a {
  cursor: pointer;
}

a, article, aside, b, body, button, dd, div, dl, dt, figcaption, figure, footer, h1, h2, h3, h4, h5, h6, header, input, li, nav, p, section, select, span, textarea, ul {
  padding: 0;
  margin: 0;
  list-style: none;
  text-decoration: none;
  border: none;
  font-weight: 400;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-smoothing: antialiased;
  outline: none;
  font-family: Microsoft YaHei, PingFang SC, Helvetica Neue, Arial, sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: .5em;
  font-weight: 700
}

input[type=button], input[type=reset], input[type=search], input[type=submit], textarea {
  -webkit-appearance: none
}

body, html {
  height: 100%;
  width: 100%;
  background-color: white;
  font-size: 14px;
  font-family: Microsoft YaHei, PingFang SC, Helvetica Neue, Arial, sans-serif;
}

.tac {
  text-align: center !important
}

.tal {
  text-align: left !important
}

.tar {
  text-align: right !important
}

.taj {
  text-align: justify !important
}

.tai {
  text-align: inherit !important
}

.b, .bold, .strong {
  font-weight: 700 !important
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  border: 1px solid #888;
  padding: 4px 6px;
  text-align: center;
}

th {
  text-align: center;
  font-weight: bold;
}
