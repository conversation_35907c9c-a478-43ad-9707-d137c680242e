const DEFAULT_DPI = 96
const deviceDpi = () => {
  if (window.screen && window.screen.deviceXDPI && window.screen.logicalXDPI) {
    return (window.screen.deviceXDPI / window.screen.logicalXDPI) * DEFAULT_DPI
  } else if (window.devicePixelRatio) {
    return DEFAULT_DPI * window.devicePixelRatio
  } else {
    return DEFAULT_DPI
  }
}

const dpi = deviceDpi()
const mmToPx = mm => mm * (dpi / 25.4)

export const getPx = (width, height) => {
  return {
    height: mmToPx(parseInt(height)) + 180,
    width: mmToPx(parseInt(width)) + 180,
  }
}

const LICENSES = import.meta.env.VITE_LODOP_LICENSES

export const getLodop = () =>
  new Promise((resolve, reject) => {
    let i = 0
    const fn = () => {
      try {
        const lodap = window.getCLodop()
        if (lodap && lodap.CVERSION) {
          clearInterval(timer)
          window.LODOP = lodap
          if (LICENSES) {
            lodap.SET_LICENSES('', LICENSES, '', '')
          }
          resolve(lodap)
        }
      } catch {}

      if (++i > 20) {
        clearInterval(timer)
        reject(new Error('打印软件 Lodop 没启动，或者地址端口错误'))
      }
    }

    const timer = setInterval(fn, 500)
    fn()
  })

export const checkReopen = async () => {
  await getLodop()
  if (window.CLODOP.blOneByone) {
    throw new Error('预览窗口已存在。如长时间没弹窗，请检查打印机是否链接正常')
  }
}

export async function allPrinters() {
  const LODOP = await getLodop()
  const out = []
  const count = LODOP.GET_PRINTER_COUNT()
  for (let i = 0; i < count; i++) {
    const name = LODOP.GET_PRINTER_NAME(i)
    const pages = LODOP.GET_PAGESIZES_LIST(name, ';').split(';')
    out.push({ name, pages })
  }
  return out
}

export async function checkPrinter(printerName, pagerName) {
  const arr = await allPrinters()
  const printerIdx = arr.findIndex(item => item.name === printerName)
  if (printerIdx < 0) {
    throw new Error(`无效的打印机：${printerName}`)
  } else {
    const pager = arr[printerIdx].pages.includes(pagerName)
    if (!pager) {
      throw new Error(`无效的纸张：${pagerName}`)
    }
  }
}

allPrinters().then(data => console.log('打印机信息', data))

// 计算 css规则
function matchRules(el, sheets) {
  sheets = sheets || document.styleSheets
  const ret = []

  for (const i in sheets) {
    if (sheets.hasOwnProperty(i)) {
      const rules = sheets[i].rules || sheets[i].cssRules
      for (const r in rules) {
        try {
          if (rules[r].selectorText && el.matches(rules[r].selectorText)) {
            ret.push(rules[r])
          }
        } catch (e) {
          console.log(r, rules)
          console.error('e', e)
        }
      }
    }
  }
  return ret
}

// 计算 css转化内联样式
export function applyInline(element, recursive = true) {
  if (!element) {
    throw new Error('No element specified.')
  }
  const matches = matchRules(element)
  const srcRules = document.createElement(element.tagName).style
  srcRules.cssText = element.style.cssText

  matches.forEach(rule => {
    for (const prop of rule.style) {
      const val = srcRules.getPropertyValue(prop) || rule.style.getPropertyValue(prop)
      const priority = rule.style.getPropertyPriority(prop)
      if (prop.indexOf('--un') === -1 && !['font-family'].includes(prop)) {
        element.style.setProperty(prop, val, priority)
      }
    }
  })
  if (recursive) {
    try {
      for (let ci = 0; ci < element.children.length; ci++) {
        const child = element.children[ci]
        applyInline(child, recursive)
      }
    } catch (e) {
      console.log(element.children)
      console.error('e', e)
    }
  }
}
