<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { BaseAutoComplete } from "@mh-base/core"
import { articlePageByOrgApi } from "@mh-wm/util"

const props = defineProps({
  deptCode: {
    type: String,
    default: null
}})
const emit = defineEmits(['selected'])

const artDataModel = reactive({
  columns: [
    { title: '条目ID', dataIndex: 'artId', width: 100, align: 'right' },
    { title: '品名', dataIndex: 'artName', width: 150 },
    { title: '规格', dataIndex: 'artSpec', width: 100 },
    { title: '生产厂家', dataIndex: 'producer', width: 150 },
    { title: '包装单位', dataIndex: 'packUnit', width: 75 },
    { title: '制剂单位', dataIndex: 'cellUnit', width: 75 },
    { title: '包装制剂数', dataIndex: 'packCells', width: 80 },
    { title: '医保编码', dataIndex: 'miCode', width: 150 },
    { title: '批准文号', dataIndex: 'approvalNo', width: 150 }
  ],
  searchFormModel: {
     order: "asc",
    sidx: "t_article.art_id",
    keyword: "",
    artName: "",
  },
  options: [],
  pagination: {
    pageSize: 10, // 每页条数
    pageNum: 1, // 当前页码
    pages: 0, // 总页数
    total: 0, // 总条数
    showTotal: total => `共：${total} 条`,
    onChange: async (current, pageSize) => {
      artDataModel.pagination.pageSize = pageSize
      artDataModel.pagination.pageNum = current
      await artDataModel.loadOptions()
    }
  },  loadOptions: async () => {
    const  data  = await articlePageByOrgApi({
      'S_EQ_t_article__Stock_Req': 1,
      ...artDataModel.searchFormModel,
      ...artDataModel.pagination
    })
    data.list.forEach((item) => {
      item.pkStr = item.artId + "-" + item.stockNo;
      item.stockPacksTotal = item.totalPacks;
      item.stockCellsTotal = item.totalCells;
    });
    artDataModel.options = data.list
    artDataModel.pagination.pageNum = Number(data.pageNum)
    artDataModel.pagination.pages = Number(data.pages)
    artDataModel.pagination.total = Number(data.total)
  },
  onSearch: async (value: string) => {
    artDataModel.pagination.pageNum = 1
    artDataModel.searchFormModel.artName = value
    await artDataModel.loadOptions()
  },
  onSelect: (item: any) => {
    if (item) {
      artDataModel.searchFormModel.artName = item.artName +  (item.artSpec ? ' ' + item.artSpec : '') + (item.producer? ' ' + item.producer: '')
      emit('selected', item)
    }
  },
})

const init = () => {
  artDataModel.searchFormModel.artName = undefined
  artDataModel.options = []
}

// 设置显示值（用于回填数据）
const setValue = (artData: any) => {
  if (artData) {
    // 构造显示文本：品种名称 + 规格 + 生产厂家
    const displayText = artData.artName +
      (artData.artSpec ? ' ' + artData.artSpec : '') +
      (artData.producer ? ' ' + artData.producer : '')

    artDataModel.searchFormModel.artName = displayText
  }
}

watch(() => props.deptCode, (val) => {
  init()
}, {
  immediate: true,
  deep: true,
})
const keywordRef = ref()

const focus = () => {
  keywordRef?.value.$refs().focus()
}

defineExpose({
  init,
  focus,
  setValue
})
</script>

<template>
  <div>
    <base-auto-complete
      style="width: 100%"
      ref="keywordRef"
      v-model:value="artDataModel.searchFormModel.artName"
      keyColumn="artId"
      :columns="artDataModel.columns"
      :options="artDataModel.options"
      :pagination="artDataModel.pagination"
      @onSearch="artDataModel.onSearch"
      @onSelect="artDataModel.onSelect"
    />
  </div>
</template>
