<script setup lang="ts">
import { BaseAutoComplete, Currents } from '@mh-base/core'
import { getDictData } from '@mh-hip/util'
import { sectionStockAndWmStockApi } from '@mh-inpatient-hsd/util'

const props = defineProps({
  // 查询范围： 无：所有，1：catType.in(201,301)，2：catType.eq(301)+stockReq=1, 3:查机构项目物价,
  // 4:artType.in(材料,护理,输氧)（材料、护理、输氧）,5：stockReq=1, 6:artTypeId in 检验、护理、诊疗+ stockReq=0
  // 7：catType.in(201,301, 401) 8: 护士计费条目 for_Billing_Entry = 1
  searchType: {
    type: Number,
    default: null,
  },
  sectionId: {
    type: Number,
    default: null,
  },
  // 1: 机构处方集 2：orgItemPrice
  urlType: {
    type: Number,
    default: null,
  },
})
const emit = defineEmits(['selected'])

const artDataModel = reactive({
  // 门诊挂号收费项目
  columns: [
    { title: '条目ID', dataIndex: 'artId', width: 100, align: 'right' },
    { title: '品名', dataIndex: 'artName', width: 150 },
    { title: '规格', dataIndex: 'artSpec', width: 100 },
    { title: '生产厂家', dataIndex: 'producer', width: 150 },
    { title: '医保编码', dataIndex: 'miCode', width: 150 },
  ],
  stockColumns: [
    { title: '条目ID', dataIndex: 'artId', width: 100, align: 'right' },
    { title: '品名', dataIndex: 'artName', width: 150 },
    { title: '规格', dataIndex: 'artSpec', width: 100 },
    { title: '生产厂家', dataIndex: 'producer', width: 150 },
    { title: '医保编码', dataIndex: 'miCode', width: 150 },
    { title: '病区库存', dataIndex: 'sectionStock', width: 100 },
    { title: '药房库存', dataIndex: 'wmStock', width: 100 },
  ],
  searchFormModel: {
    order: 'asc',
    sidx: 't_article.art_id',
    artName: '',
  },
  options: [],
  pagination: {
    pageSize: 10, // 每页条数
    pageNum: 1, // 当前页码
    pages: 0, // 总页数
    total: 0, // 总条数
    showTotal: total => `共：${total} 条`,
    onChange: async (current, pageSize) => {
      artDataModel.pagination.pageSize = pageSize
      artDataModel.pagination.pageNum = current
      await artDataModel.loadOptions()
    },
  },
  loadOptions: async () => {
    artDataModel.searchFormModel.keyword = artDataModel.searchFormModel.artName
    artDataModel.searchFormModel.orgId = Currents.tenantId
    artDataModel.searchFormModel.disabled = 0
    if (props.searchType) {
      if (props.searchType === 1) {
        artDataModel.searchFormModel.S_IN_t_article__Cat_Type_ID = '201,301'
      } else if (props.searchType === 2) {
        artDataModel.searchFormModel.S_EQ_t_article__Cat_Type_ID = 301
        artDataModel.searchFormModel.S_EQ_t_article__Stock_Req = 1
      } else if (props.searchType === 4) {
        artDataModel.searchFormModel.S_IN_t_article__Art_Type_ID = '21,14,26,29'
      } else if (props.searchType === 5) {
        artDataModel.searchFormModel.S_EQ_t_article__Stock_Req = 1
      } else if (props.searchType === 6) {
        artDataModel.searchFormModel.S_IN_t_article__Art_Type_ID = '22,26,27'
        artDataModel.searchFormModel.S_EQ_t_article__Stock_Req = 0
      } else if (props.searchType === 7) {
        artDataModel.searchFormModel.S_IN_t_article__Cat_Type_ID = '201,301,401'
      } else if (props.searchType === 8) {
        artDataModel.searchFormModel.S_EQ_t_article__for_Billing_Entry = 1
      }
    }
    let dictType = 'article'
    if ((props.searchType && props.searchType === 3) || (props.urlType && props.urlType === 2)) {
      dictType = 'orgItemPrice'
    }
    const { list, pageNum, pages, total } = await getDictData(dictType, {
      ...artDataModel.searchFormModel,
      ...artDataModel.pagination,
    })
    artDataModel.options = list
    if (props.sectionId) {
      const artIdLs = list.map((item: any) => item.artId)
      const stockParams = {
        sectionId: props.sectionId,
        artIdLs: artIdLs,
      }
      sectionStockAndWmStockApi(stockParams).then((res: any) => {
        if (res) {
          artDataModel.options.forEach((item: any) => {
            const existArt = res.find((art: any) => art.artId === item.artId)
            item.totalPacks = existArt ? existArt.totalPacks : 0
            item.totalCells = existArt ? existArt.totalCells : 0
            item.wmTotalPacks = existArt ? existArt.wmTotalPacks : 0
            item.wmTotalCells = existArt ? existArt.wmTotalCells : 0
            item.sectionStock = (item.totalPacks ? item.totalPacks + item.packUnit : '') + (item.totalCells ? item.totalCells + item.cellUnit : '')
            item.wmStock = (item.wmTotalPacks ? item.wmTotalPacks + item.packUnit : '') + (item.wmTotalCells ? item.wmTotalCells + item.cellUnit : '')
          })
        }
      })
    }
    artDataModel.pagination.pageNum = Number(pageNum)
    artDataModel.pagination.pages = Number(pages)
    artDataModel.pagination.total = Number(total)
  },
  onSearch: async (value: string) => {
    artDataModel.pagination.pageNum = 1
    artDataModel.pagination.artName = value
    await artDataModel.loadOptions()
  },
  onSelect: (item: any) => {
    if (item) {
      artDataModel.searchFormModel.artName = item.artName + (item.artSpec ? ' ' + item.artSpec : '') + (item.producer ? ' ' + item.producer : '')
      emit('selected', item)
    }
  },
})

const init = () => {
  artDataModel.searchFormModel.artName = undefined
  artDataModel.options = []
}

defineExpose({
  init,
})
</script>

<template>
  <div>
    <base-auto-complete
      style="width: 100%"
      v-model:value="artDataModel.searchFormModel.artName"
      keyColumn="artId"
      :columns="props.sectionId ? artDataModel.stockColumns : artDataModel.columns"
      :options="artDataModel.options"
      :pagination="artDataModel.pagination"
      @onSearch="artDataModel.onSearch"
      @onSelect="artDataModel.onSelect"
    />
  </div>
</template>
