<script setup lang="ts">
import filters from './utils/filters.ts'
import { Table, Button, Col, Form, FormItem, Modal, Badge, Row, Radio, RadioGroup, InputNumber, message } from 'ant-design-vue'
import type { TableColumnType } from 'ant-design-vue'
import { oeSameArtFeeLsApi, oeFeeChangeTotalApi } from '@mh-inpatient-hsd/util'
import { sectionInfoApi } from "@mh-hip/util"

const props = defineProps({
  sectionId: {
    type: Number,
    default: null
  },
})

const srcColumns: TableColumnType[] = [
  {
    title: '医嘱号',
    dataIndex: 'oeNo',
    align: 'center',
    width: 70
  },
  {
    title: '费用日期',
    dataIndex: 'bsnDate',
    align: 'center',
    width: 90
  },
  {
    title: '项目编码',
    dataIndex: 'artCode',
    align: 'left',
    width: 120,
    ellipsis: true
  },
  {
    title: '医保项目编码',
    dataIndex: 'miCode',
    align: 'left',
    width: 130,
    ellipsis: true
  },
  {
    title: '项目内容',
    dataIndex: 'artDesc',
    align: 'left',
    ellipsis: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    align: 'center',
    width: 50
  },
  {
    title: '数量',
    dataIndex: 'total',
    align: 'right',
    width: 130,
    fixed: 'right'
  },
  {
    title: '单价',
    dataIndex: 'price',
    align: 'right',
    width: 90,
    fixed: 'right'
  },
  {
    title: '金额',
    dataIndex: 'amount',
    align: 'right',
    width: 100,
    fixed: 'right'
  }
]
const sameArtColumns: TableColumnType[] = [
  {
    title: '医嘱号',
    dataIndex: 'oeNo',
    align: 'center',
    width: 70
  },
  {
    title: '费用日期',
    dataIndex: 'bsnDate',
    align: 'center',
    width: 90
  },
  {
    title: '项目编码',
    dataIndex: 'artCode',
    align: 'left',
    width: 120,
    ellipsis: true
  },
  {
    title: '医保项目编码',
    dataIndex: 'miCode',
    align: 'left',
    width: 130,
    ellipsis: true
  },
  {
    title: '项目内容',
    dataIndex: 'artDesc',
    align: 'left',
    ellipsis: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    align: 'center',
    width: 50
  },
  {
    title: '数量',
    dataIndex: 'total',
    align: 'right',
    width: 90,
    fixed: 'right'
  },
  {
    title: '单价',
    dataIndex: 'price',
    align: 'right',
    width: 90,
    fixed: 'right'
  },
  {
    title: '金额',
    dataIndex: 'amount',
    align: 'right',
    width: 100,
    fixed: 'right'
  },
  {
    title: '补录',
    dataIndex: 'manualFlag',
    align: 'center',
    width: 60,
    fixed: 'right'
  }
]

const emit = defineEmits(['page'])
const visible = ref(false);
const stConfirmLoading = ref<boolean>(false)
const toChangeList = ref<any>([])
const dataSource = ref<any>([])
const srcExecFee = ref({})
const formModel = reactive<any>({})
const drugAccountingMode = ref<boolean>(false)

const open = (record: any) => {
  srcExecFee.value = record
  srcExecFee.value.total = null
  toChangeList.value = [srcExecFee.value]
  getDataSource()
  getSection()
  visible.value = true
}

const getSection = async () => {
  drugAccountingMode.value = false
  formModel.returnToWm = 0
  sectionInfoApi({ sectionId: props.sectionId }).then((data: any) => {
    if (data) {
      drugAccountingMode.value = data.medicinesAccountingMode === 1
      formModel.returnToWm = data.stockReturnMode ? Number(data.stockReturnMode) : 0
    }
  })
}

const getDataSource = async () => {
  dataSource.value = []
  oeSameArtFeeLsApi({execSeqid: srcExecFee.value.execSeqid, lineNo: srcExecFee.value.lineNo}).then((list: any) => {
    if (list) {
      dataSource.value = list
    }
  })
}

const sumAmount = computed(() => {
  if (dataSource.value && dataSource.value.length > 0) {
    let amount = 0
    dataSource.value.forEach((item: any) => {
      amount += item.amount
    })
    return amount
  }
  return null
})

function handleCancel() {
  visible.value = false
  emit('page')
}

const handleChangeTotal = () => {
  if (!srcExecFee.value.total) {
    message.warning('请设置调整数量')
    return
  }
  stConfirmLoading.value = true
  const params = {
    execSeqid: srcExecFee.value.execSeqid,
    lineNo: srcExecFee.value.lineNo,
    total: srcExecFee.value.total,
    returnToWm: formModel.returnToWm
  }

  oeFeeChangeTotalApi(params).then(() => {
    message.success('复制补录完成')
    srcExecFee.value.total = null
    getDataSource()
  }).finally(() => {
    stConfirmLoading.value = false
  })
}

defineExpose({
  open
})
</script>

<template>
  <Modal v-model:open="visible" title="复制补录" width="1300px" @cancel="handleCancel" :mask-closable="false" style="top: 20px">
    <template #footer>
      <Form :model="formModel" w-full>
        <Row>
          <Col flex="auto">
          </Col>
          <Col flex="400px" p-t-1 v-if="srcExecFee.stockReq === 1 && !drugAccountingMode">
            <Form-item label="退药模式" name="returnToWm" :colon="false" :label-col="{ span: 6 }">
              <Radio-group v-model:value="formModel.returnToWm">
                <Radio :value="1">
                  退回药房
                </Radio>
                <Radio :value="0">
                  退回病区
                </Radio>
                <Radio :value="2">
                  使用/破损
                </Radio>
              </Radio-group>
            </Form-item>
          </Col>
          <Col flex="100px">
            <Button type="dashed" @click="handleCancel">
              关闭
            </Button>
          </Col>
        </Row>
      </Form>
    </template>
    <div class="content-req">
      <div class="op-btn">
        <Button @click="handleChangeTotal" type="primary" :disabled="!toChangeList[0].total">
          补录
        </Button>
      </div>
      <Table :rowKey="(record: any) => record.keyStr" :columns="srcColumns" :data-source="toChangeList">
        <template #bodyCell="{ text, column, record, index }">
          <template v-if="column.dataIndex === 'artDesc'">
            <div>
              <span class="art-name">{{ record.artName }}</span><span>{{ record.artSpec }}</span>
            </div>
            <div v-if="record.producer">{{ record.producer }}</div>
          </template>
          <template v-if="column.dataIndex === 'total'">
            <Input-number v-model:value="srcExecFee.total" placeholder="请设置数量" :precision="4" :min="-99999999" :max="99999999" style="width: 100%;"/>
          </template>
          <template v-if="column.dataIndex === 'amount'">
            <div v-if="record.total">
              <span v-if="record.total > 0">{{ record.total * record.price }}</span>
              <span v-else class="total-le0">{{ record.total * record.price }}</span>
            </div>
          </template>
        </template>
      </Table>
      <div class="same-art-feeLs">同条目费用明细</div>
      <Table :rowKey="(record: any) => record.keyStr" :columns="sameArtColumns" :data-source="dataSource" :scroll="{ y: 'calc(100vh - 430px)'}">
        <template #bodyCell="{ text, column, record, index }">
          <template v-if="column.dataIndex === 'manualFlag'">
            <Badge v-if="record.manualFlag === 1" status="warning" text="补录"/>
          </template>
          <template v-else-if="column.dataIndex === 'artDesc'">
            <div>
              <span class="art-name">{{ record.artName }}</span><span>{{ record.artSpec }}</span>
            </div>
            <div v-if="record.producer">{{ record.producer }}</div>
          </template>
          <template v-else-if="['total', 'amount'].includes(column.dataIndex)">
            <span v-if="text > 0">{{ text }}</span>
            <span v-else class="total-le0">{{ text }}</span>
          </template>
        </template>
      </Table>
      <div class="sumAmount">{{ filters.formatAmount(sumAmount) }}</div>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.content-req {
  height: calc(100vh - 160px);
  overflow: auto;
}
.art-name {
  font-weight: bold;
}
.total-gt0 {
  color: #018338;
}
.total-le0 {
  color: #ff0000;
}
.same-art-feeLs {
  font-weight: bold;
  font-size: 20px;
  padding: 20px 0px 10px 5px;
}
.sumAmount {
  padding-right: 85px;
  width: 100%;
  text-align: right;
}
.op-btn {
  text-align: right;
  button {
    margin-right: 10px;
  }
}
</style>
