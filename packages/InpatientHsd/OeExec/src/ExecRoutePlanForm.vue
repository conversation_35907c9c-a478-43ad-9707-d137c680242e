<script setup lang="ts">
import type { TableColumnType } from 'ant-design-vue'
import { SelectWithUnitTotal } from '@mh-inpatient-hsd/selector'
import { Button, Col, Tag, Table, Row, InputNumber, Checkbox } from 'ant-design-vue'

const artSelectWithUnitTotalRef = ref<InstanceType<typeof ArtSelectWithUnitTotal>>()
const props = defineProps({
  planOe: {
    type: Object,
    default: {},
  },
})
const columns: TableColumnType[] = [
  {
    title: '条目编号',
    dataIndex: 'artId',
    align: 'center',
    width: 80,
  },
  {
    title: '条目名称',
    dataIndex: 'artDesc',
    align: 'left',
    ellipsis: true,
  },
  {
    title: '首次',
    dataIndex: 'forFirstTimeBool',
    align: 'center',
    width: 60,
  },
  {
    title: '每次',
    dataIndex: 'forEachTimeBool',
    align: 'center',
    width: 60,
  },
  {
    title: '库存',
    dataIndex: 'sectionStock',
    align: 'center',
    width: 90,
  },
  {
    title: '数量',
    dataIndex: 'total',
    align: 'right',
    width: 170,
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 60,
  },
]

const handleArtSelect = (selectedArt: any) => {
  const art = props.planOe.planFeeLs.find((item: any) => item.artId === selectedArt.artId)
  if (art) {
    art.unitType = selectedArt.unitType
    art.total = selectedArt.total
    art.unit = selectedArt.unit
  } else {
    selectedArt.forFirstTimeBool = true
    selectedArt.forEachTimeBool = true
    props.planOe.planFeeLs.push(selectedArt)
  }
}

// 耗材选择
const handleAdd = () => {
  artSelectWithUnitTotalRef.value.open()
}

const handleDelete = (artId: any) => {
  props.planOe.planFeeLs = props.planOe.planFeeLs.filter((item: any) => item.artId !== artId)
}

const handleEdit = () => {
  props.planOe.hiddenEdit = false
  props.planOe.changePlan = true
}

defineExpose({
  open,
})
</script>

<template>
  <div class="content-req">
    <Row>
      <Col :span="22" class="oe-title">
        <span v-if="props.planOe.bedNo" m-l-2>{{ props.planOe.bedNo }}床 </span>
        <span v-if="props.planOe.patientName" m-l-2>{{ props.planOe.patientName }}</span>
        <span v-if="props.planOe.oeText" m-l-2>{{ props.planOe.oeText }}</span>
        <span v-if="props.planOe.freqCode" m-l-2>{{ props.planOe.freqCode }}</span>
        <span v-if="props.planOe.routeName" m-l-2>{{ props.planOe.routeName }}</span>
        <span v-if="props.planOe.dpm" m-l-2>滴速{{ props.planOe.dpm }}</span>
        <span v-if="props.planOe.oeNo">#{{ props.planOe.oeNo }}</span>
      </Col>
      <Col :span="2" style="text-align: right">
        <template v-if="props.planOe.hiddenEdit">
          <Button @click="handleEdit"> 调整 </Button>
        </template>
        <template v-else>
          <Button @click="handleAdd"> 添加 </Button>
        </template>
      </Col>
    </Row>
    <template v-if="props.planOe.hiddenEdit">
      <Row v-for="(planFee, index) in props.planOe.planFeeLs" :key="index">
        <Col :span="2">
          {{ planFee.artId }}
        </Col>
        <Col :span="11">
          <div>
            <span class="art-name">{{ planFee.artName }}</span
            ><span>{{ planFee.artSpec }}</span>
          </div>
          <div v-if="planFee.producer">{{ planFee.producer }}</div>
        </Col>
        <Col :span="3">
          <template v-if="props.planOe.oneDayMaxTimes !== 1">
            <Tag v-if="planFee.forFirstTimeBool">首次</Tag>
            <Tag v-if="planFee.forEachTimeBool">每次</Tag>
          </template>
        </Col>
        <Col :span="4">
          <template v-if="planFee.sectionTotalPacks || planFee.sectionTotalCells">
            库存：
            <span v-if="planFee.sectionTotalPacks">{{ planFee.sectionTotalPacks }}{{ planFee.packUnit }}</span>
            <span v-else-if="planFee.sectionTotalCells">{{ planFee.sectionTotalCells }}{{ planFee.cellUnit }}</span>
          </template>
        </Col>
        <Col :span="4">{{ planFee.total }}*{{ planFee.unit }}</Col>
      </Row>
    </template>
    <template v-else>
      <Table :rowKey="(record: any) => record.artId" :has-surely="false" :columns="columns" :data-source="props.planOe.planFeeLs">
        <template #bodyCell="{ text, column, record }">
          <template v-if="column.dataIndex === 'artDesc'">
            <div>
              <span class="art-name">{{ record.artName }}</span
              ><span>{{ record.artSpec }}</span>
            </div>
            <div v-if="record.producer">{{ record.producer }}</div>
          </template>
          <template v-else-if="column.dataIndex === 'forFirstTimeBool'">
            <Checkbox v-if="props.planOe.oneDayMaxTimes !== 1" v-model:checked="record.forFirstTimeBool" />
          </template>
          <template v-else-if="column.dataIndex === 'forEachTimeBool'">
            <Checkbox v-if="props.planOe.oneDayMaxTimes !== 1" v-model:checked="record.forEachTimeBool" />
          </template>
          <template v-else-if="column.dataIndex === 'sectionStock'">
            <span v-if="record.sectionTotalPacks">{{ record.sectionTotalPacks }}{{ record.packUnit }}</span>
            <span v-else-if="record.sectionTotalCells">{{ record.sectionTotalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column.dataIndex === 'total'">
            <Input-number
              v-model:value="record.total"
              placeholder="请录入数量"
              :controls="false"
              :min="record.stockReq ? 1 : 0.01"
              :precision="record.stockReq ? 0 : 2"
              style="width: 95%"
              :addon-after="record.unit"
            />
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a c-error @click="handleDelete(record.artId)"> 删除 </a>
          </template>
        </template>
      </Table>
      <select-with-unit-total ref="artSelectWithUnitTotalRef" :artSearchType="1" @add-art="handleArtSelect" />
    </template>
  </div>
</template>

<style lang="less" scoped>
.content-req {
  padding: 10px;
}
.art-name {
  font-weight: bold;
}
.oe-title {
  font-weight: bold;
  font-size: 15px;
}
</style>
