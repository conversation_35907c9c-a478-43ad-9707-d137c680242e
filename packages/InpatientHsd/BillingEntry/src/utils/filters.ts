import dayjs from 'dayjs'

const filters = {
  dateFormatMDHM(dataStr: any, pattern = 'MM-DD HH:mm'): string {
    if (!dataStr) {
      return ''
    }
    return dayjs(dataStr).format(pattern)
  },
  formatAge(ageOfYears: number, ageOfDays: number): string {
    let year = ageOfYears ? ageOfYears : 0
    let month = ageOfDays ? Math.floor(ageOfDays / 30) : 0
    let day = ageOfDays ? ageOfDays : 0
    if (year > 6) {
      return year + '岁'
    } else if (year > 0) {
      return year + '岁' + month + '月'
    } else if (month > 0 && day > 0) {
      return month + '月' + day + '天'
    } else if (month > 0) {
      return month + '月'
    } else {
      return day + '天'
    }
  },
}



export default filters
