export function oeQuestionApi(params: any) {
  return http.post('/hsd/orderEntry/oeQuestion', params, { appKey: 'inpatientHsd' })
}
export function execArtOeLsApi(params: any) {
  return http.post('/hsd/orderEntry/execArtOeLs', params, { appKey: 'inpatientHsd' })
}
export function execArtOeLsByDrugModeApi(params: any) {
  return http.post('/hsd/orderEntry/execArtOeLsByDrugMode', params, { appKey: 'inpatientHsd', ignoreError: true })
}
export function preOeExecPlanFeeApi(params: any) {
  return http.post('/hsd/orderEntry/preOeExecPlanFee', params, { appKey: 'inpatientHsd' })
}
export function createTemporaryOeApi(params: any) {
  return http.post('/hsd/orderEntry/createTemporaryOe', params, { appKey: 'inpatientHsd' })
}
export function drugAccTempPackDeliverOeApi(params: any) {
  return http.post('/hsd/orderEntry/drugAccTempPackDeliverOe', params, { appKey: 'inpatientHsd', ignoreError: true })
}
export function billingEntryOeLsApi(params: any) {
  return http.post('/hsd/orderEntry/billingEntryOeLs', params, { appKey: 'inpatientHsd' })
}
export function batchSaveBillingEntryApi(params: any) {
  return http.post('/hsd/orderEntry/batchSaveBillingEntry', params, { appKey: 'inpatientHsd' })
}
