export function orderFeeLsByOrderIdApi(params: any): Promise<any> {
  return http.post('/hsd/orderExecFee/orderExecFeeLsByOrderId', params, { appKey: 'inpatientHsd' })
}
export function orderFeeAddApi(params: any): Promise<any> {
  return http.post('/hsd/orderExecFee/addOrderExecFee', params, { appKey: 'inpatientHsd' })
}
export function orderItemAUPSumFeeLsApi(params: any): Promise<any> {
  return http.post('/hsd/orderExecFee/orderItemAUPSumFeeLs', params, { appKey: 'inpatientHsd' })
}
export function orderFeeRefundLsApi(params: any): Promise<any> {
  return http.post('/hsd/orderExecFee/refundOrderFeeLs', params, { appKey: 'inpatientHsd' })
}
