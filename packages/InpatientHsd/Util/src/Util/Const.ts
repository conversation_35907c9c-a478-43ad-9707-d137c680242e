// 当前病区编号，用户sessionStorage缓存
export const CURRENT_SECTION_ID = 'nurse:current_section_id'
export const CURRENT_SECTION_DEPT_CODE = 'nurse:current_section_dept_code'
export const CURRENT_SECTION_NAME = 'nurse:current_section_name'
export const CURRENT_SECTION_REQ_TYPE = 'nurse:current_section_req_type'
export const CURRENT_SECTION_MEAL_COUNT_CHECK = 'nurse:current_section_meal_count_check'
export const CURRENT_SECTION_AUTO_ACCOUNTED = 'nurse:current_section_auto_accounted'
export const CURRENT_SECTION_STOCK_RETURN_MODE = 'nurse:current_section_stock_return_mode'
export const SECTION_DISCHARGE_VISIT_ID = 'nurse:section_discharge_visit_id'
export const SECTION_REQ_TYPE_CONTROLLING = 1
export const SECTION_REQ_TYPE_PHARMACYDISPENSING = 2
// 发送药品预占计费
export const SECTION_ACC_MODE_DRUG = 1
// 扣病区库存计费
export const SECTION_ACC_MODE_EXEC = 2
// 病区申领模式
// 按需统领
export const SECTION_DRUG_REQ_MODE_CONTROLLING = 1
// 补足短缺
export const SECTION_DRUG_REQ_MODE_COMPLETION = 2
// 忽略常备库存按需统领
export const SECTION_DRUG_REQ_MODE_CONTROLLING_IGNORE_AID = 3
// 病区不做耗材费用判断（皮试、采血）
export const SECTION_IGNORE_CONSUMABLE_FEE_CHECK = 'nurse:section_ignore_consumable_fee_check'
