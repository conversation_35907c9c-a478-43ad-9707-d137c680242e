<script setup lang="ts">
import type { TableColumnType } from 'ant-design-vue'
import { Badge, Button, Col, Form, FormItem, InputNumber, message, Modal, Radio, RadioGroup, Row, Table } from 'ant-design-vue'
import { orderFeeAddApi, orderFeeLsByOrderIdApi } from '@mh-inpatient-hsd/util'
import { ArtSelect } from '@mh-inpatient-hsd/selector'

const props = defineProps({
  sectionId: {
    type: Number,
    default: null,
  },
  ignoreAnstFee: {
    type: Boolean,
    default: false,
  }
})

const columns: TableColumnType[] = [
  {
    title: '序号',
    align: 'center',
    dataIndex: 'series',
    width: 50,
  },
  {
    title: '条目编号',
    dataIndex: 'artId',
    align: 'center',
    width: 90,
  },
  {
    title: '项目编码',
    dataIndex: 'artCode',
    align: 'left',
    width: 150,
    ellipsis: true,
  },
  {
    title: '医保项目编码',
    dataIndex: 'miCode',
    align: 'left',
    width: 250,
    ellipsis: true,
  },
  {
    title: '项目内容',
    dataIndex: 'artDesc',
    align: 'left',
    ellipsis: true,
  },
  {
    title: '计量单位',
    dataIndex: 'unit',
    align: 'center',
    width: 80,
  },
  {
    title: '数量',
    dataIndex: 'baseTotal',
    align: 'right',
    width: 90,
  },
  {
    title: '单价',
    dataIndex: 'price',
    align: 'right',
    width: 90,
  },
  {
    title: '金额',
    dataIndex: 'amount',
    align: 'right',
    width: 100,
  },
  {
    title: '补录',
    dataIndex: 'manualFlag',
    align: 'center',
    width: 60,
    fixed: 'right',
  },
]

const emit = defineEmits(['add'])
const visible = ref(false)
const formRef = ref()
const formModel = ref<any>({})
const stConfirmLoading = ref<boolean>(false)
const dataSource = ref<any>([])
const orderId = ref<string>()
const lineNo = ref<number>()
const orderLineno = ref<number>()

const artSelectRef = ref<InstanceType<typeof ArtSelect>>()
const open = (initOrderId: string, initLineNo: number, initOrderLineno: number) => {
  dataSource.value = []

  nextTick(() => {
    artSelectRef.value?.init()
  })
  orderId.value = initOrderId
  lineNo.value = initLineNo
  orderLineno.value = initOrderLineno
  visible.value = true
  getDataSource()
}

const getDataSource = async () => {
  dataSource.value = []
  orderFeeLsByOrderIdApi({ orderId: orderId.value, ignoreAnstFee: props.ignoreAnstFee }).then((data: any) => {
    if (data) {
      dataSource.value = data.filter(item => item.orderLineno === orderLineno.value)
    }
  })
}

const clearFormModel = () => {
  formModel.value.artId = null
  formModel.value.artSpec = null
  formModel.value.artName = null
  formModel.value.producer = null
  formModel.value.total = null
  formModel.value.unitType = null
  formModel.value.packUnit = null
  formModel.value.cellUnit = null
  formModel.value.artCode = null
  formModel.value.packPrice = null
  formModel.value.cellPrice = null
  formModel.value.packCells = null
}

function handleCancel() {
  visible.value = false
  emit('add')
}

// 药品选择
function handleArtSelect(art: any) {
  clearFormModel()
  if (art) {
    formModel.value.artId = art.artId
    formModel.value.packUnit = art.packUnit
    formModel.value.cellUnit = art.cellUnit
    if (!formModel.value.packUnit && !formModel.value.cellUnit) {
      formModel.value.packUnit = '次'
    }
    formModel.value.total = 1
    if (art.stockReq === 1) {
      if (formModel.value.cellUnit) {
        formModel.value.unitType = 1
        formModel.value.unit = formModel.value.cellUnit
      } else if (formModel.value.packUnit) {
        formModel.value.unitType = 2
        formModel.value.unit = formModel.value.packUnit
      }
    } else {
      if (formModel.value.packUnit) {
        formModel.value.unitType = 2
        formModel.value.unit = formModel.value.packUnit
      } else if (formModel.value.cellUnit) {
        formModel.value.unitType = 1
        formModel.value.unit = formModel.value.cellUnit
      }
    }
  }
}

const handleAdd = () => {
  stConfirmLoading.value = true
  const params = {
    oriOrderId: orderId.value,
    oriLineNo: lineNo.value,
    artId: formModel.value.artId,
    total: formModel.value.total,
    unitType: formModel.value.unitType,
    unit: formModel.value.unitType === 2 ? formModel.value.packUnit : formModel.value.cellUnit,
  }
  orderFeeAddApi(params)
    .then(() => {
      message.success('新增费用完成')
      getDataSource()
      artSelectRef.value?.init()
      clearFormModel()
    })
    .finally(() => {
      stConfirmLoading.value = false
    })
}

defineExpose({
  open,
})
</script>

<template>
  <Modal v-model:open="visible" title="新增费用" width="70%" @cancel="handleCancel" :mask-closable="false" style="top: 20px">
    <template #footer>
      <Button type="dashed" @click="handleCancel"> 关闭 </Button>
    </template>
    <div class="content-req">
      <Form ref="formRef" :model="formModel" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <Row>
          <Col :span="11">
            <Form-item label="条目" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
              <art-select ref="artSelectRef" :searchType="1" @selected="handleArtSelect" :section-id="props.sectionId" />
            </Form-item>
          </Col>
          <Col :span="4">
            <Form-item label="数量" name="total">
              <Input-number v-model:value="formModel.total" placeholder="请设置数量" :precision="4" :min="0" :max="99999999" style="width: 100%" />
            </Form-item>
          </Col>
          <Col :span="7">
            <Form-item label="单位" name="unitType">
              <Radio-group v-model:value="formModel.unitType">
                <Radio v-if="formModel.packUnit" :value="2"> {{ formModel.packUnit }}(包装) </Radio>
                <Radio v-if="formModel.cellUnit" :value="1"> {{ formModel.cellUnit }}(拆零) </Radio>
              </Radio-group>
            </Form-item>
          </Col>
          <Col :span="2" style="text-align: right">
            <Button @click="handleAdd" :loading="stConfirmLoading" type="primary"> 添加 </Button>
          </Col>
        </Row>
        <!--        <Row>-->
        <!--          <Col :span="24" v-show="articlePrice">-->
        <!--            <span m-r-4 class="art-name">{{ formModel.artCode }}</span>-->
        <!--            <span m-r-4 class="art-name">{{ formModel.artName }}</span>-->
        <!--            <span m-r-4 class="art-name" v-show="formModel.artSpec">{{ formModel.artSpec }}</span>-->
        <!--            <span m-r-4 class="art-name" v-show="formModel.producer">{{ formModel.producer }}</span>-->
        <!--            <span m-r-4 class="art-name" v-show="articlePrice">{{ filters.formatAmount(articlePrice) }}元/{{ formModel.unitType === 2 ? formModel.packUnit : formModel.cellUnit }}</span>-->
        <!--          </Col>-->
        <!--        </Row>-->
        <Table :rowKey="(record: any) => record.keyStr" :columns="columns" :data-source="dataSource" :scroll="{ y: 'calc(100vh - 270px)' }">
          <template #bodyCell="{ text, column, record, index }">
            <template v-if="column.dataIndex === 'artDesc'">
              <div>
                <span class="art-name">{{ record.artName }}</span
                ><span>{{ record.artSpec }}</span>
              </div>
              <div v-if="record.producer">{{ record.producer }}</div>
            </template>
            <template v-else-if="column.dataIndex === 'series'">
              {{ index + 1 }}
            </template>
            <template v-else-if="column.dataIndex === 'unitPrice'">
              {{ record.unitPrice ? record.unitPrice : '暂未设置' }}
            </template>
            <template v-else-if="['total', 'amount'].includes(column.dataIndex)">
              <span v-if="text > 0">{{ text }}</span>
              <span v-else class="total-le0">{{ text }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'manualFlag'">
              <Badge v-if="record.manualFlag === 1" status="warning" text="补录" />
            </template>
          </template>
        </Table>
      </Form>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.content-req {
  height: calc(100vh - 170px);
  overflow: auto;
}
.art-name {
  font-weight: bold;
}
.total-gt0 {
  color: #018338;
}
.total-le0 {
  color: #ff0000;
}
</style>
