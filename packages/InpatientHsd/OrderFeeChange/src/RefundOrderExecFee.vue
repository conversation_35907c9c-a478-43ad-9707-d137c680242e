<script setup lang="ts">
import {DoubleRightOutlined} from '@ant-design/icons-vue'
import { Table, Button, Modal, InputNumber, message } from 'ant-design-vue'
import filters from './utils/filters.ts'
import type { TableColumnType } from 'ant-design-vue'
import { orderItemAUPSumFeeLsApi, orderFeeRefundLsApi } from '@mh-inpatient-hsd/util'

const columns: TableColumnType[] = [
  {
    title: '项目编号',
    dataIndex: 'artId',
    width: 90
  },
  {
    title: '医保项目编码',
    dataIndex: 'miCode',
    align: 'left',
    width: 200,
    ellipsis: true
  },
  {
    title: '项目内容',
    dataIndex: 'artDesc',
    align: 'left',
    ellipsis: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    align: 'center',
    width: 80
  },
  {
    title: '单价',
    dataIndex: 'price',
    align: 'right',
    width: 90
  },
  {
    title: '初始数量',
    dataIndex: 'oriTotal',
    align: 'right',
    width: 90
  },
  {
    title: '当前数量',
    dataIndex: 'curTotal',
    align: 'right',
    width: 90
  },
  {
    title: '冲红数量',
    dataIndex: 'refundTotal',
    align: 'center',
    width: 150
  }
]

const emit = defineEmits(['refunded'])
const visible = ref(false);
const confirmLoading = ref<boolean>(false)
const dataSource = ref<any>([])
const inputRefundKey = ref()
const inputRef = ref({})
const orderId = ref()
const lineNo = ref()

const open = (initOrderId: any, initLineNo: any) => {
  orderId.value = initOrderId
  lineNo.value = initLineNo
  getDataSource()
  visible.value = true
}

const getDataSource = async () => {
  dataSource.value = []
  orderItemAUPSumFeeLsApi({orderId: orderId.value, lineNo: lineNo.value}).then((list: any) => {
    if (list) {
      dataSource.value = list
    }
  })
}

const disableRefundBtn = computed(() => {
  if (dataSource.value && dataSource.value.length > 0) {
    const refundRows = dataSource.value.filter((item: any) => item.refundTotal > 0)
    return refundRows.length === 0
  }
  return true
})

function handleCancel() {
  visible.value = false
  emit('refunded')
}

function handleRefundAll() {
  dataSource.value.forEach((item: any) => {
    if (item.curTotal > 0) {
      item.refundTotal = item.curTotal
    }
  })
}

const handleRefundOe = () => {
  if (disableRefundBtn.value) {
    message.warning('请设置待冲红数量')
    return
  }
  const refundLs = dataSource.value.filter((item: any) => item.refundTotal > 0)
  confirmLoading.value = true
  const params = {
    srcOrderId: orderId.value,
    srcLineNo: lineNo.value,
    refundLs: refundLs
  }
  orderFeeRefundLsApi(params).then(() => {
    message.success('费用冲红完成')
    getDataSource()
  }).finally(() => {
    confirmLoading.value = false
  })
}

const handleSetRowRefundTotal = (keyStr: String) => {
  const row = dataSource.value.find(item => item.keyStr === keyStr)
  if (row) {
    row.refundTotal = row.curTotal
  }
}

const onEnterRefundTotal = (refundTotal: number, keyStr: String) => {
  inputRefundKey.value = getKey(refundTotal, keyStr)
  onChangeRefundTotal(refundTotal, keyStr, true)
}

const onChangeRefundTotal = (refundTotal: number, keyStr: String, enter?: boolean) => {
  const key = getKey(refundTotal, keyStr)
  if (!enter && inputRefundKey.value === key) {
    return
  } else {
    inputRefundKey.value = key
  }
  const sumFee = dataSource.value.find(item => item.keyStr === keyStr)
  if (refundTotal && refundTotal >= 0) {
    if (!sumFee) {
      message.warning('未匹配到划价单明细')
      return
    }
    if (sumFee.curTotal > 0) {
      if (refundTotal > sumFee.curTotal) {
        message.warning('不能超出原划价单数量')
        sumFee.refundTotal = sumFee.curTotal
        const refundInput = inputRef.value['refund_' + keyStr]
        if (refundInput) {
          refundInput.focus()
        }
      } else {
        sumFee.refundTotal = refundTotal
      }
    }
  } else if (refundTotal !== 0) {
    sumFee.refundTotal = undefined
  }
}

const getKey = (refundTotal: any, keyStr: String) => {
  let value = refundTotal
  if (filters.isNumber(refundTotal)) {
    value = Number(refundTotal).toFixed(4)
  }
  return keyStr + '_' + value
}

const setInputRef = (key: any, ref: any) => {
  inputRef.value[key] = ref
}

defineExpose({
  open
})
</script>

<template>
  <Modal v-model:open="visible" title="执行费用冲红" width="1300px" @cancel="handleCancel" :mask-closable="false" style="top: 20px">
    <template #footer>
      <Button type="dashed" @click="handleCancel">
        关闭
      </Button>
      <Button @click="handleRefundOe" :loading="confirmLoading" type="primary" danger :disabled="disableRefundBtn">
        费用冲红
      </Button>
    </template>
    <div class="content-req">
      <div class="op-btn">
        <Button @click="handleRefundAll" danger :disabled="!dataSource || dataSource.length === 0">
          <template #icon>
            <DoubleRightOutlined/>
          </template>
          全部冲红
        </Button>
      </div>
      <Table
        :rowKey="(record: any) => record.keyStr"
        :columns="columns"
        :has-surely="false"
        :data-source="dataSource"
        :scroll="{ y: 'calc(100vh - 240px)'}">
        <template #bodyCell="{ text, column, record, index }">
          <template v-if="column.dataIndex === 'artDesc'">
            <div>
              <span class="art-name">{{ record.artName }}</span><span>{{ record.artSpec }}</span>
            </div>
            <div v-if="record.producer">{{ record.producer }}</div>
          </template>
          <template v-if="column.dataIndex === 'refundTotal'">
            <Input-number
                v-model:value="record.refundTotal"
                placeholder="请录入冲红数量"
                :min="0"
                :max="record.curTotal > 0 ? record.curTotal : 0"
                style="width: 95%;"
                :ref="el => setInputRef('refund_' + record.keyStr, el)"
                @blur="onChangeRefundTotal($event.target.value, record.keyStr)"
                @pressEnter="onEnterRefundTotal($event.target.value, record.keyStr)"/>
          </template>
          <template v-if="column.dataIndex === 'curTotal'">
            <span v-if="text > 0">{{ text }}</span>
            <span v-else class="total-le0">{{ text }}</span>
            <DoubleRightOutlined v-if="text > 0" @click="handleSetRowRefundTotal(record.keyStr)" m-l-1 class="total-click"/>
          </template>
          <template v-else-if="['oriTotal', 'amount'].includes(column.dataIndex)">
            <span v-if="text > 0">{{ text }}</span>
            <span v-else class="total-le0">{{ text }}</span>
          </template>
        </template>
      </Table>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.content-req {
  height: calc(100vh - 170px);
  overflow: auto;
}
.art-name {
  font-weight: bold;
}
.total-gt0 {
  color: #018338;
}
.total-le0 {
  color: #ff0000;
}
.total-click {
  color: #ff6666;
  cursor: pointer;
}
.same-art-feeLs {
  font-weight: bold;
  font-size: 20px;
  padding: 20px 0 10px 5px;
}
.op-btn {
  text-align: right;
  button {
    margin-right: 10px;
  }
}
</style>
