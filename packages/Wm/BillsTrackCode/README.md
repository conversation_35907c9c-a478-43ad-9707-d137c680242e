# BillsTrackCode 多单据追溯码扫描组件

多单据追溯码扫描组件，用于录入和管理多个单据药品的追溯码信息。该组件基于 RecipeTrackCode 处方扫码组件构建，专门处理多个单据的批量追溯码采集。

## 特性

- 支持多个单据的批量追溯码采集
- 支持通过扫码枪或高拍仪录入追溯码
- 支持追溯码有效性验证
- 支持拆零药品的追溯码录入
- 支持采集结果状态显示
- 智能药品汇总：按条目类别和拆零/整包类型自动汇总
- 基于ant-design-vue的组件实现

## 依赖

- `ant-design-vue`: UI组件库
- `@mh-wm/util`: 工具库，提供API调用

## 核心概念

### 多单据处理逻辑

与单一处方不同，多单据追溯码扫描需要处理来自不同单据的药品明细。由于不同单据中的明细必然存在 `artId` 有交集的部分，组件采用智能汇总策略：

#### 汇总规则

药品明细按以下规则进行汇总：

1. **拆零类汇总条件**：
   - `artId + totalCells > 0` 或
   - `totalPacks > 0` 且标记了 `artIsDisassembled` 允许拆零上报

2. **整包类汇总条件**：
   - `totalPacks > 0` 且不被标记 `artIsDisassembled` 拆零上报

#### 汇总后排序

每个条目按上面的汇总后最多出现一列拆零一列整包，大多数只会有一列，再根据以下顺序排列：
1. `artTypeId` 升序
2. `artId` 升序
3. 有整包的优先
4. 有拆零的次之

#### 追溯码绑定策略

- **左侧列表**：显示汇总后的药品条目，根据汇总后的整包数拆零数进行追溯码绑定
- **右侧列表**：显示与汇总条目关联的追溯码，但调用 `addCodeApi`、`delCodeApi` 时需要细分成原始的明细主键 `wbSeqid` 和 `lineNo`
- **批量删除**：`delAllCodeApi` 能接收 `{ wbSeqids: wbSeqIdLs }`

### 重要数据结构

#### 单据明细 (billDetail)

每个单据明细包含以下关键属性：

- `wbSeqid`: 业务流水号，对应入参单据id列表中的一个
- `lineNo`: 单据明细的行号，表示这个药品在单据的第几行
- `artId`: 条目编号
- `artTypeId`: 条目类别
- `artName`: 药品名称
- `totalPacks`: 整包数量
- `totalCells`: 拆零数量
- `artIsDisassembled`: 是否允许拆零上报

#### 汇总后的数据结构

汇总后的左侧列表每行包含：

- 原始明细的所有属性
- `wbSeqid`: **必须返回**，用于与右侧追溯码列表关联
- `sourceDetails`: 原始明细列表，用于API调用时拆分
- `aggregatedTotalPacks`: 汇总后的整包数量
- `aggregatedTotalCells`: 汇总后的拆零数量

### 拆零系数

拆零系数（packCells）记录整包与拆零数量的转换系数。例如药品规格为1盒里装5支，packCells=5，表示1盒药（整包totalPacks）= 5支（拆零数totalCells）。

### 追溯码

每盒药都有一个唯一的追溯码，用于药品追溯管理。药品追溯码的长度通常在19位到27位之间，小于19位的码（如69开头的13位条形码）不是有效的追溯码，组件会给出警告提示，不能用于业务绑定。

市面上常见的是20位溯源码，其中前7位是药品标识，能标识一个药品的唯一性，与系统中的artId（条目编号）能够对应。未来数据健全后，组件将支持校验artId和追溯码前7位的对应关系，确保追溯码与药品匹配。

追溯码前7位是药品的唯一标志，一种药需要发5盒的话，这个药扫进来的五个码前7位应该是相同的。

### 追溯码拆零标志

口服药一般按整盒发药，注射药有1盒多支的情况，多支拆零数共用1盒的一个溯源码，但会记录拆零标志位。

### 追溯码扫描

在摆药时扫描一个或多个溯源码对应一条医嘱的需求量。住院的注射药按拆零数执行，可能出现多支注射药扫同一个码的情况，也可能出现一个患者医嘱的用药需求跨多个追溯码的情况。

药品数量分为整包数量(totalPacks)和拆零数量(totalCells)两种计量方式：
- 整包数量：以药品包装单位计量，如"盒"、"瓶"等
- 拆零数量：以药品最小单位计量，如"片"、"支"等

扫描追溯码时，系统会根据药品的整包数量和拆零数量进行响应。例如，如果一种药品需要发5片，而一盒有10片，则可能出现以下情况：
1. 使用一个追溯码，设置拆零数量为5
2. 如果上一盒只剩3片，则需要使用两个追溯码，一个设置拆零数量为3，另一个设置拆零数量为2

### 外部设备

追溯码的扫描通过外部设备（扫码枪或高拍仪）进行，识别后将文本传到组件里。扫码枪扫一个码将文本写入并触发回车事件，高拍仪可能一个追溯码对应一个回车事件，在文本文档查看的效果就是5个追溯码5行。

## 打包与安装

### 打包组件

在开发完成后，需要打包组件以便发布和使用：

```bash
# 在项目根目录下执行打包命令
pnpm publish:component Wm/BillsTrackCode
```

这个命令会执行以下操作：
1. 编译组件源码
2. 生成类型声明文件
3. 打包CSS样式
4. 生成组件包到dist目录
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库

### 安装组件

在其他项目中安装该组件：

```bash
# 使用npm安装
npm install @mh-wm/bills-track-code

# 或使用pnpm安装
pnpm add @mh-wm/bills-track-code
```

### package.json依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-wm/bills-track-code": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}
```

## 使用方法

### 引入组件

```javascript
import { BillsTrackCode } from '@mh-wm/bills-track-code'
import '@mh-wm/bills-track-code/index.css'
```

### 基本用法 - 按钮触发

```vue
<template>
  <!-- 基础用法 - 显示按钮 -->
  <BillsTrackCode
    :wbSeqIdLs="wbSeqIdLs"
    @success="handleSuccess"
  />
</template>

<script setup>
import { BillsTrackCode } from '@mh-wm/bills-track-code'
import '@mh-wm/bills-track-code/index.css'
import { ref } from 'vue'

// 单据ID列表
const wbSeqIdLs = ref([12345, 12346, 12347])

// 成功回调
const handleSuccess = (data) => {
  console.log('多单据扫码录入成功', data)
}
</script>
```

### 通过js方法调用

```vue
<template>
  <!-- 不显示按钮，通过js方法调用 -->
  <BillsTrackCode
    ref="billsTrackCodeRef"
    :showButton="false"
    @success="handleSuccess"
  />

  <!-- 自定义按钮 -->
  <Button @click="openBillsTrackCode">打开多单据扫码录入</Button>
</template>

<script setup>
import { BillsTrackCode } from '@mh-wm/bills-track-code'
import '@mh-wm/bills-track-code/index.css'
import { Button } from 'ant-design-vue'
import { ref } from 'vue'

// 组件引用
const billsTrackCodeRef = ref()

// 单据ID列表
const wbSeqIdLs = ref([12345, 12346, 12347])

// 打开多单据扫码录入窗口
const openBillsTrackCode = () => {
  billsTrackCodeRef.value.open(wbSeqIdLs.value)
}

// 成功回调
const handleSuccess = (data) => {
  console.log('多单据扫码录入成功', data)
}
</script>
```

## 组件属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| buttonText | 按钮文本，为空时自动生成 | string | '' |
| buttonType | 按钮类型 | 'primary' \| 'ghost' \| 'dashed' \| 'link' \| 'text' \| 'default' | 'default' |
| buttonSize | 按钮大小 | 'large' \| 'middle' \| 'small' | 'middle' |
| modalWidth | 对话框宽度 | number \| string | 1200 |
| showButton | 是否显示触发按钮 | boolean | true |
| wbSeqIdLs | 单据ID列表 | number[] | [] |
| hotkey | 绑定的键盘快捷键 | string | 'F7' |
| enableAutoClosePrompt | 是否启用自动关闭提示 | boolean | true |

## 组件事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| success | 多单据扫码录入成功时触发 | (data: any) => void |
| cancel | 取消多单据扫码录入时触发 | () => void |

## 组件方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| open | 打开多单据扫码录入窗口 | (wbSeqIdLs?: number[]) => void |
| close | 关闭多单据扫码录入窗口 | () => void |

## 开发与使用流程

### 开发流程

1. **组件开发**：在`packages/Wm/BillsTrackCode/src`目录下开发组件
   ```
   packages/
   └── Wm/
       └── BillsTrackCode/
           ├── src/
           │   ├── index.vue      # 主组件
           │   └── ...            # 其他子组件或工具
           ├── index.ts           # 组件入口文件
           ├── package.json       # 组件包配置
           └── README.md          # 组件文档
   ```

2. **本地测试**：在`src/views/wm/examples`目录下创建示例页面进行测试
   ```vue
   <!-- src/views/wm/examples/BillsTrackCodeExample.vue -->
   <template>
     <div>
       <h1>多单据追溯码扫描组件示例</h1>
       <BillsTrackCode
         ref="billsTrackCodeRef"
         :modalWidth="1400"
         @success="handleSuccess"
         @cancel="handleCancel"
       />
       <Button type="primary" @click="openTrackCode">打开多单据扫码</Button>
     </div>
   </template>

   <script setup>
   import { ref } from 'vue'
   import { Button, message } from 'ant-design-vue'
   import BillsTrackCode from '@/packages/Wm/BillsTrackCode/src/index.vue'

   const billsTrackCodeRef = ref()

   const openTrackCode = () => {
     // 示例参数：多个单据ID
     const wbSeqIdLs = [123456, 123457, 123458]

     billsTrackCodeRef.value.open(wbSeqIdLs)
   }

   const handleSuccess = (data) => {
     message.success(`多单据扫码成功，处理了 ${data.length} 个单据`)
   }

   const handleCancel = () => {
     message.info('取消多单据扫码')
   }
   </script>
   ```

3. **打包组件**：开发完成后，执行打包命令
   ```bash
   pnpm publish:component Wm/BillsTrackCode
   ```

### 使用流程

1. **安装组件**：在项目中安装组件
   ```bash
   pnpm add @mh-wm/bills-track-code
   ```

2. **引入组件**：在Vue文件中引入组件
   ```vue
   <template>
     <div>
       <BillsTrackCode
         ref="billsTrackCodeRef"
         :modalWidth="1400"
         @success="handleSuccess"
         @cancel="handleCancel"
       />
       <Button type="primary" @click="openTrackCode">打开多单据扫码</Button>
     </div>
   </template>

   <script setup>
   import { ref } from 'vue'
   import { Button, message } from 'ant-design-vue'
   import { BillsTrackCode } from '@mh-wm/bills-track-code'
   import '@mh-wm/bills-track-code/index.css'

   const billsTrackCodeRef = ref()

   const openTrackCode = () => {
     // 实际业务参数：多个单据ID
     const wbSeqIdLs = [123456, 123457, 123458] // 实际业务中的单据ID列表

     billsTrackCodeRef.value.open(wbSeqIdLs)
   }

   const handleSuccess = (data) => {
     message.success(`多单据扫码成功，处理了 ${data.length} 个单据`)
     // 在这里处理成功后的业务逻辑
   }

   const handleCancel = () => {
     message.info('取消多单据扫码')
     // 在这里处理取消后的业务逻辑
   }
   </script>
   ```

## 注意事项

1. **多单据汇总逻辑**：组件会自动将多个单据中相同 `artId` 的药品进行汇总，按拆零/整包类型分别处理。
2. **追溯码长度验证**：组件会验证扫描的追溯码长度，只有19位到27位之间的码才被视为有效追溯码。
3. **追溯码与药品匹配**：追溯码前7位是药品标识，组件会校验追溯码与药品的匹配关系。
4. **原始明细映射**：虽然左侧显示汇总后的数据，但API调用时会自动拆分回原始的 `wbSeqid` 和 `lineNo`。
5. **智能药品切换**：当满足以下条件时，组件会自动开始扫描下一种药品：
   - 当前药品的整包数量和拆零数量都已满足
   - 扫描的追溯码没有被其他药品绑定
6. **异常处理**：当一种药品需要扫描多盒但扫描出了多种不同前7位的追溯码时，组件会给出颜色提醒。
7. **拆零上报管理**：支持"拆零上报"功能，适用于大输液等一箱一码的情况。
8. **无追溯码管理**：支持将药品设置为"无追溯码"状态。
9. **高拍仪优化**：支持高拍仪快速连续扫码，异步处理追溯码数据。
10. **自动聚焦**：每次进入页面和操作完成后都会自动聚焦到追溯码输入框。

## 与 RecipeTrackCode 的主要区别

| 特性 | RecipeTrackCode | BillsTrackCode |
| --- | --- | --- |
| 处理范围 | 单一处方 | 多个单据 |
| 入参 | `(title, wbSeqid, visitId, isView)` | `(wbSeqIdLs)` |
| 数据汇总 | 无需汇总 | 按 artId + 拆零/整包类型汇总 |
| API调用 | 单个 wbSeqid | 数组 wbSeqids |
| 左侧列表 | 直接显示明细 | 显示汇总后的数据 |
| 追溯码关联 | lineNo | wbSeqid + lineNo |
| 快捷键 | F6 | F7 |

## 版本历史

### v1.0.0 (初始版本)
- 基于 RecipeTrackCode v1.2.0 构建
- 支持多单据批量追溯码采集
- 智能药品汇总：按条目类别和拆零/整包类型自动汇总
- 支持原始明细映射，API调用时自动拆分
- 支持所有 RecipeTrackCode 的核心功能：
  - 追溯码扫描、验证、绑定
  - 拆零药品管理
  - 追溯码长度验证(19-27位)
  - 智能药品匹配
  - 全部清除功能
  - 自动完成提示
  - 无追溯码管理
  - 拆零上报管理
  - 高拍仪支持
