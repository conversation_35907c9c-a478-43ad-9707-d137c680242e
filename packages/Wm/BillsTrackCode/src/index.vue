<!--多单据追溯码扫描组件-->
<script setup lang="ts">
import { reactive, ref, computed, nextTick, h } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'

import {
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Checkbox,
  Select,
  Row,
  Col,
  Table,
  Space,
  message,
  Tooltip,
  Switch
} from 'ant-design-vue'
import { createVNode } from 'vue'
import { ExclamationCircleOutlined, CheckCircleOutlined } from '@ant-design/icons-vue'

// 导入API，使用原始API名称
import {
  wmBillDetailListApi,
  trackCodeListByWbSeqIdsApi,
  trackCodeAddCodeApi as addCodeApi,
  trackCodeDelCodeApi as delCodeApi,
  trackCodeDelCodeByWbSeqIdApi as delAllCodeApi,
  trackCodeAllConfirmByWbSeqIdApi,
  trackCodeSaveBillApi as saveBillApi,
  trackCodeSubmitBillApi as submitBillApi,
  trackCodeSetNoTrackCodeApi as setNoTrackCodeApi,
  trackCodeClearNoTrackCodeApi,
  trackCodeSetDisassembledApi,
  trackCodeClearDisassembledApi
} from '@mh-wm/util'

const props = defineProps({
  // 对话框宽度
  modalWidth: {
    type: [Number, String],
    default: 1400,
  },
  // 是否只添加识别追溯码（默认为false，即未识别的追溯码也会绑定到当前选中行）
  onlyAddRecognizedTrackCode: {
    type: Boolean,
    default: false,
  },
  // 是否启用"只添加识别追溯码"复选框（默认为false，即不显示复选框）
  enableOnlyAddRecognizedTrackCodeOption: {
    type: Boolean,
    default: false,
  },
  // 是否启用自动关闭提示（默认为true，即显示自动关闭提示）
  enableAutoClosePrompt: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['success', 'cancel'])
const modalVisible = ref(false)

// 库存变化类型(101-调拨入库,102-调拨出库,103-报溢入库,104-报损出库,105-销毁出库,106-其他入库,107-其他出库,108-采购入库,109-采购退出,110-赠品入库,111-赠品退出)
const inventoryChangeTypeLs = reactive([
  {
    label: '调拨入库',
    value: 101
  }, {
    label: '调拨出库',
    value: 102
  }, {
    label: '报溢入库',
    value: 103
  }, {
    label: '报损出库',
    value: 104
  }, {
    label: '销毁出库',
    value: 105
  }, {
    label: '其他入库',
    value: 106
  }, {
    label: '其他出库',
    value: 107
  }, {
    label: '采购入库',
    value: 108
  }, {
    label: '采购退出',
    value: 109
  }, {
    label: '赠品入库',
    value: 110
  }, {
    label: '赠品退出',
    value: 111
  }
])

const formRef = ref()
const formState = reactive({
  wbSeqIdLs: [] as number[], // 多个单据ID列表
  title: '多单据追溯码采集',
  inventoryChangeType: undefined
})

const rules: Record<string, Rule[]> = {
  bsnType: [
    { required: true, message: '请选择业务类型', trigger: 'change' }
  ],
  custId: [
    { required: true, message: '请选择往来单位', trigger: 'change' }
  ],
  custName: [
    { required: true, message: '请选择往来单位', trigger: 'change' }
  ],
  bsnDate: [
    { required: true, message: '请选择业务日期', trigger: 'change' }
  ]
}

interface tableModel {
  loading?: boolean,
  columns: any[],
  dataSource: any[],
  allTrackCodes?: any[], // 存储所有追溯码数据
  selectedRowKeys?: any[],
  rowSelection?: any,
  pagination: any,
  loadDataSource?: () => Promise<void>,
  selectRow?: (record: any) => void,
  customRow?: (record: any) => any
}

const currentAggregatedKey = ref() // 当前选中的汇总键
const currentArtInfo = reactive({
  artName: '',
  artSpec: '',
  producer: '',
  noTrackCode: false,
  artIsDisassembled: false, // 拆零上报
  trackCodePrefix: '', // 追溯码前缀（前7位）
  scannedPrefixes: new Set(), // 已扫描的追溯码前缀集合
  totalPacks: 0, // 整包数量
  totalCells: 0, // 拆零数量
  collectedPacks: 0, // 已采整包数
  collectedCells: 0, // 已采拆零数
  packCells: 0, // 每包拆零数量
  sourceDetails: [] as any[] // 原始明细列表
})

// 追溯码明细表格
const trackCodeDetailTableModel = reactive<tableModel>({
  loading: false,
  columns: [{
    title: '药品追溯码',
    dataIndex: 'trackCode',
    width: 200,
    resizable: true,
    customRender: ({ text, record }) => {
      // 获取追溯码前7位
      const prefix = text.substring(0, 7)

      // 获取当前药品的所有前缀
      const drugPrefixes = new Set()
      trackCodeDetailTableModel.allTrackCodes?.forEach((item: any) => {
        if (item.aggregatedKey === record.aggregatedKey) {
          drugPrefixes.add(item.trackCode.substring(0, 7))
        }
      })

      // 根据前缀数量确定颜色
      let color = '#52c41a' // 绿色（默认，只有一种前缀）
      if (drugPrefixes.size === 2) {
        color = '#faad14' // 橙色（两种前缀）
      } else if (drugPrefixes.size > 2) {
        color = '#f5222d' // 红色（三种及以上前缀）
      }

      // 创建带颜色的前缀和普通的后缀
      return h('span', {}, [
        h('span', { style: { color, fontWeight: 'bold' } }, prefix),
        h('span', {}, text.substring(7))
      ])
    }
  }, {
    title: '拆零',
    dataIndex: 'isDisassembled',
    width: 80,
    align: 'center'
  }, {
    title: '拆零数量',
    dataIndex: 'totalCells',
    width: 80,
    align: 'right',
    customRender: ({ text }) => {
      // 拆零数量为0不显示
      return text > 0 ? text : ''
    }
  }, {
    title: '单据流水号',
    dataIndex: 'wbSeqid',
    width: 120,
    align: 'right'
  }, {
    title: '行号',
    dataIndex: 'lineNo',
    width: 80,
    align: 'right'
  }, {
    title: '操作',
    dataIndex: 'action',
    width: 60,
    fixed: 'right',
    align: 'center'
  }],
  dataSource: [],
  allTrackCodes: [], // 存储所有追溯码数据
  loadDataSource: async () => {
    trackCodeDetailTableModel.loading = true
    try {
      const response = await trackCodeListByWbSeqIdsApi({
        wbSeqids: formState.wbSeqIdLs,
      })
      // 存储所有追溯码数据，并添加汇总键
      trackCodeDetailTableModel.allTrackCodes = (response || []).map((item: any) => ({
        ...item,
        aggregatedKey: generateAggregatedKey(item)
      }))
      // 通过计算属性过滤当前药品的追溯码
      updateFilteredTrackCodes()
      trackCodeDetailTableModel.selectedRowKeys = []
      trackCodeDetailTableModel.loading = false

      // 更新追溯码缓存
      updateTrackCodeCache()
    } catch (err) {
      console.log(err)
      trackCodeDetailTableModel.loading = false
    }
  },
  pagination: false
})

// 生成汇总键的函数
const generateAggregatedKey = (item: any) => {
  const isDisassembledType = (item.totalCells > 0) || (item.totalPacks > 0 && item.artIsDisassembled)
  const type = isDisassembledType ? 'cells' : 'packs'
  return `${item.artId}_${type}`
}

// 单据明细汇总后的数据
const aggregatedBillDetails = computed(() => {
  const aggregationMap = new Map()

  // 遍历所有原始明细进行汇总
  billDetailTableModel.dataSource.forEach((detail: any) => {
    const aggregatedKey = generateAggregatedKey(detail)

    if (aggregationMap.has(aggregatedKey)) {
      const existing = aggregationMap.get(aggregatedKey)
      // 累加数量
      existing.aggregatedTotalPacks += detail.totalPacks || 0
      existing.aggregatedTotalCells += detail.totalCells || 0
      existing.collectedPacks += detail.collectedPacks || 0
      existing.collectedCells += detail.collectedCells || 0
      // 添加到原始明细列表
      existing.sourceDetails.push(detail)
    } else {
      // 创建新的汇总项
      const isDisassembledType = (detail.totalCells > 0) || (detail.totalPacks > 0 && detail.artIsDisassembled)
      aggregationMap.set(aggregatedKey, {
        ...detail,
        aggregatedKey,
        aggregatedTotalPacks: detail.totalPacks || 0,
        aggregatedTotalCells: detail.totalCells || 0,
        sourceDetails: [detail],
        isDisassembledType
      })
    }
  })

  // 转换为数组并排序
  const result = Array.from(aggregationMap.values()).sort((a, b) => {
    // 按 artTypeId 升序
    if (a.artTypeId !== b.artTypeId) {
      return a.artTypeId - b.artTypeId
    }
    // 按 artId 升序
    if (a.artId !== b.artId) {
      return a.artId - b.artId
    }
    // 整包优先，拆零次之
    if (a.isDisassembledType !== b.isDisassembledType) {
      return a.isDisassembledType ? 1 : -1
    }
    return 0
  })

  return result
})

// 单据明细表格（显示汇总后的数据）
const billDetailTableModel = reactive<tableModel>({
  loading: false,
  columns: [{
    title: '品名|规格|厂家',
    dataIndex: 'artName',
    width: 300,
    fixed: 'left',
    resizable: true
  }, {
    title: '整包数',
    dataIndex: 'aggregatedTotalPacks',
    width: 70,
    fixed: 'left',
    align: 'right',
    customRender: ({ text }) => {
      // 整包数为0或不存在时不显示
      return text > 0 ? text : ''
    }
  }, {
    title: '拆零数',
    dataIndex: 'aggregatedTotalCells',
    width: 70,
    fixed: 'left',
    align: 'right',
    customRender: ({ text }) => {
      // 拆零数为0或不存在时不显示
      return text > 0 ? text : ''
    }
  }, {
    title: '已采整包',
    dataIndex: 'collectedPacks',
    width: 80,
    fixed: 'left',
    align: 'right',
    customRender: ({ text, record }) => {
      // 整包数为0或不存在时不显示已采整包
      if (record.aggregatedTotalPacks <= 0) return ''

      // 已采数量为0时不显示
      if (!text || text <= 0) return ''

      // 计算是否已完成采集
      const isPacksComplete = record.aggregatedTotalPacks > 0 ? (record.collectedPacks >= record.aggregatedTotalPacks) : true

      // 创建带动画效果的数字
      return h('span', {
        class: 'collected-count',
        style: {
          color: isPacksComplete ? '#52c41a' : 'inherit',
          animation: record.animatePacks ? 'count-change 0.5s' : 'none'
        }
      }, text)
    }
  }, {
    title: '已采拆零',
    dataIndex: 'collectedCells',
    width: 80,
    fixed: 'left',
    align: 'right',
    customRender: ({ text, record }) => {
      // 拆零数为0且没有设置拆零上报时不显示已采拆零
      if (record.aggregatedTotalCells <= 0 && !record.artIsDisassembled) return ''

      // 已采数量为0时不显示
      if (!text || text <= 0) return ''

      // 计算是否已完成采集
      let isCellsComplete = false

      // 如果设置了拆零上报标志，只要有拆零采集就算完成
      if (record.artIsDisassembled) {
        isCellsComplete = record.collectedCells > 0
      } else {
        // 否则按正常逻辑检查
        isCellsComplete = record.aggregatedTotalCells > 0 ? (record.collectedCells >= record.aggregatedTotalCells) : true
      }

      // 创建带动画效果的数字
      return h('span', {
        class: 'collected-count',
        style: {
          color: isCellsComplete ? '#52c41a' : 'inherit',
          animation: record.animateCells ? 'count-change 0.5s' : 'none'
        }
      }, text)
    }
  }, {
    title: '采集结果',
    dataIndex: 'collectStatus',
    width: 80,
    fixed: 'left',
    align: 'center',
    customRender: ({ record }) => {
      // 如果设置为无追溯码，直接显示无追溯码状态
      if (record.noTrackCode) {
        return h('span', { style: { color: '#722ed1', fontWeight: 'bold' } }, '无追溯码')
      }

      // 如果设置了拆零上报标志
      if (record.artIsDisassembled) {
        // 如果是拆零上报且totalPacks>0，则要求拆零数合计等于totalPacks
        if (record.aggregatedTotalPacks > 0 && record.packCells === 1) {
          if (record.collectedCells >= record.aggregatedTotalPacks) {
            return h('span', { style: { color: '#52c41a' } }, '✓ 拆零上报')
          } else if (record.collectedCells > 0) {
            return h('span', { style: { color: '#1890ff' } }, `拆零上报中(${record.collectedCells}/${record.aggregatedTotalPacks})`)
          } else {
            return h('span', { style: { color: '#1890ff' } }, '待拆零上报')
          }
        } else {
          // 否则只要有拆零数量即可
          if (record.collectedCells > 0) {
            return h('span', { style: { color: '#52c41a' } }, '✓ 拆零上报')
          } else {
            return h('span', { style: { color: '#1890ff' } }, '待拆零上报')
          }
        }
      }

      // 计算是否已完成采集
      const isPacksComplete = record.aggregatedTotalPacks > 0 ? (record.collectedPacks >= record.aggregatedTotalPacks) : true
      const isCellsComplete = record.aggregatedTotalCells > 0 ? (record.collectedCells >= record.aggregatedTotalCells) : true
      const isComplete = isPacksComplete && isCellsComplete

      // 计算是否有采集
      const hasCollected = (record.collectedPacks > 0 || record.collectedCells > 0)

      // 未采集
      if (!hasCollected) {
        return h('span', { style: { color: '#999' } }, '未采集')
      }

      // 采集完且正常
      if (isComplete && !record.hasMultiplePrefixes) {
        return h('span', { style: { color: '#52c41a' } }, '✓ 已完成')
      }

      // 采集中
      if (!isComplete && !record.hasMultiplePrefixes) {
        return h('span', { style: { color: '#1890ff' } }, '采集中')
      }

      // 采集异常（超量或有多个前缀）
      const isOverCollected = (record.aggregatedTotalPacks > 0 && record.collectedPacks > record.aggregatedTotalPacks) ||
                             (record.aggregatedTotalCells > 0 && record.collectedCells > record.aggregatedTotalCells)

      if (isOverCollected || record.hasMultiplePrefixes) {
        // 创建带提示的异常状态
        return h(
          'Tooltip',
          {
            title: record.hasMultiplePrefixes
              ? '该药品采集了多种不同前缀的追溯码，请检查'
              : '采集数量超过了应采数量，请检查'
          },
          [h('span', { style: { color: '#faad14', cursor: 'pointer' } }, '⚠ 异常')]
        )
      }

      return null
    }
  }, {
    title: '生产批号',
    dataIndex: 'batchNo',
    width: 110,
    resizable: true
  }, {
    title: '有效期至',
    dataIndex: 'expiry',
    width: 100,
    resizable: true,
    align: 'center'
  }, {
    title: '单位',
    dataIndex: 'packCells',
    width: 90,
    resizable: true,
    align: 'right'
  }, {
    title: '批次',
    dataIndex: 'stockNo',
    width: 80,
    resizable: true
  }],
  dataSource: [],
  loadDataSource: async () => {
    billDetailTableModel.loading = true
    try {
      // 调用API获取所有单据的明细
      const response = await wmBillDetailListApi({
        wbSeqids: formState.wbSeqIdLs, // 传递数组参数
        setProdNo: true,
      })

      // 确保每个明细都有wbSeqid属性
      const allDetails = (response || []).map((detail: any) => ({
        ...detail,
        // 如果API返回的数据中没有wbSeqid，需要根据业务逻辑补充
        // 这里假设API已经返回了正确的wbSeqid
      }))

      billDetailTableModel.dataSource = allDetails
      billDetailTableModel.selectedRowKeys = []

      // 自动选择第一个非无追溯码的汇总项
      const aggregated = aggregatedBillDetails.value
      if (aggregated.length > 0) {
        const firstValidItem = aggregated.find(p => !p.noTrackCode)
        if (firstValidItem) {
          billDetailTableModel.selectRow(firstValidItem)
        }
      }
      billDetailTableModel.loading = false
    } catch (err) {
      console.log(err)
      billDetailTableModel.loading = false
    }
  },
  selectedRowKeys: [],
  rowSelection: computed(() => {
    return {
      type: 'radio',
      selectedRowKeys: billDetailTableModel.selectedRowKeys,
      onChange: (selectedRowKeys: any) => {
        billDetailTableModel.selectedRowKeys = selectedRowKeys
      },
    }
  }),
  selectRow: (record) => {
    // 重置追溯码前缀信息
    currentArtInfo.trackCodePrefix = ''
    currentArtInfo.scannedPrefixes.clear()

    // 更新当前药品信息
    const {
      artName, artSpec, producer, noTrackCode,
      artIsDisassembled = false,
      aggregatedTotalPacks, aggregatedTotalCells,
      collectedPacks, collectedCells, packCells,
      sourceDetails, aggregatedKey
    } = record

    // 设置当前汇总键
    currentAggregatedKey.value = aggregatedKey

    // 明确设置每个属性
    currentArtInfo.artName = artName
    currentArtInfo.artSpec = artSpec
    currentArtInfo.producer = producer
    currentArtInfo.noTrackCode = noTrackCode
    currentArtInfo.artIsDisassembled = artIsDisassembled
    currentArtInfo.totalPacks = aggregatedTotalPacks
    currentArtInfo.totalCells = aggregatedTotalCells
    currentArtInfo.collectedPacks = collectedPacks
    currentArtInfo.collectedCells = collectedCells
    currentArtInfo.packCells = packCells
    currentArtInfo.sourceDetails = sourceDetails

    // 添加日志，帮助调试
    console.log('选中汇总行数据:', {
      aggregatedKey,
      artName,
      artIsDisassembled,
      sourceDetails: sourceDetails.length,
      currentArtInfo: { ...currentArtInfo }
    })

    // 更新选中行
    const selectedRowKeys = [...billDetailTableModel.selectedRowKeys]
    if (!selectedRowKeys.includes(aggregatedKey)) {
      selectedRowKeys.splice(0, selectedRowKeys.length)
      selectedRowKeys.push(aggregatedKey)
      billDetailTableModel.selectedRowKeys = selectedRowKeys
    }

    // 使用计算属性过滤追溯码
    updateFilteredTrackCodes()

    // 处理拆零情况
    searchFormModel.totalCells = 0
    if (aggregatedTotalCells > 0 || artIsDisassembled) {
      searchFormModel.isDisassembled = true

      // 特殊情况：totalPacks>0，packCells=1且设置了拆零上报时，使用totalPacks减去已采集数量
      if (artIsDisassembled && aggregatedTotalPacks > 0 && packCells === 1) {
        const remainingPacks = aggregatedTotalPacks - (collectedCells || 0)
        searchFormModel.totalCells = remainingPacks > 0 ? remainingPacks : aggregatedTotalPacks
      }
      // 如果有拆零数量，则计算剩余的拆零数量
      else if (aggregatedTotalCells > 0) {
        const remainingCells = aggregatedTotalCells - (collectedCells || 0)
        searchFormModel.totalCells = remainingCells > 0 ? remainingCells : aggregatedTotalCells
      } else {
        searchFormModel.totalCells = 1
      }

      nextTick(() => {
        trackCodeRef.value?.focus()
      })
    } else {
      searchFormModel.isDisassembled = false
      trackCodeRef.value?.focus()
    }
  },
  customRow: (record) => {
    return {
      onClick: () => {
        billDetailTableModel.selectRow(record)
      },
    }
  },
  pagination: false
})

const trackCodeRef = ref()
const totalCellsRef = ref()
const btnOkRef = ref()
const isDisassembledRef = ref()
const isViewRef = ref(false)
const titleRef = ref('')

// 计算是否有任何追溯码被采集
const hasAnyTrackCodes = computed(() => {
  return trackCodeDetailTableModel.allTrackCodes && trackCodeDetailTableModel.allTrackCodes.length > 0
})

// 更新过滤后的追溯码数据
const updateFilteredTrackCodes = () => {
  if (currentAggregatedKey.value) {
    // 过滤当前汇总键的追溯码
    if (trackCodeDetailTableModel.allTrackCodes && trackCodeDetailTableModel.allTrackCodes.length > 0) {
      trackCodeDetailTableModel.dataSource = trackCodeDetailTableModel.allTrackCodes.filter(
        (item: any) => item.aggregatedKey === currentAggregatedKey.value
      )

      // 计算已采集的拆零数量总和
      let collectedCellsSum = 0
      trackCodeDetailTableModel.dataSource.forEach((item: any) => {
        if (item.isDisassembled) {
          collectedCellsSum += (item.totalCells || 0)
        }
      })

      // 更新汇总数据的已采集拆零数量
      const aggregated = aggregatedBillDetails.value
      const currentAggregated = aggregated.find((item: any) => item.aggregatedKey === currentAggregatedKey.value)
      if (currentAggregated) {
        currentAggregated.collectedCells = collectedCellsSum
      }
    } else {
      trackCodeDetailTableModel.dataSource = []
    }
  } else {
    trackCodeDetailTableModel.dataSource = []
  }
}

// 更新追溯码缓存
const updateTrackCodeCache = () => {
  // 清空缓存
  trackCodeCache.clear()

  // 将所有追溯码添加到缓存
  if (trackCodeDetailTableModel.allTrackCodes) {
    trackCodeDetailTableModel.allTrackCodes.forEach((item: any) => {
      trackCodeCache.add(item.trackCode, item.aggregatedKey, item.artName)
    })
  }
}

// 更新药品的采集结果和已采数量
const updateCollectedCounts = () => {
  // 重置所有汇总项的采集数量
  const aggregated = aggregatedBillDetails.value
  aggregated.forEach((drug: any) => {
    drug.collectedPacks = 0
    drug.collectedCells = 0
    drug.collectedCount = 0
    drug.hasMultiplePrefixes = false
  })

  // 根据追溯码更新汇总项的采集数量
  if (trackCodeDetailTableModel.allTrackCodes) {
    // 创建汇总项前缀集合映射
    const drugPrefixes: Record<string, Set<string>> = {}

    trackCodeDetailTableModel.allTrackCodes.forEach((item: any) => {
      const aggregatedKey = item.aggregatedKey
      const drug = aggregated.find((d: any) => d.aggregatedKey === aggregatedKey)

      if (drug) {
        // 更新整包数和拆零数
        if (item.isDisassembled) {
          // 拆零模式
          if (drug.collectedCells !== undefined) {
            drug.collectedCells += (item.totalCells || 0)
          } else {
            drug.collectedCells = (item.totalCells || 0)
          }
        } else {
          // 整包模式
          if (drug.collectedPacks !== undefined) {
            drug.collectedPacks += 1
          } else {
            drug.collectedPacks = 1
          }
        }

        // 兼容旧版本，保留collectedCount字段
        if (drug.collectedCount) {
          drug.collectedCount += 1
        } else {
          drug.collectedCount = 1
        }

        // 记录汇总项的追溯码前缀
        const prefix = item.trackCode.substring(0, 7)
        if (!drugPrefixes[aggregatedKey]) {
          drugPrefixes[aggregatedKey] = new Set()
        }
        drugPrefixes[aggregatedKey].add(prefix)
      }
    })

    // 更新汇总项的多前缀标志
    Object.entries(drugPrefixes).forEach(([aggregatedKey, prefixes]) => {
      const drug = aggregated.find((d: any) => d.aggregatedKey === aggregatedKey)
      if (drug) {
        drug.hasMultiplePrefixes = prefixes.size > 1
      }
    })
  }
}

// 追溯码缓存，用于存储所有已扫描的追溯码
const trackCodeCache = {
  // 存储格式: { [trackCode]: { aggregatedKey, artName } }
  cache: {} as Record<string, { aggregatedKey: string, artName: string }>,

  // 添加追溯码
  add(trackCode: string, aggregatedKey: string, artName: string) {
    this.cache[trackCode] = { aggregatedKey, artName }
  },

  // 删除追溯码
  remove(trackCode: string) {
    delete this.cache[trackCode]
  },

  // 获取所有追溯码
  getAll() {
    return this.cache
  },

  // 检查追溯码是否存在
  has(trackCode: string) {
    return !!this.cache[trackCode]
  },

  // 清空缓存
  clear() {
    this.cache = {}
  }
}

// 追溯码处理队列
const trackCodeQueue = reactive({
  queue: [] as string[],
  processing: false,

  // 添加追溯码到队列
  add(trackCode: string) {
    this.queue.push(trackCode)
    this.processQueue()
  },

  // 处理队列
  async processQueue() {
    if (this.processing || this.queue.length === 0) return

    this.processing = true
    const trackCode = this.queue.shift()

    try {
      await processTrackCode(trackCode as string)
    } catch (error) {
      console.log('处理追溯码出错:', error)
    } finally {
      this.processing = false
      this.processQueue() // 处理下一个
    }
  }
})

const init = async (wbSeqIdLs: number[]) => {
  titleRef.value = '多单据追溯码采集'
  currentAggregatedKey.value = null

  // 重置currentArtInfo
  currentArtInfo.artName = ''
  currentArtInfo.artSpec = ''
  currentArtInfo.producer = ''
  currentArtInfo.noTrackCode = false
  currentArtInfo.artIsDisassembled = false
  currentArtInfo.trackCodePrefix = ''
  currentArtInfo.scannedPrefixes.clear()
  currentArtInfo.totalPacks = 0
  currentArtInfo.totalCells = 0
  currentArtInfo.collectedPacks = 0
  currentArtInfo.collectedCells = 0
  currentArtInfo.sourceDetails = []

  // 清空追溯码缓存
  trackCodeCache.clear()

  // 重置searchFormModel
  searchFormModel.isDisassembled = false
  searchFormModel.totalCells = 0
  searchFormModel.trackCode = ''
  searchFormModel.onlyAddRecognized = props.onlyAddRecognizedTrackCode

  // 初始化formState
  formState.wbSeqIdLs = wbSeqIdLs

  // 注意：多单据模式不需要调用infoApi

  billDetailTableModel.dataSource = []
  trackCodeDetailTableModel.dataSource = []
  await billDetailTableModel.loadDataSource()
  await trackCodeDetailTableModel.loadDataSource()

  // 更新追溯码缓存
  updateTrackCodeCache()

  // 更新药品的采集结果和已采数量
  updateCollectedCounts()

  // 显示对话框
  modalVisible.value = true

  // 自动聚焦到追溯码输入框
  nextTick(() => {
    trackCodeRef.value?.focus()
  })
}

const searchFormModel = reactive({
  isDisassembled: false,
  totalCells: 0,
  trackCode: '',
  onlyAddRecognized: false // 是否只添加识别追溯码
})

// 处理追溯码的核心函数
const processTrackCode = async (trackCode: string) => {
  if (billDetailTableModel.selectedRowKeys.length === 0) {
    message.error('请选择要采集的药品')
    return
  }

  // 验证追溯码长度
  if (trackCode.length < 19 || trackCode.length > 27) {
    message.warning('追溯码长度应在19-27位之间，请检查')
    return
  }

  // 提取追溯码前7位
  const prefix = trackCode.substring(0, 7)

  // 检查追溯码是否已经扫描过（当前汇总项）
  const isTrackCodeAlreadyScanned = trackCodeDetailTableModel.dataSource.some(
    item => item.trackCode === trackCode
  )

  if (isTrackCodeAlreadyScanned) {
    message.warning(`追溯码 ${trackCode} 已经扫描过，请勿重复扫描`)
    return
  }

  // 获取所有已扫描的追溯码（全局缓存）
  const allScannedTrackCodes = trackCodeCache.getAll()

  // 检查追溯码是否已被其他汇总项使用
  if (allScannedTrackCodes[trackCode] && allScannedTrackCodes[trackCode].aggregatedKey !== currentAggregatedKey.value) {
    const aggregated = aggregatedBillDetails.value
    const usedDrug = aggregated.find(
      item => item.aggregatedKey === allScannedTrackCodes[trackCode].aggregatedKey
    )

    if (usedDrug) {
      message.warning(`追溯码 ${trackCode} 已被药品"${usedDrug.artName}"使用，请检查`)
      return
    }
  }

  // 智能匹配药品
  const matchedDrugs = findMatchingDrugs(prefix)

  // 如果只匹配到一种药品，自动选择该药品
  if (matchedDrugs.length === 1) {
    // 如果匹配的药品不是当前选中的药品，自动切换
    if (matchedDrugs[0].aggregatedKey !== currentAggregatedKey.value) {
      // 选择匹配的药品
      billDetailTableModel.selectRow(matchedDrugs[0])
      console.log(`追溯码前7位 ${prefix} 匹配到药品 "${matchedDrugs[0].artName}"，已自动选择`)
    }
  } else if (matchedDrugs.length > 1) {
    // 如果匹配到多种药品，使用当前选中的药品
    // message.info(`追溯码前7位 ${prefix} 匹配到多种药品，将使用当前选中的药品`)
  } else if (matchedDrugs.length === 0 && searchFormModel.onlyAddRecognized) {
    // 如果没有匹配到任何药品，且设置了只添加识别追溯码，则不绑定
    message.warning(`追溯码前7位 ${prefix} 未匹配到任何药品，已跳过`)
    return
  }

  // 获取当前汇总项信息（可能已经被上面的代码更新）
  const aggregated = aggregatedBillDetails.value
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
  if (!currentArt) {
    message.error('未找到当前选中的药品')
    return
  }

  // 如果是第一次扫描该药品的追溯码，记录前缀
  if (!currentArtInfo.trackCodePrefix && !currentArtInfo.scannedPrefixes.size) {
    currentArtInfo.trackCodePrefix = prefix
    currentArtInfo.scannedPrefixes.add(prefix)
  }
  // 检查追溯码前7位与药品匹配
  else if (currentArtInfo.trackCodePrefix && prefix !== currentArtInfo.trackCodePrefix) {
    // 记录不同的前缀
    currentArtInfo.scannedPrefixes.add(prefix)

    // 根据前缀数量确定提示级别
    if (currentArtInfo.scannedPrefixes.size === 2) {
      message.warning(`检测到第二种追溯码前缀(${prefix})，与之前的前缀(${currentArtInfo.trackCodePrefix})不同，请注意检查`)
    } else if (currentArtInfo.scannedPrefixes.size > 2) {
      message.error(`检测到多种追溯码前缀，当前前缀(${prefix})，请特别注意检查`)
    }

    // 不阻止继续扫描，只做提醒
  }

  // 选择一个原始明细进行API调用（从sourceDetails中选择第一个）
  const sourceDetail = currentArtInfo.sourceDetails[0]
  if (!sourceDetail) {
    message.error('未找到原始明细数据')
    return
  }

  // 添加追溯码
  try {
    await addCodeApi({
      wbSeqid: sourceDetail.wbSeqid,
      lineNo: sourceDetail.lineNo,
      trackCode: trackCode,
      isDisassembled: searchFormModel.isDisassembled ? 1 : 0,
      totalCells: searchFormModel.totalCells.toString()
    })

    // 更新汇总项已采集数量
    if (searchFormModel.isDisassembled) {
      // 拆零模式
      if (currentArt.collectedCells !== undefined) {
        currentArt.collectedCells += searchFormModel.totalCells
      } else {
        currentArt.collectedCells = searchFormModel.totalCells
      }
      // 添加动画效果
      currentArt.animateCells = true
      setTimeout(() => {
        currentArt.animateCells = false
      }, 500)
    } else {
      // 整包模式
      if (currentArt.collectedPacks !== undefined) {
        currentArt.collectedPacks += 1
      } else {
        currentArt.collectedPacks = 1
      }
      // 添加动画效果
      currentArt.animatePacks = true
      setTimeout(() => {
        currentArt.animatePacks = false
      }, 500)
    }

    // 兼容旧版本，保留collectedCount字段
    if (currentArt.collectedCount) {
      currentArt.collectedCount = currentArt.collectedCount + 1
    } else {
      currentArt.collectedCount = 1
    }

    // 添加到追溯码缓存
    trackCodeCache.add(trackCode, currentAggregatedKey.value, currentArt.artName)

    // 创建新的追溯码记录
    const newTrackCode = {
      wbSeqid: sourceDetail.wbSeqid,
      lineNo: sourceDetail.lineNo,
      trackCode: trackCode,
      isDisassembled: searchFormModel.isDisassembled ? 1 : 0,
      totalCells: searchFormModel.isDisassembled ? searchFormModel.totalCells : 0,
      artName: currentArt.artName,
      artSpec: currentArt.artSpec,
      producer: currentArt.producer,
      aggregatedKey: currentAggregatedKey.value
    }

    // 添加到追溯码列表
    if (trackCodeDetailTableModel.allTrackCodes) {
      trackCodeDetailTableModel.allTrackCodes.push(newTrackCode)
    } else {
      trackCodeDetailTableModel.allTrackCodes = [newTrackCode]
    }

    // 更新过滤后的追溯码数据
    updateFilteredTrackCodes()

    // 检查是否需要自动切换到下一种药品
    checkAndSwitchToNextDrug()

    // 清空追溯码输入框
    searchFormModel.trackCode = ''

    // 重新聚焦到追溯码输入框
    nextTick(() => {
      trackCodeRef.value?.focus()
    })

    console.log('追溯码添加成功:', trackCode)
  } catch (error) {
    console.error('添加追溯码失败:', error)
    message.error('添加追溯码失败，请重试')
  }
}

// 查找匹配的药品
const findMatchingDrugs = (prefix: string) => {
  const aggregated = aggregatedBillDetails.value
  return aggregated.filter((drug: any) => {
    // 检查药品的品种唯一码列表是否包含该前缀
    return drug.prodNoLs && drug.prodNoLs.includes(prefix)
  })
}

// 检查并切换到下一种药品
const checkAndSwitchToNextDrug = () => {
  const aggregated = aggregatedBillDetails.value
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)

  if (!currentArt) return

  // 检查当前药品是否已完成采集
  let isCurrentComplete = false

  if (currentArt.artIsDisassembled) {
    // 拆零上报模式
    if (currentArt.aggregatedTotalPacks > 0 && currentArt.packCells === 1) {
      isCurrentComplete = currentArt.collectedCells >= currentArt.aggregatedTotalPacks
    } else {
      isCurrentComplete = currentArt.collectedCells > 0
    }
  } else {
    // 正常模式
    const isPacksComplete = currentArt.aggregatedTotalPacks > 0 ? (currentArt.collectedPacks >= currentArt.aggregatedTotalPacks) : true
    const isCellsComplete = currentArt.aggregatedTotalCells > 0 ? (currentArt.collectedCells >= currentArt.aggregatedTotalCells) : true
    isCurrentComplete = isPacksComplete && isCellsComplete
  }

  if (isCurrentComplete) {
    // 查找下一个未完成的药品
    const nextDrug = aggregated.find((drug: any) => {
      if (drug.noTrackCode) return false // 跳过无追溯码的药品
      if (drug.aggregatedKey === currentAggregatedKey.value) return false // 跳过当前药品

      // 检查是否已完成
      if (drug.artIsDisassembled) {
        if (drug.aggregatedTotalPacks > 0 && drug.packCells === 1) {
          return drug.collectedCells < drug.aggregatedTotalPacks
        } else {
          return drug.collectedCells <= 0
        }
      } else {
        const isPacksComplete = drug.aggregatedTotalPacks > 0 ? (drug.collectedPacks >= drug.aggregatedTotalPacks) : true
        const isCellsComplete = drug.aggregatedTotalCells > 0 ? (drug.collectedCells >= drug.aggregatedTotalCells) : true
        return !(isPacksComplete && isCellsComplete)
      }
    })

    if (nextDrug) {
      console.log(`当前药品已完成，自动切换到下一种药品: ${nextDrug.artName}`)
      billDetailTableModel.selectRow(nextDrug)
    } else {
      console.log('所有药品已完成采集')
      // 可以在这里添加全部完成的提示逻辑
    }
  }
}

// 删除追溯码
const onDelArt = async (record: any) => {
  try {
    await delCodeApi({
      wbSeqid: record.wbSeqid,
      lineNo: record.lineNo,
      trackCode: record.trackCode
    })

    // 从追溯码缓存中删除
    trackCodeCache.remove(record.trackCode)

    // 从追溯码列表中删除
    if (trackCodeDetailTableModel.allTrackCodes) {
      const index = trackCodeDetailTableModel.allTrackCodes.findIndex(
        (item: any) => item.trackCode === record.trackCode
      )
      if (index > -1) {
        trackCodeDetailTableModel.allTrackCodes.splice(index, 1)
      }
    }

    // 更新过滤后的追溯码数据
    updateFilteredTrackCodes()

    // 更新汇总项的采集数量
    const aggregated = aggregatedBillDetails.value
    const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
    if (currentArt) {
      if (record.isDisassembled) {
        // 拆零模式
        currentArt.collectedCells -= (record.totalCells || 0)
        if (currentArt.collectedCells < 0) currentArt.collectedCells = 0
      } else {
        // 整包模式
        currentArt.collectedPacks -= 1
        if (currentArt.collectedPacks < 0) currentArt.collectedPacks = 0
      }

      // 兼容旧版本
      currentArt.collectedCount -= 1
      if (currentArt.collectedCount < 0) currentArt.collectedCount = 0
    }

    // 重新聚焦到追溯码输入框
    nextTick(() => {
      trackCodeRef.value?.focus()
    })

    console.log('追溯码删除成功:', record.trackCode)
  } catch (error) {
    console.error('删除追溯码失败:', error)
    message.error('删除追溯码失败，请重试')
  }
}

// 全部清除追溯码
const onDelAllArt = async () => {
  try {
    await delAllCodeApi({
      wbSeqids: formState.wbSeqIdLs
    })

    // 清空追溯码缓存
    trackCodeCache.clear()

    // 清空追溯码列表
    trackCodeDetailTableModel.allTrackCodes = []
    trackCodeDetailTableModel.dataSource = []

    // 重置所有汇总项的采集数量
    const aggregated = aggregatedBillDetails.value
    aggregated.forEach((drug: any) => {
      drug.collectedPacks = 0
      drug.collectedCells = 0
      drug.collectedCount = 0
      drug.hasMultiplePrefixes = false
    })

    // 重新聚焦到追溯码输入框
    nextTick(() => {
      trackCodeRef.value?.focus()
    })

    message.success('已清除所有追溯码')
    console.log('全部清除追溯码成功')
  } catch (error) {
    console.error('全部清除追溯码失败:', error)
    message.error('全部清除追溯码失败，请重试')
  }
}

// 设置无追溯码
const onSetNoTrackCode = async () => {
  const aggregated = aggregatedBillDetails.value
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
  if (!currentArt) return

  try {
    // 为所有原始明细设置无追溯码
    for (const sourceDetail of currentArtInfo.sourceDetails) {
      await setNoTrackCodeApi({
        wbSeqid: sourceDetail.wbSeqid,
        lineNo: sourceDetail.lineNo
      })
    }

    // 更新本地数据
    currentArt.noTrackCode = true
    currentArtInfo.noTrackCode = true

    message.success('已设置为无追溯码')
  } catch (error) {
    console.error('设置无追溯码失败:', error)
    message.error('设置无追溯码失败，请重试')
  }
}

// 清除无追溯码
const onClearNoTrackCode = async () => {
  const aggregated = aggregatedBillDetails.value
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
  if (!currentArt) return

  try {
    // 为所有原始明细清除无追溯码
    for (const sourceDetail of currentArtInfo.sourceDetails) {
      await trackCodeClearNoTrackCodeApi({
        wbSeqid: sourceDetail.wbSeqid,
        lineNo: sourceDetail.lineNo
      })
    }

    // 更新本地数据
    currentArt.noTrackCode = false
    currentArtInfo.noTrackCode = false

    message.success('已设置为有追溯码')
  } catch (error) {
    console.error('清除无追溯码失败:', error)
    message.error('清除无追溯码失败，请重试')
  }
}

// 设置拆零上报
const onSetDisassembled = async () => {
  const aggregated = aggregatedBillDetails.value
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
  if (!currentArt) return

  try {
    // 为所有原始明细设置拆零上报
    for (const sourceDetail of currentArtInfo.sourceDetails) {
      await trackCodeSetDisassembledApi({
        wbSeqid: sourceDetail.wbSeqid,
        lineNo: sourceDetail.lineNo
      })
    }

    // 更新本地数据
    currentArt.artIsDisassembled = true
    currentArtInfo.artIsDisassembled = true

    // 自动勾选拆零复选框并设置拆零数量
    searchFormModel.isDisassembled = true
    if (currentArt.aggregatedTotalPacks > 0 && currentArt.packCells === 1) {
      searchFormModel.totalCells = currentArt.aggregatedTotalPacks
    } else {
      searchFormModel.totalCells = 1
    }

    // 聚焦到追溯码输入框
    nextTick(() => {
      trackCodeRef.value?.focus()
    })

    message.success('已设置为拆零上报')
  } catch (error) {
    console.error('设置拆零上报失败:', error)
    message.error('设置拆零上报失败，请重试')
  }
}

// 清除拆零上报
const onClearDisassembled = async () => {
  const aggregated = aggregatedBillDetails.value
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
  if (!currentArt) return

  try {
    // 为所有原始明细清除拆零上报
    for (const sourceDetail of currentArtInfo.sourceDetails) {
      await trackCodeClearDisassembledApi({
        wbSeqid: sourceDetail.wbSeqid,
        lineNo: sourceDetail.lineNo
      })
    }

    // 更新本地数据
    currentArt.artIsDisassembled = false
    currentArtInfo.artIsDisassembled = false

    message.success('已取消拆零上报')
  } catch (error) {
    console.error('清除拆零上报失败:', error)
    message.error('清除拆零上报失败，请重试')
  }
}

// 处理追溯码输入
const onTrackCodeInput = (e: any) => {
  const value = e.target.value
  if (value.includes('\n') || value.includes('\r')) {
    // 处理回车事件（扫码枪或高拍仪）
    const trackCode = value.replace(/[\r\n]/g, '').trim()
    if (trackCode) {
      trackCodeQueue.add(trackCode)
    }
    searchFormModel.trackCode = ''
  }
}

// 处理追溯码按键事件
const onTrackCodeKeydown = (e: any) => {
  if (e.key === 'Enter') {
    e.preventDefault()
    const trackCode = searchFormModel.trackCode.trim()
    if (trackCode) {
      trackCodeQueue.add(trackCode)
    }
  }
}

// 确定按钮
const onOk = async () => {
  try {
    // 这里可以添加提交逻辑
    emit('success', formState.wbSeqIdLs)
    modalVisible.value = false
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败，请重试')
  }
}

// 取消按钮
const onCancel = () => {
  emit('cancel')
  modalVisible.value = false
}

// 暴露给父组件的方法
const open = (wbSeqIdLs?: number[]) => {
  if (wbSeqIdLs && wbSeqIdLs.length > 0) {
    init(wbSeqIdLs)
  } else {
    message.error('请提供有效的单据ID列表')
  }
}

const close = () => {
  modalVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    :title="titleRef"
    :width="props.modalWidth"
    :maskClosable="false"
    :keyboard="false"
    @cancel="onCancel"
  >
    <template #footer>
      <Space>
        <Button @click="onCancel">取消</Button>
        <Button type="primary" @click="onOk" ref="btnOkRef">确定</Button>
      </Space>
    </template>

    <div class="bills-track-code-container">
      <!-- 追溯码录入区域 -->
      <div class="track-code-input-section">
        <Row :gutter="16" align="middle">
          <Col :span="8">
            <Form.Item label="追溯码" style="margin-bottom: 0;">
              <Input
                ref="trackCodeRef"
                v-model:value="searchFormModel.trackCode"
                placeholder="请扫描或输入追溯码"
                @input="onTrackCodeInput"
                @keydown="onTrackCodeKeydown"
                autocomplete="off"
              />
            </Form.Item>
          </Col>
          <Col :span="4">
            <Form.Item style="margin-bottom: 0;">
              <Checkbox
                ref="isDisassembledRef"
                v-model:checked="searchFormModel.isDisassembled"
                :disabled="currentArtInfo.totalCells <= 0 && !currentArtInfo.artIsDisassembled"
              >
                拆零
              </Checkbox>
            </Form.Item>
          </Col>
          <Col :span="4">
            <Form.Item style="margin-bottom: 0;">
              <InputNumber
                ref="totalCellsRef"
                v-model:value="searchFormModel.totalCells"
                :min="1"
                :disabled="!searchFormModel.isDisassembled"
                placeholder="拆零数量"
                style="width: 100%"
              />
            </Form.Item>
          </Col>
          <Col :span="8" v-if="props.enableOnlyAddRecognizedTrackCodeOption">
            <Form.Item style="margin-bottom: 0;">
              <Checkbox v-model:checked="searchFormModel.onlyAddRecognized">
                只添加识别追溯码
              </Checkbox>
            </Form.Item>
          </Col>
        </Row>

        <!-- 当前药品信息 -->
        <div class="current-drug-info" v-if="currentArtInfo.artName">
          <div class="drug-basic-info">
            <strong>{{ currentArtInfo.artName }}</strong>
            <span v-if="currentArtInfo.artSpec"> | {{ currentArtInfo.artSpec }}</span>
            <span v-if="currentArtInfo.producer"> | {{ currentArtInfo.producer }}</span>
          </div>
          <div class="drug-status-info">
            <span v-if="currentArtInfo.totalPacks > 0">整包: {{ currentArtInfo.totalPacks }}</span>
            <span v-if="currentArtInfo.totalCells > 0">拆零: {{ currentArtInfo.totalCells }}</span>
            <span v-if="currentArtInfo.collectedPacks > 0">已采整包: {{ currentArtInfo.collectedPacks }}</span>
            <span v-if="currentArtInfo.collectedCells > 0">已采拆零: {{ currentArtInfo.collectedCells }}</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <Space>
            <Button @click="onDelAllArt" :disabled="!hasAnyTrackCodes">全部清除</Button>

            <!-- 无追溯码按钮 -->
            <Button
              v-if="!currentArtInfo.noTrackCode"
              @click="onSetNoTrackCode"
              :disabled="!currentAggregatedKey"
            >
              设置为无追溯码
            </Button>
            <Button
              v-else
              @click="onClearNoTrackCode"
              :disabled="!currentAggregatedKey"
            >
              设置为有追溯码
            </Button>

            <!-- 拆零上报按钮 -->
            <Button
              v-if="!currentArtInfo.artIsDisassembled && currentArtInfo.totalPacks > 0 && (!currentArtInfo.packCells || currentArtInfo.packCells === 1)"
              @click="onSetDisassembled"
              :disabled="!currentAggregatedKey"
            >
              拆零上报
            </Button>
            <Button
              v-else-if="currentArtInfo.artIsDisassembled"
              @click="onClearDisassembled"
              :disabled="!currentAggregatedKey"
            >
              取消拆零上报
            </Button>
          </Space>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <Row :gutter="16">
          <!-- 左侧：汇总后的药品列表 -->
          <Col :span="14">
            <div class="table-section">
              <div class="table-header">
                <h4>药品明细（汇总后）</h4>
                <span class="table-count">共 {{ aggregatedBillDetails.length }} 项</span>
              </div>
              <Table
                :columns="billDetailTableModel.columns"
                :dataSource="aggregatedBillDetails"
                :loading="billDetailTableModel.loading"
                :pagination="billDetailTableModel.pagination"
                :rowSelection="billDetailTableModel.rowSelection"
                :customRow="billDetailTableModel.customRow"
                :rowKey="(record) => record.aggregatedKey"
                size="small"
                :scroll="{ y: 400 }"
              />
            </div>
          </Col>

          <!-- 右侧：追溯码明细 -->
          <Col :span="10">
            <div class="table-section">
              <div class="table-header">
                <h4>追溯码明细</h4>
                <span class="table-count">共 {{ trackCodeDetailTableModel.dataSource.length }} 条</span>
              </div>
              <Table
                :columns="trackCodeDetailTableModel.columns"
                :dataSource="trackCodeDetailTableModel.dataSource"
                :loading="trackCodeDetailTableModel.loading"
                :pagination="trackCodeDetailTableModel.pagination"
                :rowKey="(record) => record.trackCode"
                size="small"
                :scroll="{ y: 400 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'isDisassembled'">
                    <span v-if="record.isDisassembled">是</span>
                    <span v-else>否</span>
                  </template>
                  <template v-else-if="column.dataIndex === 'action'">
                    <Button
                      type="link"
                      size="small"
                      @click="onDelArt(record)"
                      :disabled="isViewRef"
                    >
                      删除
                    </Button>
                  </template>
                </template>
              </Table>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
.bills-track-code-container {
  padding: 16px 0;
}

.track-code-input-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.current-drug-info {
  margin-top: 12px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border-left: 4px solid #1890ff;
  border-radius: 4px;
}

.drug-basic-info {
  font-size: 14px;
  margin-bottom: 4px;
}

.drug-status-info {
  font-size: 12px;
  color: #666;
}

.drug-status-info span {
  margin-right: 12px;
}

.action-buttons {
  margin-top: 12px;
}

.main-content {
  margin-top: 16px;
}

.table-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #d9d9d9;
}

.table-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.table-count {
  font-size: 12px;
  color: #666;
}

/* 动画效果 */
@keyframes count-change {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
    color: #52c41a;
  }
  100% {
    transform: scale(1);
  }
}

.collected-count {
  font-weight: bold;
}

/* 表格样式优化 */
:deep(.ant-table-tbody > tr.ant-table-row-selected > td) {
  background-color: #e6f7ff;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* 输入框样式 */
:deep(.ant-input:focus) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>