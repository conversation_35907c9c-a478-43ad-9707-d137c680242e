<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Card, Row, Col, Input, Checkbox, Tag, Button, message, Tabs, Empty } from 'ant-design-vue'
import { CheckCircleFilled, WarningFilled, CloseCircleOutlined } from '@ant-design/icons-vue'
import type { PropType } from 'vue'

// 定义OeItem接口
interface OeItem {
  visitId: number | string
  oeNo: number | string
  patientName: string
  bedNo: string
  artId: number
  artTypeId: number
  totalPacks: number
  totalCells: number
  packCells: number
  artName: string
  artSpec: string
  producer: string
  packUnit: string
  cellUnit: string
  isDisassembled: boolean
  execDate: string
  needTrackCode: boolean
}

// 定义FeeItem接口
interface FeeItem {
  visitId: number | string
  oeNo: number | string
  artId: number
  totalPacks?: number
  totalCells?: number
  totalCell?: number
  meals?: number
  times?: number
  bsnDate: number | string
}

// 定义TrackCodeItem接口
interface TrackCodeItem {
  trackCode: string
  visitId: number | string
  oeNo: number | string
  artId?: number
  cellsDispensed: number
  cellsRemain: number
  isDisassembled: number
  dispensedCount: number
  bsnDate: number | string
  dispensedNo: number
  timeDispensed: string
  totalPacks: number
  totalCells: number
}

// 定义药品汇总项
interface MedicineItem {
  artId: number
  artName: string
  artSpec: string
  producer: string
  totalPacks: number
  totalCells: number
  packUnit: string
  cellUnit: string
  packCells: number
  isDisassembled: boolean
  patients: PatientMedicineItem[]
  isCompleted: boolean
  hasWarning: boolean
}

// 定义患者药品项
interface PatientMedicineItem {
  visitId: number | string
  oeNo: number | string
  patientName: string
  bedNo: string
  totalPacks: number
  totalCells: number
  trackCodes: string[]
  needUnbind: boolean
}

// 定义组件属性
const props = defineProps({
  // 医嘱用药申请数组
  oeList: {
    type: Array as PropType<OeItem[]>,
    default: () => [],
  },
  // 医嘱计费数组
  feeList: {
    type: Array as PropType<FeeItem[]>,
    default: () => [],
  },
  // 追溯码使用数组
  trackCodeList: {
    type: Array as PropType<TrackCodeItem[]>,
    default: () => [],
  },
  // 扫码模式：'ward'(病区扫码), 'pharmacy'(药房扫码)
  mode: {
    type: String as PropType<'ward' | 'pharmacy'>,
    default: 'ward',
  },
  // 扫码方式：'byPatient'(按患者扫码), 'byMedicine'(按药品扫码)
  scanType: {
    type: String as PropType<'byPatient' | 'byMedicine'>,
    default: 'byPatient',
  },
})

// 定义事件
const emit = defineEmits(['scan', 'bind', 'unbind', 'invalid-code'])

// 当前活动的Tab
const activeTab = ref(props.scanType)

// 药品列表（按artId汇总）
const medicineList = ref<MedicineItem[]>([])

// 选中的药品
const selectedMedicine = ref<MedicineItem | null>(null)

// 选中的患者（可多选）
const selectedPatients = ref<PatientMedicineItem[]>([])

// 是否拆零
const isSplit = ref(false)

// 追溯码输入框
const trackCodeInput = ref('')
const trackCodeInputRef = ref<HTMLInputElement | null>(null)

// 新增的绑定关系
const newBindings = ref<{ trackCode: string, visitId: number | string, oeNo: number | string, artId: number }[]>([])

// 取消的绑定关系
const cancelBindings = ref<{ trackCode: string, visitId: number | string, oeNo: number | string, artId: number }[]>([])

// 计算属性：按患者分组的药品列表
const patientList = computed(() => {
  const patients: {
    visitId: number | string,
    oeNo: number | string,
    patientName: string,
    bedNo: string,
    medicines: {
      artId: number,
      artName: string,
      artSpec: string,
      producer: string,
      totalPacks: number,
      totalCells: number,
      packUnit: string,
      cellUnit: string,
      packCells: number,
      isDisassembled: boolean,
      trackCodes: string[],
      needUnbind: boolean
    }[]
  }[] = []

  // 实现按患者分组的逻辑
  // ...

  return patients
})

// 监听oeList变化，处理数据
watch(() => props.oeList, (newOeList) => {
  processMedicineList(newOeList, props.feeList, props.trackCodeList)
}, { immediate: true, deep: true })

// 处理药品列表数据
const processMedicineList = (oeList: OeItem[], feeList: FeeItem[], trackCodeList: TrackCodeItem[]) => {
  // 按artId分组汇总药品
  const medicineMap = new Map<number, MedicineItem>()

  // 处理医嘱用药申请数组
  oeList.forEach(oe => {
    // 获取或创建药品项
    if (!medicineMap.has(oe.artId)) {
      medicineMap.set(oe.artId, {
        artId: oe.artId,
        artName: oe.artName,
        artSpec: oe.artSpec,
        producer: oe.producer,
        totalPacks: 0,
        totalCells: 0,
        packUnit: oe.packUnit,
        cellUnit: oe.cellUnit,
        packCells: oe.packCells,
        isDisassembled: oe.isDisassembled,
        patients: [],
        isCompleted: false,
        hasWarning: false
      })
    }

    const medicine = medicineMap.get(oe.artId)!

    // 累加需求量
    medicine.totalPacks += oe.totalPacks
    medicine.totalCells += oe.totalCells

    // 添加患者
    const patientMedicine: PatientMedicineItem = {
      visitId: oe.visitId,
      oeNo: oe.oeNo,
      patientName: oe.patientName,
      bedNo: oe.bedNo,
      totalPacks: oe.totalPacks,
      totalCells: oe.totalCells,
      trackCodes: [],
      needUnbind: false
    }

    medicine.patients.push(patientMedicine)
  })

  // 处理计费数据，检查是否有需要解绑的情况
  if (feeList && feeList.length > 0) {
    // 按visitId+oeNo分组汇总计费数据
    const feeMap = new Map<string, number>()

    feeList.forEach(fee => {
      const key = `${fee.visitId}-${fee.oeNo}-${fee.artId}`
      const amount = fee.totalPacks || 0 + (fee.totalCells || fee.totalCell || 0)

      if (!feeMap.has(key)) {
        feeMap.set(key, 0)
      }

      feeMap.set(key, feeMap.get(key)! + amount)
    })

    // 检查计费数据，标记需要解绑的情况
    medicineMap.forEach(medicine => {
      medicine.patients.forEach(patient => {
        const key = `${patient.visitId}-${patient.oeNo}-${medicine.artId}`

        // 如果计费数据汇总为0，表示已退费，需要解绑
        if (feeMap.has(key) && feeMap.get(key) === 0) {
          patient.needUnbind = true
          medicine.hasWarning = true
        }
      })
    })
  }

  // 处理追溯码数据
  if (trackCodeList && trackCodeList.length > 0) {
    trackCodeList.forEach(track => {
      medicineMap.forEach(medicine => {
        // 如果追溯码记录中有artId，则直接匹配
        if (track.artId && medicine.artId === track.artId) {
          medicine.patients.forEach(patient => {
            if (patient.visitId === track.visitId && patient.oeNo === track.oeNo) {
              // 添加追溯码
              if (!patient.trackCodes.includes(track.trackCode)) {
                patient.trackCodes.push(track.trackCode)
              }
            }
          })
        } else {
          // 如果追溯码记录中没有artId，则只根据visitId和oeNo匹配
          medicine.patients.forEach(patient => {
            if (patient.visitId === track.visitId && patient.oeNo === track.oeNo) {
              // 添加追溯码
              if (!patient.trackCodes.includes(track.trackCode)) {
                patient.trackCodes.push(track.trackCode)
              }
            }
          })
        }
      })
    })
  }

  // 检查每个药品是否所有患者都已绑定追溯码
  medicineMap.forEach(medicine => {
    let allBound = true

    medicine.patients.forEach(patient => {
      // 如果有患者没有绑定追溯码，则药品未完成
      if (patient.trackCodes.length === 0) {
        allBound = false
      }
    })

    medicine.isCompleted = allBound
  })

  // 更新药品列表
  medicineList.value = Array.from(medicineMap.values())
}

// 选择药品
const selectMedicine = (medicine: MedicineItem) => {
  selectedMedicine.value = medicine
  selectedPatients.value = []

  // 如果药品需要拆零，自动勾选拆零选项
  isSplit.value = medicine.packCells > 1 || medicine.isDisassembled

  // 聚焦追溯码输入框
  setTimeout(() => {
    if (trackCodeInputRef.value) {
      trackCodeInputRef.value.focus()
    }
  }, 100)
}

// 选择患者
const togglePatientSelection = (patient: PatientMedicineItem) => {
  const index = selectedPatients.value.findIndex(p =>
    p.visitId === patient.visitId && p.oeNo === patient.oeNo
  )

  if (index >= 0) {
    // 已选中，取消选择
    selectedPatients.value.splice(index, 1)
  } else {
    // 未选中，添加选择
    // 如果不是拆零模式，先清空选择
    if (!isSplit.value) {
      selectedPatients.value = []
    }
    selectedPatients.value.push(patient)
  }
}

// 扫描追溯码
const scanTrackCode = () => {
  if (!trackCodeInput.value) {
    message.warning('请输入追溯码')
    return
  }

  // 验证追溯码
  if (trackCodeInput.value.length < 19) {
    message.error(`追溯码长度不足19位 (当前长度: ${trackCodeInput.value.length})，不是有效的追溯码`)
    emit('invalid-code', trackCodeInput.value, `追溯码长度不足19位 (当前长度: ${trackCodeInput.value.length})，不是有效的追溯码`)
    return
  }

  if (trackCodeInput.value.length > 27) {
    message.error(`追溯码长度超过27位 (当前长度: ${trackCodeInput.value.length})，不是有效的追溯码`)
    emit('invalid-code', trackCodeInput.value, `追溯码长度超过27位 (当前长度: ${trackCodeInput.value.length})，不是有效的追溯码`)
    return
  }

  // 检查是否选择了药品和患者
  if (!selectedMedicine.value) {
    message.warning('请先选择药品')
    return
  }

  if (selectedPatients.value.length === 0) {
    message.warning('请先选择患者')
    return
  }

  // 绑定追溯码
  bindTrackCode(trackCodeInput.value)

  // 清空输入框并聚焦
  trackCodeInput.value = ''
  if (trackCodeInputRef.value) {
    trackCodeInputRef.value.focus()
  }
}

// 绑定追溯码
const bindTrackCode = (trackCode: string) => {
  if (!selectedMedicine.value) return

  // 为选中的患者绑定追溯码
  selectedPatients.value.forEach(patient => {
    // 添加追溯码
    if (!patient.trackCodes.includes(trackCode)) {
      patient.trackCodes.push(trackCode)

      // 记录新增的绑定关系
      const now = new Date()
      const formattedDate = now.toISOString().split('T')[0] // 格式如：2023-06-01
      const formattedDateNumber = parseInt(formattedDate.replace(/-/g, '')) // 格式如：20230601

      const newBinding = {
        trackCode,
        visitId: patient.visitId,
        oeNo: patient.oeNo,
        artId: selectedMedicine.value!.artId,
        cellsDispensed: isSplit.value ? 1 : patient.totalCells, // 如果是拆零，则已用制剂数为1，否则为患者需求的拆零数
        cellsRemain: isSplit.value ? (selectedMedicine.value!.packCells - 1) : 0, // 如果是拆零，则剩余制剂数为拆零系数-1，否则为0
        isDisassembled: isSplit.value ? 1 : 0, // 拆零标志位
        dispensedCount: 1, // 分发次数
        bsnDate: formattedDateNumber, // 发药日期
        dispensedNo: 1, // 分发序号
        timeDispensed: formattedDate, // 发药时间
        totalPacks: patient.totalPacks, // 发药包装数
        totalCells: patient.totalCells // 发药制剂数
      }

      newBindings.value.push(newBinding)

      // 触发绑定事件
      emit('bind', newBinding)
    }
  })

  // 检查药品是否所有患者都已绑定追溯码
  let allBound = true
  selectedMedicine.value.patients.forEach(patient => {
    if (patient.trackCodes.length === 0) {
      allBound = false
    }
  })

  selectedMedicine.value.isCompleted = allBound

  // 如果不是拆零模式，清空选择的患者
  if (!isSplit.value) {
    selectedPatients.value = []
  }
}

// 解绑追溯码
const unbindTrackCode = (patient: PatientMedicineItem, trackCode: string) => {
  if (!selectedMedicine.value) return

  // 从患者的追溯码列表中移除
  const index = patient.trackCodes.indexOf(trackCode)
  if (index >= 0) {
    patient.trackCodes.splice(index, 1)

    // 记录取消的绑定关系
    const cancelBinding = {
      trackCode,
      visitId: patient.visitId,
      oeNo: patient.oeNo,
      artId: selectedMedicine.value.artId
    }

    cancelBindings.value.push(cancelBinding)

    // 触发解绑事件
    emit('unbind', cancelBinding)

    // 更新药品完成状态
    selectedMedicine.value.isCompleted = false
  }
}

// 处理追溯码输入框回车事件
const handleTrackCodeEnter = () => {
  scanTrackCode()
}

// 暴露方法
defineExpose({
  getNewBindings: () => newBindings.value,
  getCancelBindings: () => cancelBindings.value,
  resetBindings: () => {
    newBindings.value = []
    cancelBindings.value = []
  }
})
</script>

<template>
  <div class="wm-track-code-core">
    <Tabs v-model:activeKey="activeTab">
      <Tabs.TabPane key="byMedicine" tab="按药品扫码">
        <div class="track-code-container">
          <!-- 左侧药品列表 -->
          <div class="medicine-list">
            <Card title="药品列表" class="medicine-card">
              <Empty v-if="medicineList.length === 0" description="暂无药品数据" />
              <div v-else class="medicine-items">
                <div
                  v-for="medicine in medicineList"
                  :key="medicine.artId"
                  class="medicine-item"
                  :class="{
                    'medicine-item-selected': selectedMedicine && selectedMedicine.artId === medicine.artId,
                    'medicine-item-completed': medicine.isCompleted,
                    'medicine-item-warning': medicine.hasWarning
                  }"
                  @click="selectMedicine(medicine)"
                >
                  <div class="medicine-name">{{ medicine.artName }} {{ medicine.artSpec }}</div>
                  <div class="medicine-producer">{{ medicine.producer }}</div>
                  <div class="medicine-amount">
                    需求量: {{ medicine.totalPacks }}{{ medicine.packUnit }}
                    <span v-if="medicine.totalCells > 0">
                      {{ medicine.totalCells }}{{ medicine.cellUnit }}
                    </span>
                  </div>
                  <div class="medicine-status">
                    <CheckCircleFilled v-if="medicine.isCompleted" class="status-icon completed" />
                    <WarningFilled v-if="medicine.hasWarning" class="status-icon warning" />
                  </div>
                </div>
              </div>
            </Card>
          </div>

          <!-- 右侧患者列表和追溯码输入 -->
          <div class="patient-list">
            <Card :title="selectedMedicine ? `${selectedMedicine.artName} 的患者列表` : '患者列表'" class="patient-card">
              <Empty v-if="!selectedMedicine || selectedMedicine.patients.length === 0" description="请先选择药品" />
              <template v-else>
                <div class="patient-items">
                  <div
                    v-for="(patient, index) in selectedMedicine.patients"
                    :key="`${patient.visitId}-${patient.oeNo}-${index}`"
                    class="patient-item"
                    :class="{
                      'patient-item-selected': selectedPatients.some(p => p.visitId === patient.visitId && p.oeNo === patient.oeNo),
                      'patient-item-bound': patient.trackCodes.length > 0,
                      'patient-item-warning': patient.needUnbind
                    }"
                    @click="togglePatientSelection(patient)"
                  >
                    <div class="patient-info">
                      <div class="patient-name">{{ patient.bedNo }} {{ patient.patientName }}</div>
                      <div class="patient-amount">
                        需求量: {{ patient.totalPacks }}{{ selectedMedicine.packUnit }}
                        <span v-if="patient.totalCells > 0">
                          {{ patient.totalCells }}{{ selectedMedicine.cellUnit }}
                        </span>
                      </div>
                      <div v-if="patient.trackCodes.length > 0" class="patient-track-codes">
                        <div v-for="(code, codeIndex) in patient.trackCodes" :key="codeIndex" class="track-code-item">
                          <Tag>{{ code }}</Tag>
                          <CloseCircleOutlined class="unbind-icon" @click.stop="unbindTrackCode(patient, code)" />
                        </div>
                      </div>
                      <div v-if="patient.needUnbind" class="patient-warning">
                        <Tag color="error">已退费，请解绑追溯码</Tag>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="track-code-input-container">
                  <div class="split-checkbox">
                    <Checkbox v-model:checked="isSplit">拆零</Checkbox>
                  </div>
                  <Input
                    ref="trackCodeInputRef"
                    v-model:value="trackCodeInput"
                    placeholder="请扫描或输入追溯码"
                    @pressEnter="handleTrackCodeEnter"
                  />
                  <Button type="primary" @click="scanTrackCode">绑定</Button>
                </div>
              </template>
            </Card>
          </div>
        </div>
      </Tabs.TabPane>

      <Tabs.TabPane key="byPatient" tab="按患者扫码">
        <!-- 按患者扫码的内容，类似上面的布局但左右互换 -->
        <div class="track-code-container">
          <!-- 实现按患者扫码的界面 -->
          <Empty description="按患者扫码功能开发中..." />
        </div>
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>
