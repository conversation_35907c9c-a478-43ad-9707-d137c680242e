<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Modal, Form, DatePicker, Radio, Button, message } from 'ant-design-vue'
import TrackCodeCore from './components/TrackCodeCore.vue'
import type { PropType } from 'vue'
import dayjs from 'dayjs'
import { mockOeList, mockFeeList, mockTrackCodeList } from './mock/data'

// 定义组件属性
const props = defineProps({
  // 对话框宽度
  modalWidth: {
    type: [Number, String],
    default: 1200,
  },
  // 追溯码ID
  wbSeqid: {
    type: [Number, String],
    default: '',
  },
  // 扫码方式：'byPatient'(按患者扫码), 'byMedicine'(按药品扫码)
  scanType: {
    type: String as PropType<'byPatient' | 'byMedicine'>,
    default: 'byMedicine',
  },
  // 医嘱用药申请数组
  oeList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  // 医嘱计费数组
  feeList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  // 追溯码使用数组
  trackCodeList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
})

// 定义事件
const emit = defineEmits(['success', 'cancel', 'scan', 'bind', 'unbind', 'invalid-code'])

// 对话框可见性
const visible = ref(false)

// 表单引用
const formRef = ref()

// 核心组件引用
const trackCodeCoreRef = ref()

// 表单数据
const formState = reactive({
  execType: 'byTimes', // 执行方式：按次(byTimes)、按天(byDay)
  dateRange: [dayjs(), dayjs()], // 执行日期范围
})

// 加载状态
const loading = ref(false)

// 医嘱用药申请数组
const oeListData = ref<any[]>([])

// 医嘱计费数组
const feeListData = ref<any[]>([])

// 追溯码使用数组
const trackCodeListData = ref<any[]>([])

// 追溯码ID
const trackCodeId = ref<string | number>('')

// 不再需要按钮文本计算属性

// 监听visible变化
watch(() => visible.value, (newVisible) => {
  if (newVisible) {
    // 打开弹窗时加载数据
    loadData()
  }
})

// 监听props中的数据变化
watch(() => props.oeList, (newOeList) => {
  if (newOeList && newOeList.length > 0) {
    oeListData.value = [...newOeList]
  }
}, { deep: true })

watch(() => props.feeList, (newFeeList) => {
  if (newFeeList && newFeeList.length > 0) {
    feeListData.value = [...newFeeList]
  }
}, { deep: true })

watch(() => props.trackCodeList, (newTrackCodeList) => {
  if (newTrackCodeList && newTrackCodeList.length > 0) {
    trackCodeListData.value = [...newTrackCodeList]
  }
}, { deep: true })

// 打开对话框
const open = async (id?: string | number) => {
  // 如果传入了ID，使用传入的ID，否则使用props中的wbSeqid
  trackCodeId.value = id || props.wbSeqid || ''

  // 显示对话框
  visible.value = true
}

// 关闭对话框
const close = () => {
  visible.value = false
  emit('cancel')
}

// 加载数据
const loadData = async () => {
  if (!trackCodeId.value && !props.wbSeqid) return

  loading.value = true

  try {
    // 如果props中提供了数据，则使用props中的数据
    if (props.oeList && props.oeList.length > 0) {
      oeListData.value = [...props.oeList]
    }

    if (props.feeList && props.feeList.length > 0) {
      feeListData.value = [...props.feeList]
    }

    if (props.trackCodeList && props.trackCodeList.length > 0) {
      trackCodeListData.value = [...props.trackCodeList]
    }

    // 如果props中没有提供数据，则从API加载数据
    if (oeListData.value.length === 0) {
      // 这里将来会调用API加载数据
      // 目前只是模拟加载过程
      await new Promise(resolve => setTimeout(resolve, 500))

      // 使用mock数据
      oeListData.value = [...mockOeList]
      feeListData.value = [...mockFeeList]
      trackCodeListData.value = [...mockTrackCodeList]
    }

    console.log('加载住院追溯码数据成功，ID:', trackCodeId.value || props.wbSeqid)
  } catch (error) {
    console.error('加载住院追溯码数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 查询数据
const queryData = async () => {
  // 验证表单
  try {
    await formRef.value.validate()

    // 根据表单数据查询
    loading.value = true

    try {
      // 这里将来会调用API查询数据
      // 目前只是模拟查询过程
      await new Promise(resolve => setTimeout(resolve, 500))

      // 模拟查询结果
      message.success('查询成功')
    } catch (error) {
      console.error('查询数据失败:', error)
      message.error('查询数据失败')
    } finally {
      loading.value = false
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 提交数据
const submitData = async () => {
  if (!trackCodeCoreRef.value) return

  loading.value = true

  try {
    // 获取新增和取消的绑定关系
    const newBindings = trackCodeCoreRef.value.getNewBindings()
    const cancelBindings = trackCodeCoreRef.value.getCancelBindings()

    // 这里将来会调用API提交数据
    // 目前只是模拟提交过程
    await new Promise(resolve => setTimeout(resolve, 500))

    // 提交成功
    message.success('追溯码绑定成功')

    // 触发成功事件
    emit('success', {
      trackCodes: trackCodeListData.value,
      newBindings,
      cancelBindings
    })

    // 关闭弹窗
    close()
  } catch (error) {
    console.error('提交数据失败:', error)
    message.error('提交数据失败')
  } finally {
    loading.value = false
  }
}

// 不再需要按钮点击和键盘事件处理

// 验证追溯码是否有效
const validateTrackCode = (code: string) => {
  if (!code) {
    return { valid: false, reason: '追溯码不能为空' }
  }

  // 验证追溯码长度，必须是19位到27位之间
  if (code.length < 19) {
    return {
      valid: false,
      reason: `追溯码长度不足19位 (当前长度: ${code.length})，不是有效的追溯码`
    }
  }

  if (code.length > 27) {
    return {
      valid: false,
      reason: `追溯码长度超过27位 (当前长度: ${code.length})，不是有效的追溯码`
    }
  }

  return { valid: true }
}

// 扫描追溯码
const scanCode = (code: string) => {
  if (!code) return

  // 触发扫描事件
  emit('scan', code)

  // 验证追溯码
  const validation = validateTrackCode(code)
  if (!validation.valid) {
    // 触发无效码事件
    emit('invalid-code', code, validation.reason)
    return
  }

  // 这里可以添加追溯码处理逻辑
  console.log('扫描到有效追溯码:', code)
}

// 不再需要键盘事件监听

// 暴露方法
defineExpose({
  open,
  close,
  scanCode,
  validateTrackCode
})
</script>

<template>
  <div class="wm-inpatient-track-code">
    <!-- 病区追溯码录入对话框 -->
    <Modal
      v-model:open="visible"
      title="病区追溯码录入"
      :width="modalWidth"
      :maskClosable="false"
      :destroyOnClose="true"
      @cancel="close"
    >
      <div class="ward-track-code-entry">
        <!-- 筛选条件 -->
        <Form
          ref="formRef"
          :model="formState"
          layout="inline"
          class="filter-form"
        >
          <Form.Item label="执行方式" name="execType">
            <Radio.Group v-model:value="formState.execType">
              <Radio.Button value="byTimes">按次执行</Radio.Button>
              <Radio.Button value="byDay">按天执行</Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Form.Item label="执行日期" name="dateRange">
            <DatePicker.RangePicker
              v-model:value="formState.dateRange"
              :allowClear="false"
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" @click="queryData" :loading="loading">查询</Button>
          </Form.Item>
        </Form>

        <!-- 追溯码核心组件 -->
        <TrackCodeCore
          ref="trackCodeCoreRef"
          :oeList="oeListData"
          :feeList="feeListData"
          :trackCodeList="trackCodeListData"
          mode="ward"
          :scanType="scanType"
        />
      </div>

      <template #footer>
        <Button @click="close">取消</Button>
        <Button type="primary" @click="submitData" :loading="loading">确认</Button>
      </template>
    </Modal>
  </div>
</template>
