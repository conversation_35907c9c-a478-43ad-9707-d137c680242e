// 医嘱用药申请数组模拟数据
export const mockOeList = [
  {
    visitId: 85027,
    oeNo: 42,
    patientName: '付美真',
    bedNo: '48',
    artId: 1048512,
    artTypeId: 11,
    totalPacks: 2,
    totalCells: 0,
    packCells: 1,
    artName: '丹参酮ⅡA磺酸钠注射液',
    artSpec: '2ml:10mg/支',
    producer: '上海上药第一生化药业有限公司',
    packUnit: '支',
    cellUnit: '支',
    isDisassembled: false,
    execDate: '2023-06-01',
    needTrackCode: true
  },
  {
    visitId: 84784,
    oeNo: 76,
    patientName: '张广英',
    bedNo: '9',
    artId: 1063668,
    artTypeId: 11,
    totalPacks: 0,
    totalCells: 2,
    packCells: 10,
    artName: '盐酸氨溴索注射液',
    artSpec: '4ml:30mg*10支',
    producer: '国药集团国瑞药业有限公司',
    packUnit: '盒',
    cellUnit: '支',
    isDisassembled: true,
    execDate: '2023-06-01',
    needTrackCode: true
  }
]

// 医嘱计费数组模拟数据
export const mockFeeList = [
  {
    visitId: 79299,
    oeNo: 14,
    artId: 1014892,
    totalPacks: 0,
    totalCells: 1.0000,
    meals: 1,
    bsnDate: 20250306
  },
  {
    visitId: 79299,
    oeNo: 14,
    artId: 1014892,
    totalCell: -1.0000,
    times: 1,
    bsnDate: 20250306
  },
  {
    visitId: 79299,
    oeNo: 15,
    artId: 1049455,
    totalCell: 1.0000,
    times: 1,
    bsnDate: 20250306
  }
]

// 追溯码使用数组模拟数据
export const mockTrackCodeList = [
  {
    cellsDispensed: 2,
    cellsRemain: 22,
    trackCode: '81122981263916924486',
    isDisassembled: 1,
    dispensedCount: 1,
    bsnDate: 20250520,
    dispensedNo: 1,
    oeNo: 42,
    visitId: 85027,
    timeDispensed: '2025-05-20',
    totalPacks: 0,
    totalCells: 2,
    artId: 1048512
  },
  {
    cellsDispensed: 1,
    cellsRemain: 0,
    trackCode: '09876543210987654321',
    isDisassembled: 0,
    dispensedCount: 1,
    bsnDate: 20250520,
    dispensedNo: 2,
    oeNo: 76,
    visitId: 84784,
    timeDispensed: '2025-05-20',
    totalPacks: 1,
    totalCells: 0,
    artId: 1063668
  }
]
