/* 住院追溯码组件样式 */
.wm-inpatient-track-code {
  /* 组件容器样式 */
}

/* 弹窗内容区域样式 */
.wm-inpatient-track-code .ant-modal-body {
  padding: 16px 24px;
}

/* 弹窗底部按钮区域样式 */
.wm-inpatient-track-code .ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 10px 16px;
}

/* 病区入口组件样式 */
.ward-track-code-entry {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ward-track-code-entry .filter-form {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

/* 药房入口组件样式 */
.pharmacy-track-code-entry {
  display: flex;
  flex-direction: column;
}

/* 核心组件样式 */
.wm-track-code-core {
  width: 100%;
}

.wm-track-code-core .track-code-container {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

/* 左侧药品列表样式 */
.wm-track-code-core .medicine-list {
  width: 40%;
  min-width: 300px;
}

.wm-track-code-core .medicine-card {
  height: 100%;
}

.wm-track-code-core .medicine-items {
  max-height: 500px;
  overflow-y: auto;
}

.wm-track-code-core .medicine-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s;
}

.wm-track-code-core .medicine-item:hover {
  background-color: #f5f5f5;
}

.wm-track-code-core .medicine-item-selected {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.wm-track-code-core .medicine-item-completed {
  border-color: #52c41a;
  background-color: #f6ffed;
}

.wm-track-code-core .medicine-item-warning {
  border-color: #faad14;
  background-color: #fffbe6;
}

.wm-track-code-core .medicine-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.wm-track-code-core .medicine-producer {
  color: #666;
  font-size: 12px;
  margin-bottom: 4px;
}

.wm-track-code-core .medicine-amount {
  font-size: 12px;
  color: #333;
}

.wm-track-code-core .medicine-status {
  position: absolute;
  top: 8px;
  right: 8px;
}

.wm-track-code-core .status-icon {
  font-size: 16px;
}

.wm-track-code-core .status-icon.completed {
  color: #52c41a;
}

.wm-track-code-core .status-icon.warning {
  color: #faad14;
}

/* 右侧患者列表样式 */
.wm-track-code-core .patient-list {
  width: 60%;
}

.wm-track-code-core .patient-card {
  height: 100%;
}

.wm-track-code-core .patient-items {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.wm-track-code-core .patient-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.wm-track-code-core .patient-item:hover {
  background-color: #f5f5f5;
}

.wm-track-code-core .patient-item-selected {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.wm-track-code-core .patient-item-bound {
  border-color: #52c41a;
}

.wm-track-code-core .patient-item-warning {
  border-color: #faad14;
  background-color: #fffbe6;
}

.wm-track-code-core .patient-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.wm-track-code-core .patient-amount {
  font-size: 12px;
  color: #333;
  margin-bottom: 8px;
}

.wm-track-code-core .patient-track-codes {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.wm-track-code-core .track-code-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wm-track-code-core .unbind-icon {
  color: #ff4d4f;
  cursor: pointer;
}

.wm-track-code-core .patient-warning {
  margin-top: 8px;
}

.wm-track-code-core .track-code-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
}

.wm-track-code-core .split-checkbox {
  min-width: 80px;
}
