# @mh-wm/inpatient-track-code
# 业务调整，次组件暂时搁置
# 业务调整，次组件暂时搁置
# 业务调整，次组件暂时搁置

住院追溯码组件，用于录入和管理住院药品追溯码信息。该组件支持药房和病区两种场景下的追溯码扫描、绑定和管理。

> **重要提示**：使用本组件时，必须引入样式文件 `import '@mh-wm/inpatient-track-code/index.css'`，否则组件样式将无法正常显示。

## 基础概念

### 医嘱
医生给住院患者开的医嘱，包含visitId（诊疗编号）、oeNo（医嘱序号）作为医嘱的主键。医嘱包含用药的条目编号（artId）、拆零系数（packCells）、给药数量（totalPacks整包数、totalCells拆零数）。

### 拆零系数
记录整包与拆零数量的转换系数。例如药品规格为1盒里装5支，packCells=5，表示1盒药（整包totalPacks）= 5支（拆零数totalCells）。

### 用药申请
每个医嘱都有频次、单次用量，它们会计算出药品的需求量。住院是以病区为单位向药房进行用药申请，一次申请包含病区的多个患者医嘱的申请量。

### 追溯码
每盒药都有一个唯一的追溯码，用于药品追溯管理。药品追溯码的长度通常在19位到27位之间，小于19位的码（如69开头的13位条形码）不是有效的追溯码，组件会给出警告提示，不能用于业务绑定。

市面上常见的是20位溯源码，其中前7位是药品标识，能标识一个药品的唯一性，与系统中的artId（条目编号）能够对应。未来数据健全后，组件将支持校验artId和追溯码前7位的对应关系，确保追溯码与药品匹配。

### 追溯码拆零标志
口服药一般按整盒发药，注射药有1盒多支的情况，多支拆零数共用1盒的一个溯源码，但会记录拆零标志位。

### 追溯码扫描
在摆药时扫描一个或多个溯源码对应一条医嘱的需求量。住院的注射药按拆零数执行，可能出现多支注射药扫同一个码的情况，也可能出现一个患者医嘱的用药需求跨多个追溯码的情况。

### 外部设备
追溯码的扫描通过外部设备（扫码枪或高拍仪）进行，识别后将文本传到组件里。扫码枪扫一个码将文本写入并触发回车事件，高拍仪可能一个追溯码对应一个回车事件，在文本文档查看的效果就是5个追溯码5行。

## 业务场景

住院追溯码组件支持两种主要业务场景：

1. **病区扫码模式**：患者所在病区的护士向药房做统领申请，药房发药，病区护士给患者扫追溯码摆药并计费。
2. **药房扫码模式**：患者所在病区的护士向药房做摆药申请，药房将药扫描追溯码摆好发给病区，护士执行计费。

组件提供两种扫码方式：

1. **按药品扫码**：将药分给患者，选中一种药列出哪些患者需要用这种药并为他们扫描追溯码。
2. **按患者扫码**：根据患者摆药，选中患者列出他的用药需求，选中药品再扫描追溯码。

## 组件架构

组件分为三个部分：

1. **病区入口组件**：
   - 以Modal弹窗形式呈现
   - 参考packages/InpatientHsd/OeExec/src/ExecForm的patientFormRef
   - 保留按次、按天执行、执行日期的日期范围等筛选条件
   - 这些筛选条件用于计算病区给药一次能执行几天的费用
   - 根据筛选条件计算出医嘱用药申请数组(oeList)，传入核心组件

2. **药房入口组件**：
   - 简单的Modal弹窗
   - 药房获取到的用药申请是病区发送过来的数据，已明确需求量
   - 直接从后台获取医嘱用药申请数据，传入核心组件

3. **核心组件**：
   - 非弹窗形式，普通div展示
   - 接收上面两个入口组件传入的数据进行业务处理
   - 包含两个卡片：按药给患者绑追溯码和按患者选药绑追溯码

### 核心组件布局

#### 按药给患者绑追溯码卡片
- 左右布局设计
- **左侧**：列出医嘱用药申请数组中需要用到的药品（按artId汇总）
  - 每个药品展示三行信息：
    - 第一行：药品名称(artName) + 规格(artSpec)
    - 第二行：生产厂家(producer)
    - 第三行：需求总量 - 整包数(totalPacks + packUnit) + 拆零数(totalCells + cellUnit)
  - 选中药品时样式突出显示
  - 状态标识：
    - 当药品对应的所有患者都已绑定追溯码时，药品卡片显示"已完成"标识（如打勾图标）
    - 当药品对应的患者中有需要解绑追溯码的情况时（如计费数据显示已退费），药品卡片显示警告标识和特殊颜色
- **右侧**：显示需要使用选中药品的患者列表
  - 患者卡片展示信息：
    - 第一行：床号 + 患者姓名
    - 第二行：需求量（整包数/拆零数）
    - 第三行：已绑定的追溯码（可能有多行，每个追溯码一行）
  - 状态标识：
    - 已绑定追溯码的患者卡片变色突出显示
    - 已绑定但需要解绑的患者卡片显示警告标识和特殊颜色
    - 每个追溯码旁边显示小图标，点击可解绑该追溯码
  - 底部放置追溯码输入框
  - 输入框左侧有"拆零"复选框
    - 当药品packCells > 1或isDisassembled为true时，自动勾选
    - 勾选"拆零"表示一盒药有多支可分给多个患者，此时可多选患者绑定同一个追溯码
    - 不勾选"拆零"时只能单选患者绑定追溯码

#### 按患者选药绑追溯码卡片
- 类似布局，但左侧显示患者列表，右侧显示选中患者的用药需求
- 患者可能需要多个追溯码的情况：
  - 当一个患者需要多支药品（如需求2支）时，可能需要绑定多个追溯码
  - 患者卡片中的追溯码区域设计为一个追溯码一行
  - 例如：一盒药已用完，需要开新盒药时，会为同一患者绑定多个追溯码

### 计费数据分析与提醒

组件会分析传入的计费数组(feeList)，根据医嘱的双主键(visitId+oeNo)对计费数据进行汇总：

1. **退费检测**：
   - 当计费数据汇总后数量抵消归零（正负值相抵为0）时，表示该医嘱已退费
   - 对于已退费但仍绑定了追溯码的医嘱，组件会通过以下方式提醒用户：
     - 左侧药品卡片显示警告标识和特殊颜色
     - 右侧对应的患者卡片显示警告标识和特殊颜色
     - 提示用户需要解绑这些追溯码

2. **绑定状态管理**：
   - 组件维护追溯码的绑定状态，包括初始绑定、新增绑定和解绑操作
   - 用户可以通过点击追溯码旁的解绑图标来解除绑定
   - 解绑操作会记录在组件的内部状态中，直到用户确认提交

## 功能特性

1. **追溯码录入**：支持通过扫码枪或高拍仪录入追溯码
2. **多场景支持**：适应药房扫码和病区扫码两种场景
3. **灵活的扫码方式**：支持按药品扫码和按患者扫码两种方式
4. **追溯码管理**：
   - 漏扫补录：支持多次打开，列出已建立关联的患者用药与追溯码关系，对未扫码药进行扫码
   - 退费处理：删除已绑定的患者药品与追溯码关系
   - 绑定关系切换：支持将追溯码从一个患者切换绑定到另一个患者
5. **历史记录**：记录初始绑定关系和多次操作，支持提交前的新绑定、撤销绑定关系
6. **提前领药支持**：适应医院提前领明天药品的业务场景
7. **医嘱与计费分析**：
   - visitId+oeNo是双组件能确定一条医嘱，医嘱会指定用药条目artId
   - 医嘱计费加了bsnDate属性，它是用来指定医嘱计费的日期
   - 根据医嘱的双主键对计费数组进行group by汇总后能计算出一条医嘱总共计了多少费用，正负值抵消后为0表示该医嘱已完全退费
   - 如果一条已退费的医嘱还绑定了追溯码，需要提醒用户处理
   - 如果一条医嘱没有计费记录，说明它还没被绑定过追溯码，可以进行扫码绑定

## 安装

```bash
# 安装组件
pnpm publish:component Wm/InpatientTrackCode
```

在package.json中添加依赖：

```json
{
  "dependencies": {
    "@mh-wm/inpatient-track-code": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}
```

## 样式引入

在使用组件时，需要手动引入样式文件，否则组件样式将无法正常显示：

```javascript
// 在项目入口文件（如main.js或main.ts）中引入样式
import '@mh-wm/inpatient-track-code/index.css'
```

或者在组件中单独引入：

```vue
<script setup>
import { InpatientTrackCode } from '@mh-wm/inpatient-track-code'
import '@mh-wm/inpatient-track-code/index.css'
</script>
```

## 组件

### InpatientTrackCode

住院追溯码录入组件，点击按钮打开弹窗，可以录入和管理住院药品追溯码信息。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| buttonText | string | '追溯码(F5)' | 按钮文本 |
| buttonType | string | 'default' | 按钮类型，可选值：'primary', 'default', 'dashed', 'text', 'link' |
| buttonSize | string | 'middle' | 按钮大小，可选值：'large', 'middle', 'small' |
| showButton | boolean | true | 是否显示触发按钮 |
| wbSeqid | number \| string | '' | 追溯码ID |
| hotkey | string | 'F5' | 绑定的键盘快捷键 |
| modalWidth | number \| string | 1200 | 对话框宽度 |
| mode | string | 'ward' | 扫码模式，可选值：'ward'(病区扫码), 'pharmacy'(药房扫码) |
| scanType | string | 'byPatient' | 扫码方式，可选值：'byPatient'(按患者扫码), 'byMedicine'(按药品扫码) |
| oeList | array | [] | 医嘱用药申请数组 |
| feeList | array | [] | 医嘱计费数组 |
| trackCodeList | array | [] | 追溯码使用数组 |

#### 事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| success | 追溯码录入成功时触发 | (data: {trackCodes: array, newBindings: array, cancelBindings: array}) => void |
| cancel | 取消追溯码录入时触发 | () => void |
| scan | 扫描追溯码时触发 | (code: string) => void |
| bind | 绑定追溯码与医嘱关系时触发 | (binding: object) => void |
| unbind | 解除追溯码与医嘱关系时触发 | (binding: object) => void |
| invalid-code | 扫描到无效追溯码时触发 | (code: string, reason: string) => void |

#### 方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| open | 打开追溯码录入对话框 | (wbSeqid?: number \| string) => void |
| close | 关闭追溯码录入对话框 | () => void |
| scanCode | 手动触发扫码 | (code: string) => void |
| submitBindings | 提交绑定关系 | () => Promise<{success: boolean, data: any}> |
| resetBindings | 重置绑定关系 | () => void |
| validateTrackCode | 验证追溯码是否有效 | (code: string) => {valid: boolean, reason?: string} |

#### 数据结构

##### 医嘱用药申请数组(oeList)
```typescript
interface OeItem {
  visitId: number | string;    // 诊疗编号
  oeNo: number | string;       // 医嘱序号
  patientName: string;         // 患者姓名
  bedNo: string;               // 床号
  artId: number;               // 药品条目编号
  artTypeId: number;           // 条目类型
  totalPacks: number;          // 整包数
  totalCells: number;          // 拆零数
  packCells: number;           // 拆零系数
  artName: string;             // 条目名称
  artSpec: string;             // 规格
  producer: string;            // 生产厂家
  packUnit: string;            // 整包单位
  cellUnit: string;            // 拆零单位
  isDisassembled: boolean;     // 需要拆零
  execDate: string;            // 执行日期
  needTrackCode: boolean;      // 是否需要追溯码
}
```

示例：
```json
[
  {
    "visitId": 85027,
    "oeNo": 42,
    "patientName": "付美真",
    "bedNo": "48",
    "artId": 1048512,
    "artTypeId": 11,
    "totalPacks": 2,
    "totalCells": 0,
    "packCells": 1,
    "artName": "丹参酮ⅡA磺酸钠注射液",
    "artSpec": " 2ml:10mg/支",
    "producer": "上海上药第一生化药业有限公司",
    "packUnit": "支",
    "cellUnit": "支",
    "isDisassembled": false,
    "execDate": "2023-06-01",
    "needTrackCode": true
  },
  {
    "visitId": 84784,
    "oeNo": 76,
    "patientName": "张广英",
    "bedNo": "9",
    "artId": 1063668,
    "artTypeId": 11,
    "totalPacks": 0,
    "totalCells": 2,
    "packCells": 10,
    "artName": "盐酸氨溴索注射液",
    "artSpec": " 4ml:30mg*10支",
    "producer": "国药集团国瑞药业有限公司",
    "packUnit": "盒",
    "cellUnit": "支",
    "isDisassembled": true,
    "execDate": "2023-06-01",
    "needTrackCode": true
  }
]
```

##### 医嘱计费数组(feeList)
```typescript
interface FeeItem {
  visitId: number | string;    // 诊疗编号
  oeNo: number | string;       // 医嘱序号
  artId: number;               // 药品条目编号
  totalPacks?: number;         // 整包数（可选）
  totalCells?: number;         // 拆零数（可选）
  totalCell?: number;          // 拆零数（新格式，与totalCells二选一）
  meals?: number;              // 餐次（可选）
  bsnDate: number | string;    // 执行日期（格式如：20250306）
}
```

> **说明**：
> 1. visitId+oeNo是双组件能确定一条医嘱，医嘱会指定用药条目artId
> 2. 医嘱计费加了bsnDate属性，它是用来指定医嘱计费的日期
> 3. 根据医嘱的双主键对计费数组进行group by汇总后能计算出一条医嘱总共计了多少费用，正负值抵消后为0表示该医嘱已完全退费
> 4. 如果一条已退费的医嘱还绑定了追溯码，需要提醒用户处理
> 5. 如果一条医嘱没有计费记录，说明它还没被绑定过追溯码，可以进行扫码绑定

示例：
```json
[
  {
    "visitId": 79299,
    "oeNo": 14,
    "artId": 1014892,
    "totalPacks": 0,
    "totalCells": 1.0000,
    "meals": 1,
    "bsnDate": 20250306
  },
  {
    "visitId": 79299,
    "oeNo": 14,
    "artId": 1014892,
    "totalCell": -1.0000,
    "bsnDate": 20250306
  },
  {
    "visitId": 79299,
    "oeNo": 15,
    "artId": 1049455,
    "totalCell": 1.0000,
    "bsnDate": 20250306
  }
]
```

##### 追溯码使用数组(trackCodeList)
```typescript
interface TrackCodeItem {
  trackCode: string;          // 追溯码
  visitId: number | string;   // 诊疗编号
  oeNo: number | string;      // 医嘱序号
  artId?: number;             // 药品条目编号（可选）
  cellsDispensed: number;     // 已用制剂数
  cellsRemain: number;        // 剩余制剂数
  isDisassembled: number;     // 拆零标志位（1表示拆零，0表示不拆零）
  dispensedCount: number;     // 分发次数
  bsnDate: number | string;   // 发药日期（格式如：20250520）
  dispensedNo: number;        // 分发序号
  timeDispensed: string;      // 发药时间
  totalPacks: number;         // 发药包装数
  totalCells: number;         // 发药制剂数
}
```

#### 示例

##### 基本用法
```vue
<template>
  <InpatientTrackCode
    :wbSeqid="wbSeqid"
    :oeList="oeList"
    :feeList="feeList"
    :trackCodeList="trackCodeList"
    mode="ward"
    scanType="byPatient"
    @success="handleSuccess"
    @cancel="handleCancel"
  />
</template>

<script setup>
import { InpatientTrackCode } from '@mh-wm/inpatient-track-code'
import '@mh-wm/inpatient-track-code/index.css'
import { ref } from 'vue'

// 追溯码ID
const wbSeqid = ref('12345')

// 医嘱用药申请数组
const oeList = ref([
  {
    visitId: 85027,
    oeNo: 42,
    patientName: '付美真',
    bedNo: '48',
    artId: 1048512,
    artTypeId: 11,
    totalPacks: 2,
    totalCells: 0,
    packCells: 1,
    artName: '丹参酮ⅡA磺酸钠注射液',
    artSpec: '2ml:10mg/支',
    producer: '上海上药第一生化药业有限公司',
    packUnit: '支',
    cellUnit: '支',
    execDate: '2023-06-01',
    needTrackCode: true
  },
  {
    visitId: 84784,
    oeNo: 76,
    patientName: '张广英',
    bedNo: '9',
    artId: 1063668,
    artTypeId: 11,
    totalPacks: 0,
    totalCells: 2,
    packCells: 10,
    artName: '盐酸氨溴索注射液',
    artSpec: '4ml:30mg*10支',
    producer: '国药集团国瑞药业有限公司',
    packUnit: '盒',
    cellUnit: '支',
    execDate: '2023-06-01',
    needTrackCode: true
  }
  // 更多医嘱...
])

// 医嘱计费数组
const feeList = ref([
  {
    visitId: 79299,
    oeNo: 14,
    artId: 1014892,
    totalPacks: 0,
    totalCells: 1.0000,
    meals: 1,
    bsnDate: 20250306
  },
  {
    visitId: 79299,
    oeNo: 14,
    artId: 1014892,
    totalCell: -1.0000,
    times: 1,
    bsnDate: 20250306
  },
  {
    visitId: 79299,
    oeNo: 15,
    artId: 1049455,
    totalCell: 1.0000,
    times: 1,
    bsnDate: 20250306
  },
  // 更多计费记录...
])

// 追溯码使用数组
const trackCodeList = ref([
  {
    cellsDispensed: 2,
    cellsRemain: 22,
    trackCode: '81122981263916924486',
    isDisassembled: 1,
    dispensedCount: 1,
    bsnDate: 20250520,
    dispensedNo: 1,
    oeNo: 14,
    visitId: 79299,
    timeDispensed: '2025-05-20',
    totalPacks: 0,
    totalCells: 2,
    artId: 1048512
  },
  {
    cellsDispensed: 1,
    cellsRemain: 0,
    trackCode: '09876543210987654321',
    isDisassembled: 0,
    dispensedCount: 1,
    bsnDate: 20250520,
    dispensedNo: 2,
    oeNo: 76,
    visitId: 84784,
    timeDispensed: '2025-05-20',
    totalPacks: 1,
    totalCells: 0,
    artId: 1063668
  },
  // 更多追溯码记录...
])

// 成功回调
const handleSuccess = (data) => {
  console.log('追溯码录入成功', data)
  // data包含:
  // - trackCodes: 所有追溯码记录
  // - newBindings: 新建立的绑定关系
  // - cancelBindings: 取消的绑定关系
}

// 取消回调
const handleCancel = () => {
  console.log('取消追溯码录入')
}
</script>
```

##### 通过js方法调用
```vue
<template>
  <!-- 不显示按钮，通过js方法调用 -->
  <InpatientTrackCode
    ref="trackCodeRef"
    :showButton="false"
    mode="pharmacy"
    scanType="byMedicine"
    @success="handleSuccess"
  />

  <!-- 自定义按钮 -->
  <Button @click="openTrackCode">打开追溯码录入</Button>
</template>

<script setup>
import { InpatientTrackCode } from '@mh-wm/inpatient-track-code'
import '@mh-wm/inpatient-track-code/index.css'
import { Button } from 'ant-design-vue'
import { ref, onMounted } from 'vue'

// 组件引用
const trackCodeRef = ref()

// 追溯码ID
const wbSeqid = ref('12345')

// 打开追溯码录入窗口
const openTrackCode = () => {
  trackCodeRef.value.open(wbSeqid.value)
}

// 成功回调
const handleSuccess = (data) => {
  console.log('追溯码录入成功', data)
}

// 加载数据并打开窗口
onMounted(async () => {
  // 可以在组件挂载后加载数据
  await loadData()
  // 然后打开窗口
  openTrackCode()
})

// 加载数据
const loadData = async () => {
  // 从API加载数据
  // ...
}
</script>
```

## 注意事项

1. 组件设计考虑了对高拍仪数据的接收处理，支持一个追溯码一个回车事件的模式。
2. 组件适应一级医院药房和病区人力紧张的情况，提供简单高效的操作流程。
3. 组件支持多次打开，可以查看和修改之前已建立的绑定关系。
4. 提交前会记录新建立的绑定关系和取消的绑定关系，便于后续处理。
5. 组件支持医院提前领明天药品的业务场景，可以区分不同执行日期的药品。
6. **追溯码长度验证**：组件会验证扫描的追溯码长度，只有19位到27位之间的码才被视为有效追溯码。如果扫描到小于19位的码（如69开头的13位条形码），组件会给出警告提示，不允许用于业务绑定。
7. **追溯码与药品匹配**：目前市面上常见的20位溯源码，其前7位是药品标识，能标识药品的唯一性。未来数据健全后，组件将支持校验artId和追溯码前7位的对应关系，确保扫描的追溯码与实际药品匹配。
