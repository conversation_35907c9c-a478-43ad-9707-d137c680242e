# ScmCust 供应商组件

供应商相关组件，包括：

1. ScmCustSelect：供应商选择组件，支持根据输入内容搜索供应商

## 特性

- 基于ant-design-vue的Select组件实现
- 支持单选和多选模式
- 支持根据输入内容实时搜索供应商
- 支持自定义显示字段（供应商名称或编码）
- 支持禁用和清除功能

## 依赖

- `ant-design-vue`: UI组件库
- `@mh-wm/util`: 工具库，包含API调用

## 安装

```bash
# 安装组件
pnpm publish:component Wm/ScmCust
```

### package.json依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-wm/scm-cust": "^1.0.0",
    "@mh-wm/util": "^1.0.4"
  }
}
```

## 使用方法

### 引入组件

```javascript
import { ScmCustSelect } from '@mh-wm/scm-cust'
```

### ScmCustSelect 基本用法

```vue
<template>
  <!-- 基础用法 - 供应商选择 -->
  <ScmCustSelect v-model="custId" style="width: 200px" />

  <!-- 显示供应商编码 -->
  <ScmCustSelect v-model="custId" showField="custCode" style="width: 200px" />

  <!-- 多选模式 -->
  <ScmCustSelect v-model="custIds" multiple />

  <!-- 使用机构供应商API -->
  <ScmCustSelect
    v-model="orgCustId"
    :orgPartner="true"
    style="width: 200px"
    placeholder="请输入机构供应商名称/编码"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 单选值
const custId = ref()

// 多选值
const custIds = ref([])

// 机构供应商选择值
const orgCustId = ref()
</script>
```

## 组件属性

### ScmCustSelect 属性

| 属性名 | 类型 | 默认值 | 说明 |
| ----- | ---- | ----- | ---- |
| v-model | Number/Array | - | 绑定值，多选时为数组 |
| allowClear | Boolean | true | 是否允许清除 |
| disabled | Boolean | false | 是否禁用 |
| multiple | Boolean | false | 是否多选 |
| placeholder | String | '请输入供应商名称/编码/五笔/拼音码' | 占位符文本 |
| width | String/Number | '100%' | 组件宽度 |
| showField | String | 'custName' | 显示字段，可选值：'custName'、'custCode'。当showField='custName'时，如果custCode为空则不显示括号内的编码 |
| searchType | String | 'all' | 搜索类型，可选值：'all'(全部)、'name'(名称)、'code'(编码)、'wubi'(五笔)、'pinyin'(拼音) |
| pageSize | Number | 20 | 每页显示条数 |
| loadFirstPage | Boolean | true | 是否默认加载第一页数据 |
| enablePagination | Boolean | true | 是否启用分页加载功能 |
| paginationMode | String | 'scroll' | 分页加载方式，可选值：'scroll'(滚动加载)、'button'(按钮加载) |
| maxPages | Number | 5 | 最大页数限制，防止加载过多数据 |
| orgPartner | Boolean | false | 是否使用机构供应商API，当为true时调用机构供应商API，为false时调用普通供应商API |

### 显示格式说明

组件根据`showField`属性和数据中的`custCode`字段决定显示格式：

1. 当`showField="custCode"`时：
   - 显示格式为：`编码 - 名称`
   - 例如：`1001 - 某供应商`
   - 如果`custCode`为空，则显示为：` - 某供应商`

2. 当`showField="custName"`时（默认）：
   - 如果`custCode`有值，显示格式为：`名称 (编码)`
   - 例如：`某供应商 (1001)`
   - 如果`custCode`为空，则只显示名称：`某供应商`
   - 这样可以避免显示空括号

## API说明

### ScmCustSelect 组件

组件内部根据orgPartner属性的值，使用不同的API：

#### 当orgPartner为false时（普通供应商）

1. 当用户输入搜索内容时，调用`/clinics_wm/scmcust/page`搜索供应商
   - 参数：

     ```json
     {
       "keyword": "搜索关键字",
       "pageSize": 20,
       "pageNum": 1,
       "searchField": "custName" // 可选，根据searchType属性决定
     }
     ```

   - 返回：分页数据，包含供应商列表
   - 支持按名称、编码、五笔码(qsCode1)和拼音码(qsCode2)搜索
   - 组件初始化时会自动加载第一页数据（当loadFirstPage=true时）

2. 当禁用分页时，调用`/clinics_wm/scmcust/findAll`获取所有供应商数据

#### 当orgPartner为true时（机构供应商）

1. 当用户输入搜索内容时，调用`/clinics_wm/orgpartner/orgPartnerPage`搜索机构供应商
   - 参数：

     ```json
     {
       "keyword": "搜索关键字",
       "pageSize": 20,
       "pageNum": 1,
       "searchField": "custName" // 可选，根据searchType属性决定
     }
     ```

   - 返回：分页数据，包含机构供应商列表
   - 支持按名称、编码、五笔码(qsCode1)和拼音码(qsCode2)搜索
   - 组件初始化时会自动加载第一页数据（当loadFirstPage=true时）

2. 当禁用分页时，调用`/clinics_wm/orgpartner/findAll`获取所有机构供应商数据
   - 返回数据包含：`custId`、`custName`、`custCode`、`qsCode1`、`qsCode2`四个关键属性
   - 组件会根据这四个属性进行前端过滤
   - `qsCode1`、`qsCode2`是拼音首字母和拼音的大写，搜索时会自动转大写进行匹配

组件使用了debounce防抖处理，避免频繁请求API。当用户输入停止300ms后才会发起请求。

## 机构供应商功能

当需要使用机构供应商数据时，可以通过设置`orgPartner`属性为`true`来启用机构供应商API。

### 使用方法

```vue
<template>
  <!-- 基础用法 - 使用机构供应商API -->
  <ScmCustSelect
    v-model="orgCustId"
    :orgPartner="true"
    style="width: 200px"
  />

  <!-- 带更多配置的机构供应商选择 -->
  <ScmCustSelect
    v-model="orgCustId"
    :orgPartner="true"
    showField="custCode"
    placeholder="请输入机构供应商编码"
    style="width: 300px"
  />

  <!-- 机构供应商多选模式 -->
  <ScmCustSelect
    v-model="orgCustIds"
    :orgPartner="true"
    multiple
    style="width: 100%"
  />

  <!-- 机构供应商 + 禁用分页（推荐用法） -->
  <ScmCustSelect
    v-model="orgCustId"
    :orgPartner="true"
    :enablePagination="false"
    style="width: 100%"
    placeholder="请输入机构供应商名称/编码/五笔/拼音"
  />

  <!-- 机构供应商 + 禁用分页 + 指定搜索类型 -->
  <ScmCustSelect
    v-model="orgCustId"
    :orgPartner="true"
    :enablePagination="false"
    searchType="pinyin"
    placeholder="请输入拼音搜索机构供应商"
    style="width: 100%"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 单选值
const orgCustId = ref()

// 多选值
const orgCustIds = ref([])
</script>
```

### 动态切换API类型

您也可以动态切换是否使用机构供应商API：

```vue
<template>
  <div>
    <div style="margin-bottom: 16px">
      <span style="margin-right: 8px">使用机构供应商API：</span>
      <a-switch v-model:checked="useOrgPartner" />
    </div>

    <ScmCustSelect
      v-model="custId"
      :orgPartner="useOrgPartner"
      style="width: 100%"
      :placeholder="useOrgPartner ? '请输入机构供应商名称' : '请输入供应商名称'"
    />
  </div>
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 选中的供应商ID
const custId = ref()

// 是否使用机构供应商API
const useOrgPartner = ref(false)
</script>
```

### 机构供应商非分页模式（推荐）

当使用机构供应商API且禁用分页时（`orgPartner="true"` + `enablePagination="false"`），组件具有以下特性：

#### 数据加载方式
- 调用 `orgPartnerFindAllApi` 一次性加载所有机构供应商数据
- 在前端进行实时过滤，响应速度更快
- 适合机构供应商数据量不大的场景（建议少于1000条）

#### 四字段智能搜索
组件会根据以下四个字段进行搜索过滤：

1. **custName**: 供应商名称
2. **custCode**: 供应商编码
3. **qsCode1**: 拼音首字母和五笔码（大写）
4. **qsCode2**: 拼音码（大写）

#### 搜索特性
- **大小写不敏感**: `qsCode1`、`qsCode2` 搜索时会自动转换为大写进行匹配
- **多字段搜索**: 默认搜索所有四个字段，也可通过 `searchType` 指定搜索特定字段
- **实时过滤**: 用户输入时立即在前端进行过滤，无需等待API响应

#### 使用示例

```vue
<template>
  <!-- 推荐用法：机构供应商 + 非分页 -->
  <ScmCustSelect
    v-model="orgCustId"
    :orgPartner="true"
    :enablePagination="false"
    style="width: 100%"
    placeholder="请输入机构供应商名称/编码/五笔/拼音"
  />

  <!-- 指定搜索类型 -->
  <ScmCustSelect
    v-model="orgCustId"
    :orgPartner="true"
    :enablePagination="false"
    searchType="pinyin"
    placeholder="请输入拼音搜索"
    style="width: 100%"
  />

  <!-- 多选模式 -->
  <ScmCustSelect
    v-model="orgCustIds"
    :orgPartner="true"
    :enablePagination="false"
    multiple
    style="width: 100%"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

const orgCustId = ref()
const orgCustIds = ref([])
</script>
```

## 分页加载功能

组件支持两种分页加载方式：

1. **滚动加载（默认）**：当用户滚动到下拉列表底部时，自动加载下一页数据
2. **按钮加载**：在下拉列表底部显示"加载更多"按钮，点击时加载下一页数据

### 使用方法

```vue
<!-- 默认滚动加载 -->
<ScmCustSelect v-model="custId" :pageSize="10" enablePagination />

<!-- 按钮加载 -->
<ScmCustSelect
  v-model="custId"
  :pageSize="10"
  enablePagination
  paginationMode="button"
  :maxPages="3"
/>

<!-- 禁用分页加载 -->
<ScmCustSelect v-model="custId" :enablePagination="false" />
```

### 暴露的方法

组件暴露了以下方法，可以通过ref调用：

```javascript
// 获取组件引用
const scmCustSelectRef = ref()

// 手动加载更多数据
scmCustSelectRef.value.loadMore()

// 获取当前页码
const currentPage = scmCustSelectRef.value.getCurrentPage()

// 获取总页数
const totalPages = scmCustSelectRef.value.getTotalPages()

// 检查是否还有更多数据
const hasMore = scmCustSelectRef.value.getHasMore()

// 检查是否已加载全部数据
const allDataLoaded = scmCustSelectRef.value.getAllDataLoaded()
```
