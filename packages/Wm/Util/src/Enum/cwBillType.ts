/**
 * 业务单据类型枚举
 */
export enum CwBillType {
  // 采购相关
  PURCHASE_IN = 11, // 采购入库
  PURCHASE_RETURN = 12, // 采购退货
  PURCHASE_DIRECT = 13, // 采购直调
  OTHER_IN = 19, // 其他入库

  // 销售相关
  SALE_OUT = 21, // 销售出库
  SALE_RETURN = 22, // 销售退回
  CONSUME_OUT = 23, // 消耗出库
  OTHER_OUT = 29, // 其他出库

  // 调拨相关
  TRANSFER_OUT = 31, // 调拨出库
  TRANSFER_RETURN = 32, // 调出退回
  TRANSFER_IN = 41, // 调拨入库
  TRANSFER_IN_RETURN = 42, // 调入退出

  // 损溢相关
  OVERFLOW_IN = 51, // 报溢入库
  LOSS_OUT = 52, // 报损出库
  PRICE_ADJUST = 61, // 调价损溢
  PROFIT_ADJUST = 62, // 销售返利
  SPLIT_ASSEMBLE = 71, // 拆零拼装
  BATCH_ADJUST = 72, // 批号调整
}

/**
 * 获取业务单据类型名称
 * @param type 业务单据类型
 * @returns 业务单据类型名称
 */
export function getCwBillTypeName(type: CwBillType): string {
  const typeMap: Record<CwBillType, string> = {
    [CwBillType.PURCHASE_IN]: '采购入库',
    [CwBillType.PURCHASE_RETURN]: '采购退货',
    [CwBillType.PURCHASE_DIRECT]: '采购直调',
    [CwBillType.OTHER_IN]: '其他入库',
    [CwBillType.SALE_OUT]: '销售出库',
    [CwBillType.SALE_RETURN]: '销售退回',
    [CwBillType.CONSUME_OUT]: '消耗出库',
    [CwBillType.OTHER_OUT]: '其他出库',
    [CwBillType.TRANSFER_OUT]: '调拨出库',
    [CwBillType.TRANSFER_RETURN]: '调出退回',
    [CwBillType.TRANSFER_IN]: '调拨入库',
    [CwBillType.TRANSFER_IN_RETURN]: '调入退出',
    [CwBillType.OVERFLOW_IN]: '报溢入库',
    [CwBillType.LOSS_OUT]: '报损出库',
    [CwBillType.PRICE_ADJUST]: '调价损溢',
    [CwBillType.PROFIT_ADJUST]: '利润调整',
    [CwBillType.SPLIT_ASSEMBLE]: '拆零拼装',
  }
  return typeMap[type] || '未知类型'
}

/**
 * 获取业务单据类型列表
 * @returns 业务单据类型列表
 */
export function getCwBillTypeList(): { title: string; value: number }[] {
  return Object.entries(CwBillType)
    .filter(([key]) => isNaN(Number(key)))
    .map(([key, value]) => ({
      title: getCwBillTypeName(value as CwBillType),
      value: value as number
    }))
}
