import { http } from '@idmy/core'

/**
 * 获取所有仓库列表
 * @param params 查询参数
 * @returns 仓库列表
 */
export function findAllDeptLsApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptcustmap/findAll', params, { appKey: 'wm' })
}

/**
 * 获取仓库列表
 * @param params 查询参数
 * @returns 仓库列表
 */
export function listDeptApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptcustmap/list', params, { appKey: 'wm' })
}

/**
 * 科室网来单位映射分页查询
 * @param params 查询参数
 * @returns 科室网来单位映射列表
 */
export function deptCustMapPageApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptcustmap/page', params, { appKey: 'wm' })
}

/**
 * 获取科室网来单位映射详情
 * @param params 查询参数，包含deptCode
 * @returns 科室网来单位映射详情
 */
export function deptCustMapInfoApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptcustmap/info', params, { appKey: 'wm' })
}

/**
 * 保存科室网来单位映射
 * @param params 科室网来单位映射信息
 * @returns 保存结果
 */
export function saveDeptCustMapApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptcustmap/save', params, { appKey: 'wm' })
}

/**
 * 删除科室网来单位映射
 * @param params 包含deptCode
 * @returns 删除结果
 */
export function deleteDeptCustMapApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptcustmap/delete', params, { appKey: 'wm' })
}
