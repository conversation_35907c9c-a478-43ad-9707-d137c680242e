import { http } from '@idmy/core'

/**
 * 盘点单分页查询
 * @param params 查询参数
 * @returns 盘点单列表
 */
export function countPageApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmcount/page', params, { appKey: 'wm' })
}

/**
 * 盘点单详情
 * @param params 查询参数，包含countId
 * @returns 盘点单详情
 */
export function countInfoApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmcount/info', params, { appKey: 'wm' })
}

/**
 * 创建盘点单
 * @param params 盘点单信息和品种列表
 * @returns 创建结果
 */
export function createCountApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmcount/addBatchArt', params, { appKey: 'wm' })
}

/**
 * 更新盘点明细
 * @param params 盘点明细信息
 * @returns 更新结果
 */
export function updateCountDetailApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmcount/updateArtAndDiffCalc', params, { appKey: 'wm' })
}

/**
 * 盘点明细列表
 * @param params 查询参数，包含countId和pageNo
 * @returns 盘点明细列表
 */
export function countDetailListApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmcount/diffListByCountId', params, { appKey: 'wm' })
}

/**
 * 复盘操作
 * @param params 包含countId
 * @returns 操作结果
 */
export function recheckApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmcount/recheck', params, { appKey: 'wm' })
}

/**
 * 复核操作
 * @param params 包含countId
 * @returns 操作结果
 */
export function finishApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmcount/finish', params, { appKey: 'wm' })
}

/**
 * 导出初始库存明细
 * @param params 包含countId
 * @returns 文件流
 */
export function exportInitStockApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmcount/downloadInitSotck', params, { 
    appKey: 'wm',
    responseType: 'blob'
  })
}

/**
 * 导入盘点明细
 * @param formData 表单数据，包含文件和countId
 * @returns 导入结果
 */
export function importCountDetailApi(formData: FormData): Promise<any> {
  return http.post('/clinics_wm/wmcount/updateCountDetail', formData, { 
    appKey: 'wm',
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

/**
 * 添加盘点品种
 * @param params 包含countId和artId
 * @returns 操作结果
 */
export function addCountArtApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmcount/addArt', params, { appKey: 'wm' })
}

/**
 * 删除盘点品种
 * @param params 包含countId和lineNo
 * @returns 操作结果
 */
export function delCountArtApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmcount/delArt', params, { appKey: 'wm' })
}

/**
 * 作废盘点单
 * @param params 包含countId
 * @returns 操作结果
 */
export function cancelCountApi(params: any): Promise<any> {
  return http.post('/clinics_wm/wmcount/cancel', params, { appKey: 'wm' })
}
