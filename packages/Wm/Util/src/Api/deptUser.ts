import { http } from '@idmy/core'

/**
 * 科室用户分页查询
 * @param params 查询参数
 * @returns 科室用户列表
 */
export function deptUserPageApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptuser/page', params, { appKey: 'wm' })
}

/**
 * 获取科室用户详情
 * @param params 查询参数，包含deptCode和userId
 * @returns 科室用户详情
 */
export function deptUserInfoApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptuser/info', params, { appKey: 'wm' })
}

/**
 * 保存科室用户
 * @param params 科室用户信息
 * @returns 保存结果
 */
export function saveDeptUserApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptuser/save', params, { appKey: 'wm' })
}

/**
 * 删除科室用户
 * @param params 包含deptCode和userId
 * @returns 删除结果
 */
export function deleteDeptUserApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptuser/delete', params, { appKey: 'wm' })
}
