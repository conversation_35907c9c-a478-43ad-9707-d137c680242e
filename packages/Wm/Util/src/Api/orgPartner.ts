import { http } from '@idmy/core'

/**
 * 机构供应商分页查询
 * @param params 查询参数
 * @returns 机构供应商列表
 */
export async function orgPartnerPageApi(params: any): Promise<any> {
  // 确保至少有pageNum参数
  const requestParams = {
    ...params,
    pageNum: params.pageNum || 1,
  }

  try {
    const response = await http.post('/clinics_wm/orgpartner/orgPartnerPage', requestParams, { appKey: 'wm' })

    // 检查响应结构
    if (response && typeof response === 'object') {
      const typedResponse = response as any

      // 直接检查是否有list字段，因为返回的数据结构可能是直接包含list的对象
      if (typedResponse.list && Array.isArray(typedResponse.list)) {
        console.log('[API] 供应商列表数量:', typedResponse.list.length)
      }
      // 检查是否有data.list结构
      else if (typedResponse.data && Array.isArray(typedResponse.data.list)) {
        console.log('[API] 供应商列表数量:', typedResponse.data.list.length)
      }
      // 检查是否有code字段，表示标准API响应
      else if (typedResponse.code === 0) {
        console.log('[API] 响应成功，但未找到预期的列表数据')
      } else {
        console.warn('[API] 响应结构不符合预期:', response)
      }
    }

    return response
  } catch (error) {
    console.error('[API] orgPartnerPageApi错误:', error)
    throw error
  }
}

/**
 * 获取所有机构供应商数据（不分页）
 * @param params 查询参数
 * @returns 所有机构供应商列表
 */
export function orgPartnerFindAllApi(params: any = {}): Promise<any> {
  return http.post('/clinics_wm/orgpartner/findAll', params, { appKey: 'wm' })
}
