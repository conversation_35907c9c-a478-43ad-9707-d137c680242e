<script setup lang="ts">
import { Select, Spin } from 'ant-design-vue'
import { Data, http } from '@idmy/core'
import { ref, watch, onMounted } from 'vue'
import { findAllDeptLsApi, listDeptApi } from '@mh-wm/util'

const props = defineProps({
  multiple: {
    type: Boolean,
    default: false,
  },
  own: {
    type: Boolean,
    default: false,
  },
  // 业务类型，用于过滤药房列表
  bsnType: {
    type: Number,
    default: 0,
  },
  // 是否用于处方发药
  forRecipeDispensing: {
    type: Boolean,
    default: false,
  },
  // 是否用于科室请领
  forSectionReq: {
    type: Boolean,
    default: false,
  },
  // 是否用于库存调拨源
  forMoveSource: {
    type: Boolean,
    default: false,
  },
  // 是否为药房
  forPharmacy: {
    type: Boolean,
    default: false,
  },
  // 是否为仓库
  forWms: {
    type: Boolean,
    default: false,
  },
  // 是否管理员
  isOperator: {
    type: Boolean,
    default: false,
  },
  // 是否审核员
  isChecker: {
    type: Boolean,
    default: false,
  },
  // 是否调度员
  isScheduler: {
    type: Boolean,
    default: false,
  },
})

const modelValue = defineModel<any>()

// 药房列表
const pharmacyList = ref<any[]>([])
const loading = ref(false)

// 获取药房列表
const fetchPharmacies = async () => {
  loading.value = true
  try {
    // 构造请求参数
    const params: any = {}

    // 根据业务类型设置参数
    if (props.bsnType > 0) {
      params.bsnType = props.bsnType
    }

    // 设置其他过滤条件
    if (props.forRecipeDispensing) {
      params.forRecipeDispensing = 1
    }

    if (props.forSectionReq) {
      params.forSectionReq = 1
    }

    if (props.forMoveSource) {
      params.forMoveSource = 1
    }

    if (props.forPharmacy) {
      params.forPharmacy = 1
    }

    if (props.forWms) {
      params.forWms = 1
    }

    if (props.isOperator) {
      params.isOperator = 1
    }

    if (props.isChecker) {
      params.isChecker = 1
    }

    if (props.isScheduler) {
      params.isScheduler = 1
    }

    // 根据own属性决定调用哪个API
    let response
    if (props.own) {
      // 调用list API，获取当前用户的药房列表
      response = await listDeptApi(params)
    } else {
      // 调用findAll API，获取所有药房列表
      response = await findAllDeptLsApi(params)
    }

    // 处理API返回的数据
    if (response && Array.isArray(response)) {
      pharmacyList.value = response
        .filter(row => row.disabled !== 1)
        .map(row => ({
          code: row.deptCode,
          id: row.deptCode, // 使用deptCode作为药房ID
          value: row.deptCode, // 使用deptCode作为value
          label: `${row.deptName} (${row.deptCode})`, // 在显示内容中展示deptCode
          data: row,
        }))
    } else {
      console.error('获取药房列表返回格式不正确:', response)
      pharmacyList.value = []
    }
  } catch (error) {
    console.error('获取药房列表失败:', error)
    pharmacyList.value = []
  } finally {
    loading.value = false
  }
}

// 初始加载
onMounted(() => {
  fetchPharmacies()
})

// 监听own属性变化，重新加载列表
watch(
  () => props.own,
  () => {
    fetchPharmacies()
  }
)

// 监听业务类型变化，重新加载列表
watch(
  () => props.bsnType,
  () => {
    fetchPharmacies()
  }
)

// 监听其他过滤条件变化，重新加载列表
watch(
  () => [props.forRecipeDispensing, props.forSectionReq, props.forMoveSource, props.forPharmacy, props.forWms, props.isOperator, props.isChecker, props.isScheduler],
  () => {
    fetchPharmacies()
  },
  { deep: true }
)
</script>

<template>
  <Spin :spinning="loading">
    <Select
      v-model:value="modelValue"
      :mode="props.multiple ? 'multiple' : undefined"
      :options="pharmacyList"
      placeholder="请选择药房"
      :fieldNames="{ label: 'label', value: 'value' }"
      :showSearch="true"
      :filterOption="(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())"
      :allowClear="true"
      style="width: 100%"
    />
  </Spin>
</template>
