# ReqComponent 品种申请表单组件

品种申请表单组件，用于库房管理系统中快速录入品种申请信息。该组件提供了一个完整的表单界面，支持品种选择、信息填写和数据验证。

## 特性

- 集成WmDeptArtSelect组件进行品种选择
- 支持缓存选中的品种数据
- 提供完整的表单验证功能
- 支持回车键顺序导航表单字段
- 自动填充品种相关信息（包装单位、拆零单位等）
- 支持日期格式验证（YYYYMMDD）
- 提供表单清空和重置功能
- 支持外部传入仓库编码（deptCode）
- 在添加品种成功后自动清空表单并聚焦回品种选择框

## 依赖

- `ant-design-vue`: UI组件库
- `@mh-inpatient-hsd/selector`: 提供WmDeptArtSelect组件

## 打包与安装

### 打包组件

在开发完成后，需要打包组件以便发布和使用：

```bash
# 在项目根目录下执行打包命令
pnpm publish:component Wm/ReqComponent
```

这个命令会执行以下操作：
1. 编译组件源码
2. 生成类型声明文件
3. 打包CSS样式
4. 生成组件包到dist目录
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库

### 安装组件

在其他项目中安装该组件：

```bash
# 使用npm安装
npm install @mh-wm/req-component

# 或使用pnpm安装
pnpm add @mh-wm/req-component
```

### package.json依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-wm/req-component": "^1.0.0",
    "@mh-inpatient-hsd/selector": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}
```

## 使用方法

### 引入组件

```javascript
import { ReqComponent } from '@mh-wm/req-component'
```

### 基本用法

```vue
<template>
  <ReqComponent
    :deptCode="deptCode"
    @addArt="handleAddArt"
  />
</template>

<script setup>
import { ReqComponent } from '@mh-wm/req-component'
import { ref } from 'vue'

// 仓库编码
const deptCode = ref('000013')

// 添加品种回调
const handleAddArt = (formData) => {
  console.log('添加品种成功，表单数据：', formData)
  // formData包含表单数据和选中的品种数据
}
</script>
```

## 组件属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| deptCode | 仓库编码 | string | '' |
| hasStock | 是否只显示有库存的品种 | boolean | false |

## 组件事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| addArt | 添加品种成功时触发 | (formData: object) => void |

## 组件方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| getSelectedArtData | 获取当前选中的品种数据 | () => any |
| clearFormData | 清空表单数据 | () => void |

## 表单字段说明

| 字段 | 说明 | 类型 | 是否必填 |
| --- | --- | --- | --- |
| artData | 选中的品种数据 | object | 是 |
| originPlace | 原产地 | string | 否 |
| batchNo | 生产批号 | string | 否 |
| dateManufactured | 生产日期（YYYYMMDD） | string | 是 |
| expiry | 有效期至（YYYYMMDD） | string | 是 |
| packPrice | 整包单价 | number | 否 |
| totalPacks | 整包数量 | number | 是（与totalCells至少填一个） |
| totalCells | 拆零数量（仅当splittable=1时显示） | number | 是（与totalPacks至少填一个） |
| packUnit | 包装单位 | string | 自动填充 |
| cellUnit | 拆零单位 | string | 自动填充 |
| splittable | 是否可拆零 | number | 自动填充 |

## 回车键导航顺序

组件支持通过回车键在表单字段之间导航，顺序如下：

1. 品种查询
2. 原产地
3. 生产批号
4. 生产日期
5. 有效期至
6. 整包单价
7. 整包数量
8. 拆零数量（如果可拆零）
9. 提交表单

## 示例

### 基本用法

```vue
<template>
  <div class="req-component-container">
    <h2>品种申请表单</h2>
    <ReqComponent
      :deptCode="deptCode"
      @addArt="handleAddArt"
    />

    <!-- 显示已添加的品种列表 -->
    <div v-if="addedItems.length > 0" class="added-items-container">
      <h3>已添加品种列表</h3>
      <a-table :dataSource="addedItems" :columns="columns" rowKey="id" />
    </div>
  </div>
</template>

<script setup>
import { ReqComponent } from '@mh-wm/req-component'
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

// 仓库编码
const deptCode = ref('000013')

// 已添加的品种列表
const addedItems = ref([])

// 表格列定义
const columns = [
  { title: '序号', dataIndex: 'id', key: 'id' },
  { title: '品种名称', dataIndex: 'artName', key: 'artName' },
  { title: '规格', dataIndex: 'artSpec', key: 'artSpec' },
  { title: '生产批号', dataIndex: 'batchNo', key: 'batchNo' },
  { title: '生产日期', dataIndex: 'dateManufactured', key: 'dateManufactured' },
  { title: '有效期至', dataIndex: 'expiry', key: 'expiry' },
  { title: '整包数量', dataIndex: 'totalPacks', key: 'totalPacks' },
  { title: '包装单位', dataIndex: 'packUnit', key: 'packUnit' },
  { title: '拆零数量', dataIndex: 'totalCells', key: 'totalCells' },
  { title: '拆零单位', dataIndex: 'cellUnit', key: 'cellUnit' }
]

// 添加品种回调
const handleAddArt = (formData) => {
  // 添加唯一ID
  const newItem = {
    id: Date.now(),
    ...formData,
    artName: formData.artData.artName,
    artSpec: formData.artData.artSpec
  }

  // 添加到列表
  addedItems.value.push(newItem)

  // 提示添加成功
  message.success(`成功添加品种: ${newItem.artName}`)

  // 处理添加品种的逻辑，例如保存到数据库等
  console.log('添加品种成功，表单数据：', formData)
}
</script>

<style scoped>
.req-component-container {
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.added-items-container {
  margin-top: 20px;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
}
</style>
```

### 在表单中使用

```vue
<template>
  <div class="form-container">
    <a-form :model="formState" layout="vertical">
      <a-form-item label="申请单号" name="reqNo">
        <a-input v-model:value="formState.reqNo" placeholder="系统自动生成" disabled />
      </a-form-item>

      <a-form-item label="申请科室" name="deptName">
        <a-select v-model:value="formState.deptCode" @change="handleDeptChange">
          <a-select-option v-for="dept in deptList" :key="dept.deptCode" :value="dept.deptCode">
            {{ dept.deptName }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="申请人" name="reqUser">
        <a-input v-model:value="formState.reqUser" />
      </a-form-item>

      <a-form-item label="申请日期" name="reqDate">
        <a-date-picker v-model:value="formState.reqDate" style="width: 100%" />
      </a-form-item>

      <a-form-item label="品种信息" name="items">
        <ReqComponent
          ref="reqComponentRef"
          :deptCode="formState.deptCode"
          @addArt="handleAddArt"
        />

        <!-- 已添加品种列表 -->
        <div v-if="formState.items.length > 0" class="items-table">
          <a-table
            :dataSource="formState.items"
            :columns="columns"
            rowKey="id"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" danger @click="removeItem(record.id)">删除</a-button>
              </template>
            </template>
          </a-table>
        </div>
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formState.remark" :rows="3" />
      </a-form-item>

      <a-form-item>
        <a-button type="primary" @click="handleSubmit">提交申请</a-button>
        <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ReqComponent } from '@mh-wm/req-component'
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

// 组件引用
const reqComponentRef = ref()

// 科室列表
const deptList = ref([
  { deptCode: '000013', deptName: '西药库' },
  { deptCode: '000014', deptName: '中药库' },
  { deptCode: '000015', deptName: '耗材库' }
])

// 表单数据
const formState = reactive({
  reqNo: 'REQ' + Date.now(),
  deptCode: '000013',
  deptName: '西药库',
  reqUser: '张三',
  reqDate: dayjs(),
  items: [],
  remark: ''
})

// 表格列定义
const columns = [
  { title: '品种名称', dataIndex: 'artName', key: 'artName' },
  { title: '规格', dataIndex: 'artSpec', key: 'artSpec' },
  { title: '生产批号', dataIndex: 'batchNo', key: 'batchNo' },
  { title: '生产日期', dataIndex: 'dateManufactured', key: 'dateManufactured' },
  { title: '有效期至', dataIndex: 'expiry', key: 'expiry' },
  { title: '整包数量', dataIndex: 'totalPacks', key: 'totalPacks' },
  { title: '包装单位', dataIndex: 'packUnit', key: 'packUnit' },
  { title: '操作', key: 'action' }
]

// 科室变更处理
const handleDeptChange = (value) => {
  const dept = deptList.value.find(item => item.deptCode === value)
  if (dept) {
    formState.deptName = dept.deptName
  }
}

// 添加品种回调
const handleAddArt = (formData) => {
  // 添加唯一ID
  const newItem = {
    id: Date.now(),
    ...formData,
    artName: formData.artData.artName,
    artSpec: formData.artData.artSpec
  }

  // 添加到列表
  formState.items.push(newItem)

  // 提示添加成功
  message.success(`成功添加品种: ${newItem.artName}`)
}

// 删除品种
const removeItem = (id) => {
  const index = formState.items.findIndex(item => item.id === id)
  if (index !== -1) {
    formState.items.splice(index, 1)
    message.success('删除成功')
  }
}

// 提交表单
const handleSubmit = () => {
  if (formState.items.length === 0) {
    message.error('请至少添加一个品种')
    return
  }

  // 这里可以添加表单提交逻辑
  message.success('申请单提交成功')
  console.log('提交的表单数据:', formState)
}

// 重置表单
const handleReset = () => {
  formState.items = []
  formState.remark = ''
  message.info('表单已重置')
}

// 获取选中的品种数据
const getSelectedData = () => {
  const selectedData = reqComponentRef.value.getSelectedArtData()
  console.log('当前选中的品种数据:', selectedData)
  return selectedData
}
</script>

<style scoped>
.form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.items-table {
  margin-top: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}
</style>
```

## 注意事项

1. 生产日期和有效期至必须填写，且格式为YYYYMMDD
2. 整包数量和拆零数量至少填写一个
3. 必须先选择品种才能提交表单
4. 添加品种成功后会自动清空表单并聚焦回品种选择框
5. 如果品种不可拆零（splittable !== 1），则不会显示拆零数量字段

## 开发和发布流程

### 开发流程

1. **开发组件**：在 `packages/Wm/ReqComponent/src/` 目录下开发组件
2. **测试组件**：在示例页面中测试组件功能
3. **更新文档**：更新README.md文档和使用示例

### 发布流程

1. **打包组件**：开发完成后，执行打包命令
   ```bash
   pnpm publish:component Wm/ReqComponent
   ```

2. **版本管理**：
   - 正式版本：`pnpm publish:component Wm/ReqComponent`
   - 测试版本：`pnpm publish:test-component Wm/ReqComponent`
   - 开发版本：`pnpm publish:dev-component Wm/ReqComponent`

### 使用流程

1. **安装组件**：在项目中安装组件
   ```bash
   pnpm add @mh-wm/req-component
   ```

2. **引入组件**：在Vue文件中引入并使用
   ```javascript
   import { ReqComponent } from '@mh-wm/req-component'
   ```

3. **配置依赖**：确保项目中已安装必要的依赖包
