<script setup lang="ts">
import { ref, reactive, nextTick, computed } from 'vue'
import { message } from 'ant-design-vue'
import { WmArticlePageByOrgSelect } from '@mh-inpatient-hsd/selector'
import { orgArtInfoApi, orgCustMapInfoApi } from '@mh-wm/util'
import OrgArtPriceSetting from './org-art-price-setting.vue'

// 定义组件事件
const emit = defineEmits(['addArt'])

// 表单数据
const formState = reactive({
  deptCode: '',
  artIds: [] as number[],
  hasStock: false,

})

const orgCustMap = ref({})
const loadOrgCustMap = async () => {
  const  data  = await orgCustMapInfoApi({})
  orgCustMap.value = data
}

// 缓存选中的品种数据
const selectedArtData = ref(null)
const item = ref(null)
// 搜索表单数据
const searchFormModel = reactive({
  batchNo: '',
  expiry: '',
  originPlace: '',
  dateManufactured: '',
  packPrice: undefined,
  stockPacksTotal: '',
  totalPacks: undefined,
  stockCellsTotal: undefined,
  packCells: undefined,
  totalCells: '',
  cellPrice: undefined,
  packUnit: undefined,
  cellUnit: '',
  splittable: undefined,
})

// 品种选择组件引用
const artSelectRef = ref()

// 机构商品设置弹窗相关
const orgArtPriceSettingVisible = ref(false)
const orgArtPriceSettingRef = ref()

// 添加品种
const onAddArt = () => {
  // 验证表单是否为空

  // 验证数量是否为空
  if (searchFormModel.totalPacks <= 0 && (searchFormModel.splittable !== 1 || Number(searchFormModel.totalCells) <= 0)) {
    message.error('请输入有效的数量')
    return
  }

  // 验证是否选择了品种
  if (!selectedArtData.value) {
    message.error('请先选择一个品种')
    return
  }

  // 验证生产日期不为空且格式正确
  if (!validateDateManufactured()) {
    return
  }

  // 验证有效期至不为空且格式正确
  if (!validateExpiry()) {
    return
  }

  // 克隆当前表单数据
  const formData = {
    ...searchFormModel,
    // 添加选中的品种数据
    artData: selectedArtData.value,
  }

  // 发射事件，返回表单数据
  emit('addArt', formData)

  // 清空表单
  clearFormData()

  // 聚焦回品种选择框，方便继续添加
  nextTick(() => {
    if (artSelectRef.value && typeof artSelectRef.value.focus === 'function') {
      artSelectRef.value.focus()
    }
  })
  console.log('添加品种成功，表单数据：', formData)
  // 返回表单数据，方便外部使用
  return formData
}
// 处理品种选择
const handleArtSelect = async (selectedArt: any) => {
  if (!selectedArt) return

  console.log('选中品种数据:', selectedArt)

  // 将选中的品种数据缓存到新变量中
  selectedArtData.value = selectedArt

  // 获取品种详细信息
  try {
    const response = await orgArtInfoApi({
      artId: selectedArtData.value.artId,
    })

    // 判断API返回值是否为空或无效
    // 检查response、response.data或直接的数据结构
    const itemData = response?.data || response

    if (itemData && Object.keys(itemData).length > 0) {
      // 有数据时，设置价格信息
      item.value = itemData
      searchFormModel.packPrice = itemData.lastBuyPrice || undefined
      if (selectedArt.cellPrice && itemData.lastBuyPrice) {
        searchFormModel.cellPrice = itemData.lastBuyPrice / selectedArt.cellPrice
      }
      console.log('获取到品种详细信息:', itemData)
    } else {
      // 当item返回数据为空时，调用org-art-price-setting.vue页面进行弹窗
      console.log('品种详细信息为空，打开机构商品设置弹窗')
      item.value = null
      await loadOrgCustMap()
      orgArtPriceSettingVisible.value = true
      // 使用nextTick确保弹窗组件已经挂载后再初始化
      nextTick(() => {
        if (orgArtPriceSettingRef.value) {
          orgArtPriceSettingRef.value.init(selectedArt,  orgCustMap.value.pricingMethod) // 1为定价方式参数
        }
      })
    }
  } catch (error) {
    // API调用失败时也打开弹窗
    console.error('获取品种详细信息失败:', error)
    console.log('API调用失败，打开机构商品设置弹窗')
    item.value = null
    orgArtPriceSettingVisible.value = true
    nextTick(() => {
      if (orgArtPriceSettingRef.value) {
        orgArtPriceSettingRef.value.init(selectedArt, orgCustMap.value.pricingMethod)
      }
    })
  }

  // 更新表单中的相关字段 - 包装相关信息
  searchFormModel.packUnit = selectedArt.packUnit || ''
  searchFormModel.cellUnit = selectedArt.cellUnit || ''
  searchFormModel.splittable = selectedArt.splittable !== undefined ? selectedArt.splittable : 0
  searchFormModel.packCells = selectedArt.packCells || undefined
  // 对选中的input标签进行赋值 - 库存相关信息
  searchFormModel.batchNo = selectedArt.batchNo || ''
  searchFormModel.expiry = selectedArt.expiry || ''
  searchFormModel.stockPacksTotal = selectedArt.stockPacksTotal || ''
  searchFormModel.stockCellsTotal = selectedArt.stockCellsTotal || undefined

  // 价格相关信息
  if (selectedArt.packPrice !== undefined) {
    searchFormModel.packPrice = selectedArt.packPrice
  }
  if (selectedArt.cellPrice !== undefined) {
    searchFormModel.cellPrice = selectedArt.cellPrice
  }

  // 其他可能的字段
  if (selectedArt.originPlace !== undefined) {
    searchFormModel.originPlace = selectedArt.originPlace
  }
  if (selectedArt.dateManufactured !== undefined) {
    searchFormModel.dateManufactured = selectedArt.dateManufactured
  }

  // 如果有artId，添加到artIds数组中
  if (selectedArt.artId && !formState.artIds.includes(selectedArt.artId)) {
    formState.artIds.push(selectedArt.artId)
  }

  // 聚焦到整包数量输入框，方便用户直接输入数量
  nextTick(() => {
    if (originPlaceRef.value) {
      originPlaceRef.value.focus()
    }
  })
}

// 清空表单数据
const clearFormData = () => {
  // 重置表单数据 - 将所有字段都设置为空值
  // 字符串类型字段设置为空字符串
  searchFormModel.batchNo = ''
  searchFormModel.expiry = ''
  searchFormModel.originPlace = ''
  searchFormModel.dateManufactured = ''
  searchFormModel.stockPacksTotal = ''
  searchFormModel.totalCells = ''
  searchFormModel.cellUnit = ''

  // 数字类型字段设置为undefined，让输入框显示为空
  searchFormModel.stockCellsTotal = undefined
  searchFormModel.totalPacks = undefined
  searchFormModel.packPrice = undefined
  searchFormModel.cellPrice = undefined
  searchFormModel.packUnit = undefined
  searchFormModel.splittable = undefined
  searchFormModel.packCells = undefined

  // 清空品种选择组件
  if (artSelectRef.value) {
    // 使用init方法清空选择组件
    if (typeof artSelectRef.value.init === 'function') {
      artSelectRef.value.init()
    }
  }
  // 清空缓存的品种数据
  selectedArtData.value = null
}
// 接收外部传入的仓库编码
const props = defineProps({
  deptCode: {
    type: String,
    default: '',
  },
})

// 使用示例中的仓库编码
const deptCode = computed(() => {
  // 优先使用外部传入的仓库编码，如果没有则使用表单中的
  const code = props.deptCode || formState.deptCode || ''
  // 将计算得到的仓库编码赋值给formState
  formState.deptCode = code
  return code
})
// 获取选中的品种数据
const getSelectedArtData = () => {
  return selectedArtData.value
}

// 设置表单数据（用于编辑时回填数据）
const setFormData = (data: any) => {
  if (!data) return

  // 设置品种数据
  if (data.artData) {
    selectedArtData.value = data.artData

    // 设置品种选择组件的显示值
    if (artSelectRef.value && typeof artSelectRef.value.setValue === 'function') {
      artSelectRef.value.setValue(data.artData)
    }
  }

  // 设置表单字段
  searchFormModel.originPlace = data.originPlace || ''
  searchFormModel.batchNo = data.batchNo || ''
  searchFormModel.dateManufactured = data.dateManufactured || ''
  searchFormModel.expiry = data.expiry || ''
  searchFormModel.packPrice = data.packPrice || undefined
  searchFormModel.totalPacks = data.totalPacks || undefined
  searchFormModel.totalCells = data.totalCells || ''

  // 设置品种相关信息
  if (data.artData) {
    searchFormModel.packUnit = data.artData.packUnit || data.packUnit || ''
    searchFormModel.cellUnit = data.artData.cellUnit || data.cellUnit || ''
    searchFormModel.splittable = data.artData.splittable !== undefined ? data.artData.splittable : (data.splittable || 0)
    searchFormModel.packCells = data.artData.packCells || data.packCells || 0
  }
}

// 校验8位数字日期格式 (YYYYMMDD)
const validateDateFormat = (value: string) => {
  // 检查是否为空

  // 检查是否为8位数字
  if (!/^\d{8}$/.test(value)) {
    message.error('日期格式必须是8位数字(YYYYMMDD)')
    return false
  }

  // 提取年月日
  const year = parseInt(value.substring(0, 4))
  const month = parseInt(value.substring(4, 6))
  const day = parseInt(value.substring(6, 8))

  // 检查年月日是否有效
  if (year < 1900 || year > 2100) {
    message.error('年份必须在1900-2100之间')
    return false
  }

  if (month < 1 || month > 12) {
    message.error('月份必须在1-12之间')
    return false
  }

  // 获取当月最大天数
  const maxDay = new Date(year, month, 0).getDate()
  if (day < 1 || day > maxDay) {
    message.error(`日期必须在1-${maxDay}之间`)
    return false
  }

  return true
}

// 验证生产日期
const validateDateManufactured = () => {
  if (validateDateFormat(searchFormModel.dateManufactured)) {
    return true
  }
  // 验证失败时聚焦回输入框
  nextTick(() => {
    dateManufacturedRef.value?.focus()
  })
  return false
}

// 验证有效期至
const validateExpiry = () => {
  if (validateDateFormat(searchFormModel.expiry)) {
    // 如果生产日期和有效期都有值，检查有效期是否大于生产日期
    if (searchFormModel.dateManufactured && searchFormModel.expiry) {
      if (parseInt(searchFormModel.expiry) <= parseInt(searchFormModel.dateManufactured)) {
        message.error('有效期至必须大于生产日期')
        nextTick(() => {
          expiryRef.value?.focus()
        })
        return false
      }
    }
    return true
  }
  // 验证失败时聚焦回输入框
  nextTick(() => {
    expiryRef.value?.focus()
  })
  return false
}

// 处理生产日期回车事件
const handleDateManufacturedEnter = () => {
  if (validateDateManufactured()) {
    focusNext('expiryRef')
  }
}

// 处理有效期至回车事件
const handleExpiryEnter = () => {
  if (validateExpiry()) {
    focusNext('packPriceRef')
  }
}
// 定义所有输入框的引用
const originPlaceRef = ref()
const batchNoRef = ref()
const dateManufacturedRef = ref()
const expiryRef = ref()
const packPriceRef = ref()
const totalPacksRef = ref()
const totalCellsRef = ref()
// 聚焦到下一个输入框
const focusNext = (refName: string) => {
  nextTick(() => {
    if (refName === 'totalCellsRef' && searchFormModel.splittable !== 1) {
      // 如果下一个是拆零数量但不可拆零，则直接提交
      onAddArt()
      return
    }
    // 根据引用名称获取对应的ref对象
    const refMap: Record<string, any> = {
      originPlaceRef: originPlaceRef,
      batchNoRef: batchNoRef,
      dateManufacturedRef: dateManufacturedRef,
      expiryRef: expiryRef,
      packPriceRef: packPriceRef,
      totalPacksRef: totalPacksRef,
      totalCellsRef: totalCellsRef,
    }

    const nextRef = refMap[refName]?.value
    if (nextRef) {
      // 处理不同类型的组件
      if (typeof nextRef.focus === 'function') {
        nextRef.focus()
      } else if (nextRef.$el && typeof nextRef.$el.focus === 'function') {
        nextRef.$el.focus()
      } else if (nextRef.input && typeof nextRef.input.focus === 'function') {
        nextRef.input.focus()
      }
    }
  })
}

// 处理机构商品设置弹窗确定回调
const handleOrgArtPriceSettingOk = async (formData: any) => {
  console.log('机构商品设置保存成功:', formData)
  // 关闭弹窗
  orgArtPriceSettingVisible.value = false

  // 保存成功后重新获取品种信息
  if (selectedArtData.value?.artId) {
    try {
      const response = await orgArtInfoApi({
        artId: selectedArtData.value.artId,
      })

      const itemData = response?.data || response
      if (itemData && Object.keys(itemData).length > 0) {
        item.value = itemData
        searchFormModel.packPrice = itemData.lastBuyPrice || 0
        if (selectedArtData.value.cellPrice && itemData.lastBuyPrice) {
          searchFormModel.cellPrice = itemData.lastBuyPrice / selectedArtData.value.cellPrice
        }
        console.log('重新获取品种详细信息成功:', itemData)
      }
    } catch (error) {
      console.error('重新获取品种详细信息失败:', error)
    }
  }

  message.success('机构商品设置保存成功')
}

// 暴露方法给外部使用
defineExpose({
  getSelectedArtData,
  clearFormData,
  setFormData,
})
</script>
<template>
  <a-form layout="inline" :model="searchFormModel">
    <a-form-item label="品种查询" name="keyword">
      <div class="art-select-box">
        <WmArticlePageByOrgSelect
          ref="artSelectRef"
          :deptCode="deptCode"
          @selected="handleArtSelect"
          :hasStock="formState.hasStock"
          style="width: 300px"
          @keydown.enter="focusNext('originPlaceRef')"
        />
      </div>
    </a-form-item>
    <a-form-item label="原产地" name="originPlace">
      <a-input ref="originPlaceRef" v-model:value="searchFormModel.originPlace" style="width: 220px" @keydown.enter="focusNext('batchNoRef')" />
    </a-form-item>
    <a-form-item label="生产批号" name="batchNo">
      <a-input ref="batchNoRef" v-model:value="searchFormModel.batchNo" style="width: 200px" @keydown.enter="focusNext('dateManufacturedRef')" />
    </a-form-item>
    <a-form-item label="生产日期" name="dateManufactured" required>
      <a-input ref="dateManufacturedRef" v-model:value="searchFormModel.dateManufactured" style="width: 120px" placeholder="YYYYMMDD" @keydown.enter="handleDateManufacturedEnter" />
    </a-form-item>
    <a-form-item label="有效期至" name="expiry" required>
      <a-input ref="expiryRef" v-model:value="searchFormModel.expiry" style="width: 120px" placeholder="YYYYMMDD" @keydown.enter="handleExpiryEnter" />
    </a-form-item>
    <a-form-item label="整包单价" name="packPrice">
      <a-input-number ref="packPriceRef" v-model:value="searchFormModel.packPrice" :min="0" @keydown.enter="focusNext('totalPacksRef')" />
    </a-form-item>
    <a-form-item label="整包数量" name="totalPacks">
      <a-input-number
        ref="totalPacksRef"
        v-model:value="searchFormModel.totalPacks"
        :min="0"
        :precision="0"
        :addon-after="searchFormModel.packUnit"
        style="width: 150px"
        @keydown.enter="searchFormModel.packCells && searchFormModel.packCells > 1 ? focusNext('totalCellsRef') : onAddArt()"
      />
    </a-form-item>
    <a-form-item label="拆零数量" name="totalCells" v-if="searchFormModel.packCells && searchFormModel.packCells > 1">
      <a-input-number ref="totalCellsRef" v-model:value="searchFormModel.totalCells" style="width: 150px" :min="0" :addon-after="searchFormModel.cellUnit" @keydown.enter="onAddArt()" />
    </a-form-item>
    <a-form-item>
      <a-button @click="onAddArt">添加</a-button>
    </a-form-item>
  </a-form>

  <!-- 机构商品设置弹窗 -->
  <OrgArtPriceSetting ref="orgArtPriceSettingRef" v-model:visible="orgArtPriceSettingVisible" @ok="handleOrgArtPriceSettingOk" width="900px" />
</template>
