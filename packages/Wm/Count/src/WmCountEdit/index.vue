<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { Button, Modal, message, Spin, Input, Upload, Tooltip, Tag, Row, Col, Tabs, Checkbox } from 'ant-design-vue'
import { STable } from '@surely-vue/table'
import { EditOutlined, UploadOutlined, DownloadOutlined, ReloadOutlined, CheckOutlined, InfoCircleOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { countInfoApi, countDetailListApi, updateCountDetailApi, exportInitStockApi, importCountDetailApi, recheckApi, finishApi, cancelCountApi } from '@mh-wm/util'
import type { PropType } from 'vue'
import type { UploadChangeParam } from 'ant-design-vue'

// 定义组件属性
const props = defineProps({
  // 按钮文本
  buttonText: {
    type: String,
    default: '录入盘点',
  },
  // 按钮类型
  buttonType: {
    type: String as PropType<'primary' | 'ghost' | 'dashed' | 'link' | 'text' | 'default'>,
    default: 'default',
  },
  // 按钮大小
  buttonSize: {
    type: String as PropType<'large' | 'middle' | 'small'>,
    default: 'middle',
  },
  // 对话框宽度
  modalWidth: {
    type: [Number, String],
    default: 1200,
  },
  // 是否显示复盘按钮
  showRecheckButton: {
    type: Boolean,
    default: true,
  },
  // 是否显示复核按钮
  showFinishButton: {
    type: Boolean,
    default: true,
  },
  // 是否显示触发按钮
  showButton: {
    type: Boolean,
    default: false,
  },
})

// 定义事件
const emit = defineEmits(['success', 'cancel', 'recheck', 'finish'])

// 对话框可见性
const visible = ref(false)
// 加载状态
const loading = ref(false)
// 保存加载状态
const saveLoading = ref(false)
// 盘点单ID
const countId = ref<number | null>(null)
// 盘点单信息
const countInfo = ref<any>(null)
// 盘点明细
const countDetails = ref<any[]>([])
// 总记录数
const total = ref(0)
// 搜索关键字
const searchKeyword = ref('')
// 是否显示盘盈条目
const showPositiveDiff = ref(false)
// 是否显示盘亏条目
const showNegativeDiff = ref(false)
// 表格引用
const tableRef = ref()
// 导入上传状态
const uploadLoading = ref(false)
// 复盘加载状态
const recheckLoading = ref(false)
// 复核加载状态
const finishLoading = ref(false)
// 作废加载状态
const cancelLoading = ref(false)

// Tabs相关状态
// 页码列表
const pageList = ref<number[]>([1])
// 当前激活的标签页
const activeTabKey = ref('1')

// 表格列定义
const columns: any[] = [
  {
    title: '行号',
    dataIndex: 'lineNo',
    width: 60,
    align: 'right',
  },
  {
    title: '条目ID',
    dataIndex: 'artId',
    width: 80,
    resizable: true,
  },
  {
    title: '品名|规格|厂家',
    dataIndex: 'artName',
    resizable: true,
    defaultSortOrder: 'ascend',
    sorter: (a: any, b: any) => {
      // 先按品名排序
      const nameCompare = a.artName.localeCompare(b.artName)
      if (nameCompare !== 0) return nameCompare

      // 如果品名相同，按规格排序
      if (a.artSpec && b.artSpec) {
        const specCompare = a.artSpec.localeCompare(b.artSpec)
        if (specCompare !== 0) return specCompare
      }

      // 如果规格也相同，按厂家排序
      if (a.producer && b.producer) {
        return a.producer.localeCompare(b.producer)
      }

      return 0
    },
    customRender: ({ record }: any) => {
      if (!record) return ''
      const artSpec = record.artSpec ? `|${record.artSpec}` : ''
      const producer = record.producer ? `|${record.producer}` : ''
      return `${record.artName}${artSpec}${producer}`
    },
  },
  {
    title: '生产批号',
    dataIndex: 'batchNo',
    width: 120,
    resizable: true,
  },
  {
    title: '盈亏数',
    dataIndex: 'diffCells',
    width: 90,
    resizable: true,
    align: 'right',
    sorter: (a: any, b: any) => {
      // 先比较整包差异
      const packDiff = (a.diffPacks || 0) - (b.diffPacks || 0)
      if (packDiff !== 0) return packDiff

      // 如果整包差异相同，比较拆零差异
      return (a.diffCells || 0) - (b.diffCells || 0)
    },
    customRender: ({ record }: any) => {
      // 参考老代码的显示方式
      const diffTotalPacks = record.diffPacks || 0
      const diffTotalCells = record.diffCells || 0
      const packUnit = record.packUnit || ''
      const cellUnit = record.cellUnit || ''

      // 创建一个包含多个元素的数组
      const elements = []

      // 如果整包差异不为0，显示整包差异
      if (diffTotalPacks !== 0) {
        elements.push(
          h(
            'span',
            {
              class: diffTotalPacks > 0 ? 'diff-positive' : 'diff-negative',
            },
            `${diffTotalPacks}${packUnit}`
          )
        )
      }

      // 如果整包差异不为0且拆零差异不为0，添加一个空格
      if (diffTotalPacks !== 0 && diffTotalCells !== 0) {
        elements.push(h('span', {}, ' '))
      }

      // 如果拆零差异不为0，显示拆零差异
      if (diffTotalCells !== 0) {
        elements.push(
          h(
            'span',
            {
              class: diffTotalCells > 0 ? 'diff-positive' : 'diff-negative',
            },
            `${diffTotalCells}${cellUnit}`
          )
        )
      }

      // 如果整包差异和拆零差异都为0，显示0
      if (diffTotalPacks === 0 && diffTotalCells === 0) {
        elements.push(h('span', {}, '0'))
      }

      // 返回包含所有元素的div
      return h('div', {}, elements)
    },
  },
  {
    title: '账存整包数',
    dataIndex: 'totalPacks',
    width: 100,
    resizable: true,
    align: 'right',
  },
  {
    title: '账存拆零数',
    dataIndex: 'totalCells',
    width: 100,
    resizable: true,
    align: 'right',
  },
  {
    title: '实盘整包数',
    dataIndex: 'actualPacks',
    key: 'actualPacks',
    width: 100,
    resizable: true,
    align: 'right',
    editable: () => {
      // 在盘点结束状态下不可编辑
      return countInfo.value?.countStatus !== 3
    },
    editableTrigger: 'click',
    valueParser: ({ newValue, oldValue, record }: any) => {
      // 确保输入的是数字
      if (isNaN(+newValue) || +newValue < 0) {
        return oldValue
      }

      // 直接使用传入的record更新数据
      if (record) {
        // 更新记录中的值
        record.actualPacks = +newValue

        // 计算差异
        calculateDiff(record)

        // 创建一个新的数组，触发视图更新
        countDetails.value = [...countDetails.value]

        // 保存该行数据
        saveRowCountResult(record)
      }

      return +newValue
    },
  },
  {
    title: '实盘拆零数',
    dataIndex: 'actualCells',
    key: 'actualCells',
    width: 100,
    resizable: true,
    align: 'right',
    editable: () => {
      // 在盘点结束状态下不可编辑
      return countInfo.value?.countStatus !== 3
    },
    editableTrigger: 'click',
    valueParser: ({ newValue, oldValue, record }: any) => {
      // 确保输入的是数字
      if (isNaN(+newValue) || +newValue < 0) {
        return oldValue
      }

      // 直接使用传入的record更新数据
      if (record) {
        // 更新记录中的值
        record.actualCells = +newValue

        // 计算差异
        calculateDiff(record)

        // 创建一个新的数组，触发视图更新
        countDetails.value = [...countDetails.value]

        // 保存该行数据
        saveRowCountResult(record)
      }

      return +newValue
    },
  },
]

// 计算差异值
const calculateDiffValue = (record: any): { diffPacks: number; diffCells: number } => {
  if (!record) return { diffPacks: 0, diffCells: 0 }
  const actualPacks = Number(record.actualPacks) || 0
  const totalPacks = Number(record.totalPacks) || 0
  const actualCells = Number(record.actualCells) || 0
  const totalCells = Number(record.totalCells) || 0

  // 计算整包差异
  const diffPacks = actualPacks - totalPacks

  // 计算拆零差异
  const diffCells = actualCells - totalCells

  return {
    diffPacks,
    diffCells,
  }
}

// 计算差异并更新记录
const calculateDiff = (record: any) => {
  if (!record) return
  const diff = calculateDiffValue(record)
  record.diffPacks = diff.diffPacks
  record.diffCells = diff.diffCells
}

// 初始化页面数据
const initPageData = async () => {
  // 清空现有数据
  countDetails.value = []
}

// 页码列表函数已移除，不再需要分页

// 过滤后的盘点明细
const filteredDetails = computed(() => {
  let filtered = [...countDetails.value]

  // 根据盘盈和盘亏checkbox的状态过滤数据
  if (showPositiveDiff.value || showNegativeDiff.value) {
    filtered = filtered.filter(detail => {
      // 检查是否有盘盈（正数差异）
      const hasPositiveDiff = detail.diffPacks > 0 || detail.diffCells > 0

      // 检查是否有盘亏（负数差异）
      const hasNegativeDiff = detail.diffPacks < 0 || detail.diffCells < 0

      // 根据checkbox状态返回过滤结果
      if (showPositiveDiff.value && showNegativeDiff.value) {
        // 同时显示盘盈和盘亏
        return hasPositiveDiff || hasNegativeDiff
      } else if (showPositiveDiff.value) {
        // 只显示盘盈
        return hasPositiveDiff
      } else if (showNegativeDiff.value) {
        // 只显示盘亏
        return hasNegativeDiff
      }

      // 默认情况，不应该到达这里
      return false
    })
  }

  // 如果没有搜索关键字，返回当前过滤结果
  if (!searchKeyword.value.trim()) {
    return filtered
  }

  // 有搜索关键字，继续过滤
  const keyword = searchKeyword.value.toUpperCase().trim()

  filtered = filtered.filter(detail => {
    // 检查条目ID
    const artIdMatch = detail.artId && detail.artId.toString().toUpperCase().includes(keyword)
    // 检查品名
    const artNameMatch = detail.artName && detail.artName.toUpperCase().includes(keyword)
    // 检查规格
    const artSpecMatch = detail.artSpec && detail.artSpec.toUpperCase().includes(keyword)
    // 检查厂家
    const producerMatch = detail.producer && detail.producer.toUpperCase().includes(keyword)
    // 检查qsCode1
    const qsCode1Match = detail.qsCode1 && detail.qsCode1.toUpperCase().includes(keyword)
    // 检查qsCode2
    const qsCode2Match = detail.qsCode2 && detail.qsCode2.toUpperCase().includes(keyword)

    return artIdMatch || artNameMatch || artSpecMatch || producerMatch || qsCode1Match || qsCode2Match
  })

  return filtered
})

// 打开对话框
const open = async (id: number) => {
  if (!id) {
    message.error('盘点单ID不能为空')
    return
  }

  // 设置盘点单ID
  countId.value = id

  // 显示对话框
  visible.value = true

  // 初始化页面数据
  await initPageData()

  // 加载盘点单信息
  loadCountInfo()
}

// 关闭对话框
const close = () => {
  visible.value = false
  // 重置状态
  countDetails.value = []
  countInfo.value = null
  searchKeyword.value = ''
  showPositiveDiff.value = false
  showNegativeDiff.value = false
  emit('cancel')
}

// 加载盘点单信息
const loadCountInfo = async () => {
  if (!countId.value) return

  loading.value = true

  try {
    // 加载盘点单基本信息，API返回的直接是data
    const response = await countInfoApi({ countId: countId.value })
    console.log('盘点单信息API响应:', response)
    countInfo.value = response

    // 加载盘点明细
    await loadCountDetails()

    message.success('加载盘点单信息成功')
  } catch (error) {
    console.error('加载盘点单信息失败:', error)
    message.error('加载盘点单信息失败')
  } finally {
    loading.value = false
  }
}

// 加载盘点明细
const loadCountDetails = async () => {
  if (!countId.value) return

  loading.value = true

  try {
    // 设置一个较大的pageSize，以便一次性获取所有数据
    // API返回的直接是data
    const response = await countDetailListApi({
      countId: countId.value,
    })
    console.log('盘点明细API响应:', response)

    // 处理数据
    const processedDetails = (response || []).map((item: any) => {
      // 确保actualPacks和actualCells有值，如果后端返回null或undefined，则默认为0
      const actualPacks = item.actualPacks !== null && item.actualPacks !== undefined ? item.actualPacks : 0
      const actualCells = item.actualCells !== null && item.actualCells !== undefined ? item.actualCells : 0

      // 计算差异
      const diff = calculateDiffValue({
        ...item,
        actualPacks,
        actualCells,
      })

      return {
        ...item,
        actualPacks,
        actualCells,
        diffPacks: diff.diffPacks,
        diffCells: diff.diffCells,
      }
    })

    // 更新总数
    total.value = processedDetails.length

    // 更新countDetails
    countDetails.value = processedDetails
    console.log('盘点明细数据已更新:', processedDetails)
  } catch (error) {
    console.error('加载盘点明细失败:', error)
    message.error('加载盘点明细失败')
  } finally {
    loading.value = false
  }
}

// 切换页码函数已移除，不再需要分页

// 搜索
const handleSearch = (value: string) => {
  searchKeyword.value = value
}

// 搜索框已经有内置的清除功能，不需要额外的清除函数

// 导出初始库存
const exportInitStock = async () => {
  if (!countId.value) return

  try {
    loading.value = true
    const response = await exportInitStockApi({ countId: countId.value })

    // 创建Blob对象
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })

    // 创建下载链接
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `盘点单${countId.value}初始库存.xlsx`
    link.click()

    message.success('导出成功')
  } catch (error) {
    console.error('导出初始库存失败:', error)
    message.error('导出初始库存失败')
  } finally {
    loading.value = false
  }
}

// 导入盘点明细
const handleImport = async (info: UploadChangeParam) => {
  const { file } = info

  if (file.status === 'uploading') {
    uploadLoading.value = true
    return
  }

  if (file.status === 'done') {
    uploadLoading.value = false

    // 框架已经处理了code不为0的情况，这里直接处理成功情况
    message.success('导入成功')
    // 重新加载盘点明细
    loadCountDetails()
  }

  if (file.status === 'error') {
    uploadLoading.value = false
    message.error('导入失败')
  }
}

// 上传前检查
const beforeUpload = (file: any) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel'

  if (!isExcel) {
    message.error('只能上传Excel文件!')
  }

  return isExcel
}

// 自定义上传请求
const customRequest = async (options: any) => {
  const { file, onSuccess, onError } = options

  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('countId', countId.value?.toString() || '')

    // API返回的直接是data，框架已经处理了code不为0的情况
    const response = await importCountDetailApi(formData)

    // 导入成功
    onSuccess(response, file)
  } catch (error) {
    onError(error)
  }
}

// 复盘操作
const handleRecheck = async () => {
  if (!countId.value) return

  try {
    recheckLoading.value = true
    // API已经处理了错误情况，直接调用即可
    await recheckApi({ countId: countId.value })

    message.success('复盘操作成功')
    emit('recheck', countId.value)
    // 重新加载盘点单信息
    loadCountInfo()
  } catch (error) {
    console.error('复盘操作失败:', error)
    message.error('复盘操作失败')
  } finally {
    recheckLoading.value = false
  }
}

// 复核操作
const handleFinish = async () => {
  if (!countId.value) return

  try {
    finishLoading.value = true
    // API已经处理了错误情况，直接调用即可
    await finishApi({ countId: countId.value })

    message.success('复核操作成功')
    emit('finish', countId.value)
    // 重新加载盘点单信息
    loadCountInfo()
  } catch (error) {
    console.error('复核操作失败:', error)
    message.error('复核操作失败')
  } finally {
    finishLoading.value = false
  }
}

// 作废操作
const handleCancel = async () => {
  if (!countId.value) return

  // 显示确认对话框
  Modal.confirm({
    title: '确认作废',
    content: '确定要作废该盘点单吗？作废后将无法恢复。',
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      try {
        cancelLoading.value = true
        // 调用作废API，API已经处理了错误情况，直接调用即可
        await cancelCountApi({ countId: countId.value })

        message.success('作废成功')
        // 关闭窗口
        visible.value = false
      } catch (error) {
        console.error('作废操作失败:', error)
        message.error('作废操作失败')
      } finally {
        cancelLoading.value = false
      }
    },
  })
}

// 保存单行盘点结果
const saveRowCountResult = async (record: any) => {
  if (!countId.value || !record) {
    return
  }

  // 保存原始值，以便在保存失败时恢复
  const originalActualPacks = record.actualPacks
  const originalActualCells = record.actualCells

  try {
    // 准备要保存的数据，根据后端更新逻辑构造参数
    const params = {
      countId: countId.value,
      pageNo: record.pageNo || 1, // 页码，如果不存在则默认为1
      lineNo: record.lineNo,
      artId: record.artId,
      batchNo: record.batchNo || '', // 批号，如果不存在则默认为空字符串
      rackNo: record.rackNo || '', // 货位号，如果不存在则默认为空字符串
      totalPacks: record.actualPacks || 0, // 账存整包数量
      totalCells: record.actualCells || 0, // 账存拆零数量
      // actualPacks: record.actualPacks || 0, // 实盘整包数量
      // actualCells: record.actualCells || 0, // 实盘拆零数量
    }

    // API已经处理了错误情况，直接调用即可
    // API返回的直接是data
    const updatedData = await updateCountDetailApi(params)
    // 使用API返回的数据更新当前记录
    if (updatedData) {
      // 更新当前记录的数据
      Object.assign(record, updatedData)

      // 重新计算差异，确保盈亏显示正确
      calculateDiff(record)

      // 创建一个新的数组，触发视图更新
      countDetails.value = [...countDetails.value]
    }

    console.log('保存行数据成功')
  } catch (error) {
    console.error('保存行数据失败:', error)
    message.error('保存行数据失败')

    // 恢复原始值
    record.actualPacks = originalActualPacks
    record.actualCells = originalActualCells

    // 重新计算差异
    calculateDiff(record)

    // 创建一个新的数组，触发视图更新
    countDetails.value = [...countDetails.value]
  }
}

// 保存盘点结果（保留此函数以备将来使用）
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const saveCountResult = async () => {
  if (!countId.value || countDetails.value.length === 0) {
    message.warning('没有可保存的盘点明细')
    return
  }

  try {
    saveLoading.value = true

    // 准备要保存的数据，根据后端更新逻辑构造参数
    const details = countDetails.value.map(detail => ({
      countId: countId.value,
      pageNo: detail.pageNo || 1, // 页码，如果不存在则默认为1
      lineNo: detail.lineNo,
      artId: detail.artId,
      batchNo: detail.batchNo || '', // 批号，如果不存在则默认为空字符串
      rackNo: detail.rackNo || '', // 货位号，如果不存在则默认为空字符串
      totalPacks: detail.totalPacks || 0, // 账存整包数量
      totalCells: detail.totalCells || 0, // 账存拆零数量
      actualPacks: detail.actualPacks || 0, // 实盘整包数量
      actualCells: detail.actualCells || 0, // 实盘拆零数量
    }))

    // API已经处理了错误情况，直接调用即可
    // API返回的直接是data
    await updateCountDetailApi({ details })

    message.success('保存盘点结果成功')
    emit('success', countId.value)
    // 重新加载盘点明细
    loadCountDetails()
  } catch (error) {
    console.error('保存盘点结果失败:', error)
    message.error('保存盘点结果失败')
  } finally {
    saveLoading.value = false
  }
}

// 获取盘点状态文本
const getCountStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '初盘'
    case 2:
      return '复盘'
    case 3:
      return '盘结'
    default:
      return '未知'
  }
}

// 获取盘点状态颜色
const getStatusColor = (status: number) => {
  switch (status) {
    case 1:
      return 'blue' // 初盘
    case 2:
      return 'orange' // 复盘
    case 3:
      return 'green' // 盘结
    default:
      return 'default' // 未知状态
  }
}

// 动态设置行样式 - 保留此函数以兼容旧代码
const getRowClassName = (record: any) => {
  // 检查diffPacks和diffCells，如果任一个大于0，则返回绿色样式类
  if (record.diffPacks > 0 || record.diffCells > 0) {
    return 'row-green' // >0 时返回绿色样式类
  }
  // 检查diffPacks和diffCells，如果任一个小于0，则返回红色样式类
  else if (record.diffPacks < 0 || record.diffCells < 0) {
    return 'row-red' // <0 时返回红色样式类
  }
  return '' // 其他情况不添加样式
}

// 自定义行属性
const customRow = (record: any, index: number) => {
  // 根据盈亏状态设置不同的背景色
  let style = {}

  if (record.diffPacks > 0 || record.diffCells > 0) {
    // 盘盈行 - 浅绿色背景
    style = { background: 'rgba(82, 196, 26, 0.1)' }
  } else if (record.diffPacks < 0 || record.diffCells < 0) {
    // 盘亏行 - 浅红色背景
    style = { background: 'rgba(245, 34, 45, 0.1)' }
  } else if (index % 2 === 1) {
    // 奇数行 - 浅灰色背景
    style = { background: '#fafafa' }
  }

  return {
    style,
    onDblclick: (event: Event) => {
      // 在盘点结束状态下不能编辑
      if (countInfo.value?.countStatus === 3) {
        return
      }

      // 获取点击的单元格
      const target = event.target as HTMLElement
      const cell = target.closest('td')
      if (cell) {
        // 获取列索引
        const cellIndex = Array.from((cell as HTMLElement).parentNode?.children || []).indexOf(cell)
        // 根据列索引确定要编辑的列
        if (cellIndex === 6) {
          // 实盘整包数量列
          triggerEditor(record, 'actualPacks')
        } else if (cellIndex === 7) {
          // 实盘拆零数量列
          triggerEditor(record, 'actualCells')
        }
      }
    },
  }
}

// 自定义单元格属性
const customCell = ({ record, column }: any) => {
  // 根据列和行的特性设置不同的样式
  if (column.dataIndex === 'diffPacks' || column.dataIndex === 'diffCells') {
    // 差异列根据值设置不同的文本颜色
    const value = record[column.dataIndex]
    if (value > 0) {
      return { style: { color: '#52c41a', fontWeight: 'bold' } }
    } else if (value < 0) {
      return { style: { color: '#f5222d', fontWeight: 'bold' } }
    }
  } else if (column.dataIndex === 'actualPacks' || column.dataIndex === 'actualCells') {
    // 实盘数量列使用浅黄色背景，表示可编辑
    return { style: { background: 'rgba(250, 219, 20, 0.1)' } }
  } else if (column.dataIndex === 'artName') {
    // 品名列使用不同的背景色
    return { style: { background: 'rgba(24, 144, 255, 0.05)' } }
  }

  return {}
}

// 自定义表头单元格属性
const customHeaderCell = (column: any) => {
  // 为特定列设置不同的表头样式
  if (column.dataIndex === 'actualPacks' || column.dataIndex === 'actualCells') {
    // 实盘数量列的表头使用浅黄色背景
    return { style: { background: 'rgba(250, 219, 20, 0.2)' } }
  } else if (column.dataIndex === 'diffPacks' || column.dataIndex === 'diffCells') {
    // 差异列的表头使用浅蓝色背景
    return { style: { background: 'rgba(24, 144, 255, 0.1)' } }
  }

  return {}
}

// 移除了键盘事件处理函数

// 触发编辑器
const triggerEditor = (record: any, columnKey: string) => {
  if (!record || !columnKey) return

  // 使用openEditor方法打开编辑器
  tableRef.value?.openEditor([
    {
      columnKey: columnKey,
      rowKey: record.lineNo.toString(),
    },
  ])
}

// 处理表格变化事件
const handleTableChange = (pagination: any, filters: any, sorter: any, extra: any) => {
  console.log('表格变化:', pagination, filters, sorter, extra)
  // 这里可以根据需要处理排序逻辑
}

// 组件挂载后执行
onMounted(() => {
  // 移除了键盘事件监听
})

// 组件卸载前执行
onBeforeUnmount(() => {
  // 移除了键盘事件监听
})

// 暴露方法
defineExpose({
  open,
  close,
  triggerEditor,
})
</script>

<template>
  <div class="wm-count-edit">
    <!-- 触发按钮，只在showButton为true时显示 -->
    <Button v-if="showButton" :type="buttonType" :size="buttonSize" @click="() => open(countId || 0)">
      <template #icon><EditOutlined /></template>
      {{ buttonText }}
    </Button>

    <!-- 录入盘点结果对话框 -->
    <Modal v-model:open="visible" :title="'录入盘点结果'" :width="modalWidth" :maskClosable="false" :destroyOnClose="true" @cancel="close" :closable="true" :footer="null">
      <Spin :spinning="loading">
        <div v-if="countInfo" style="">
          <!-- 盘点单基本信息 -->
          <div style="background-color: #f5f5f5; border-radius: 4px; margin-bottom: 16px; padding: 10px 0px 10px 0px">
            <Row align="middle" :gutter="0">
              <Col :span="3">
                <div style="display: flex; align-items: center">
                  <span style="font-weight: 500; color: #666; margin-right: 4px; min-width: 70px; text-align: right">盘点单号:</span>
                  <span>{{ countInfo.countId }}</span>
                  <Tooltip placement="top">
                    <template #title>{{ countInfo.notes }}</template>
                    <Button type="link" size="small" style="padding: 0">
                      <InfoCircleOutlined style="color: #1890ff" />
                    </Button>
                  </Tooltip>
                </div>
              </Col>
              <Col :span="3">
                <div style="display: flex; align-items: center">
                  <span style="font-weight: 500; color: #666; margin-right: 4px; min-width: 70px; text-align: right">盘点仓库:</span>
                  <span>{{ countInfo.deptName }}</span>
                </div>
              </Col>
              <Col :span="3">
                <div style="display: flex; align-items: center">
                  <span style="font-weight: 500; color: #666; margin-right: 4px; min-width: 70px; text-align: right">盘点人:</span>
                  <span>{{ countInfo.counterName }}</span>
                </div>
              </Col>
              <Col :span="3">
                <div style="display: flex; align-items: center">
                  <span style="font-weight: 500; color: #666; margin-right: 4px; min-width: 70px; text-align: right">盘点状态:</span>
                  <Tag :color="getStatusColor(countInfo.countStatus)">
                    {{ getCountStatusText(countInfo.countStatus) }}
                  </Tag>
                </div>
              </Col>
              <Col :span="5">
                <div style="display: flex; align-items: center">
                  <span style="font-weight: 500; color: #666; margin-right: 4px; min-width: 70px; text-align: right">创建时间:</span>
                  <span>{{ countInfo.timeStarted }}</span>
                </div>
              </Col>
              <Col :span="1" v-if="countInfo.notes"> </Col>
              <Col :span="6" style="display: flex; justify-content: flex-end; gap: 2px">
                <Tooltip title="导出初始库存">
                  <Button type="primary" size="small" ghost @click="exportInitStock">
                    <template #icon><DownloadOutlined /></template>
                    导出
                  </Button>
                </Tooltip>
                <Upload v-if="countInfo.countStatus !== 3" :customRequest="customRequest" :beforeUpload="beforeUpload" :showUploadList="false" @change="handleImport">
                  <Button :loading="uploadLoading" type="primary" size="small" ghost>
                    <template #icon><UploadOutlined /></template>
                    导入
                  </Button>
                </Upload>

                <Button v-if="showRecheckButton && countInfo.countStatus === 1" :loading="recheckLoading" type="primary" size="small" ghost @click="handleRecheck">
                  <template #icon><ReloadOutlined /></template>
                  复盘
                </Button>

                <Button v-if="countInfo.countStatus === 1" :loading="cancelLoading" type="primary" size="small" danger @click="handleCancel">
                  <template #icon><CloseOutlined /></template>
                  作废
                </Button>

                <Button v-if="showFinishButton && countInfo.countStatus === 2" :loading="finishLoading" type="primary" size="small" ghost @click="handleFinish">
                  <template #icon><CheckOutlined /></template>
                  复核
                </Button>
              </Col>
            </Row>
          </div>

          <!-- 盘点明细表格 -->
          <div class="count-details">
            <div class="tabs-header">
              <Tabs v-model:activeKey="activeTabKey" type="card">
                <template #rightExtra>
                  <div class="filter-container">
                    <span class="total-count">共 {{ filteredDetails.length }} 条</span>
                    <Checkbox v-model:checked="showPositiveDiff" style="margin-right: 8px; margin-top: 5px">
                      <span style="color: #52c41a">盘盈</span>
                    </Checkbox>
                    <Checkbox v-model:checked="showNegativeDiff" style="margin-right: 8px; margin-top: 5px">
                      <span style="color: #f5222d">盘亏</span>
                    </Checkbox>
                    <Input.Search v-model:value="searchKeyword" placeholder="搜索条目ID/品名/规格/厂家/助记码" style="width: 280px" allowClear @search="handleSearch" />
                  </div>
                </template>
                <Tabs.TabPane v-for="page in pageList" :key="page.toString()" :tab="`页码 ${page}`" :closable="false">
                  <div class="table-container">
                    <STable
                      ref="tableRef"
                      :columns="columns"
                      :dataSource="filteredDetails"
                      :pagination="false"
                      :rowKey="(record: any) => record.lineNo"
                      size="middle"
                      :scroll="{ y: 380 }"
                      :loading="loading"
                      :locale="{ emptyText: '暂无数据' }"
                      :rowClassName="getRowClassName"
                      @change="handleTableChange"
                      :customRow="customRow"
                      :customCell="customCell"
                      :customHeaderCell="customHeaderCell"
                      bordered
                    />
                  </div>
                </Tabs.TabPane>
              </Tabs>
            </div>
          </div>
        </div>

        <div v-else class="empty-container">
          <Spin tip="加载中..." :spinning="loading">
            <div class="empty-content">
              <p>{{ loading ? '正在加载盘点单信息...' : '暂无盘点单信息' }}</p>
              <p v-if="countId">盘点单ID: {{ countId }}</p>
            </div>
          </Spin>
        </div>
      </Spin>

      <!-- 移除了底部的关闭和保存按钮 -->
    </Modal>
  </div>
</template>

<style>
/* 组件样式已移至全局样式文件 */
/* 以下样式仅用于开发时参考，不会影响最终构建 */

/* 盘点单信息样式已移除，使用Row和Col组件代替 */

/* Tag样式调整 */
:deep(.ant-tag) {
  margin-right: 0;
  font-size: 12px;
}

/* 工具栏样式已移除，按钮直接放在第一行信息中 */

/* 盘点明细表格样式 */
.count-details {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  height: 500px;
  display: flex;
  flex-direction: column;
}

.tabs-header {
  padding: 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.filter-container {
  margin-right: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.total-count {
  margin-right: 12px;
  color: #666;
  font-size: 14px;
}

/* 确保过滤框在标签栏一行 */
:deep(.ant-tabs-nav-wrap) {
  flex: 1;
}

:deep(.ant-tabs-extra-content) {
  display: flex;
  align-items: center;
}

/* 表格容器固定高度 */
.table-container {
  height: 450px; /* 增加容器高度，以适应更高的行高 */
  position: relative;
}

/* 确保表格始终占满容器高度 */
:deep(.table-container .ant-table-wrapper),
:deep(.table-container .surely-table-wrapper) {
  height: 100%;
}

:deep(.table-container .ant-table-wrapper .ant-table),
:deep(.table-container .surely-table-wrapper .surely-table) {
  height: 100%;
}

/* 空数据状态下保持高度 */
:deep(.table-container .ant-table-placeholder),
:deep(.table-container .surely-table-placeholder) {
  height: 400px; /* 增加空数据状态下的高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保表格体占满剩余空间 */
:deep(.table-container .ant-table-body),
:deep(.table-container .surely-table-body) {
  height: calc(100% - 50px) !important; /* 减去表头高度 */
}

/* 分页容器样式已移除 */

/* 实盘数量输入框容器 */
.actual-input-container {
  display: flex;
  align-items: center;
  gap: 4px;
}

.unit-text {
  color: #666;
}

/* 差异数量样式 */
.diff-positive {
  color: #52c41a;
}

.diff-negative {
  color: #f5222d;
}

/* 空状态容器 */
.empty-container {
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fafafa;
  border-radius: 4px;
}

.empty-content {
  text-align: center;
  color: #999;
}

/* 表格内容溢出处理 */
:deep(.ant-table-cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 表格行悬停效果 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #e6f7ff !important;
}

/* 表格固定高度 */
:deep(.ant-table-placeholder) {
  height: 350px;
}

/* 输入框样式 */
:deep(.ant-input-number) {
  width: 80px;
}

/* 确保表格内容垂直居中 */
:deep(.ant-table-cell) {
  vertical-align: middle;
  padding: 12px 8px; /* 增加单元格内边距，使行高更高 */
}

/* 增加表格行高 */
:deep(.ant-table-tbody > tr > td),
:deep(.surely-table-tbody > tr > td) {
  height: 50px; /* 设置固定行高 */
}

/* 行样式 - 绿色（盈余） */
:deep(.row-green),
:deep(tr.row-green td) {
  background-color: rgba(82, 196, 26, 0.1) !important;
}

/* 行样式 - 红色（亏损） */
:deep(.row-red),
:deep(tr.row-red td) {
  background-color: rgba(245, 34, 45, 0.1) !important;
}

/* 确保行样式在悬停时也保持 */
:deep(.row-green:hover > td),
:deep(tr.row-green:hover td) {
  background-color: rgba(82, 196, 26, 0.15) !important;
}

:deep(.row-red:hover > td),
:deep(tr.row-red:hover td) {
  background-color: rgba(245, 34, 45, 0.15) !important;
}

/* 移除输入框的上下箭头 - Chrome, Safari, Edge, Opera */
:deep(input[type='number']::-webkit-outer-spin-button),
:deep(input[type='number']::-webkit-inner-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}

/* 移除输入框的上下箭头 - Firefox */
:deep(input[type='number']) {
  -moz-appearance: textfield;
  appearance: textfield; /* 标准属性，提高兼容性 */
}
</style>
