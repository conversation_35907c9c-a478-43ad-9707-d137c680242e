/* WmCountCreate 组件样式 */
.wm-count-create .count-modal .ant-modal-body {
  padding: 16px 12px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.wm-count-create .count-modal .ant-modal-footer {
  margin-top: 16px;
  border-top: 1px solid #f0f0f0;
  padding-top: 10px;
}

.wm-count-create .count-form {
  padding: 0;
  max-width: 100%;
  margin: 0 auto;
  padding-bottom: 16px;
}

.wm-count-create .form-row {
  margin-bottom: 16px;
}

.wm-count-create .form-item {
  display: flex;
  align-items: center;
}

.wm-count-create .item-label {
  margin-right: 8px;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 500;
  text-align: right;
  min-width: 70px;
}

.wm-count-create .art-select-box {
  margin-top: -5px;
}

.wm-count-create .clear-button {
  margin-left: auto;
}

.wm-count-create .art-name {
  font-weight: bold;
}

.wm-count-create .art-spec,
.wm-count-create .art-producer {
  font-size: 12px;
  color: #666;
}

.wm-count-create .selected-arts {
  margin-top: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  max-height: calc(100vh - 350px);
  overflow: hidden;
}

.wm-count-create .tabs-header {
  padding: 8px;
}

.wm-count-create .filter-container {
  margin-right: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.wm-count-create .total-count {
  margin-right: 12px;
  color: #666;
  font-size: 14px;
}

.wm-count-create .ant-tabs-nav-wrap {
  flex: 1;
}

.wm-count-create .ant-tabs-extra-content {
  display: flex;
  align-items: center;
}

.wm-count-create .confirm-form {
  padding: 8px 0;
}

.wm-count-create .remove-link {
  color: #ff4d4f;
  text-decoration: none;
}

.wm-count-create .remove-link:hover {
  color: #ff7875;
  text-decoration: underline;
}

.wm-count-create .art-info-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.wm-count-create .surely-table-cell-content,
.wm-count-create .ant-table-cell-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wm-count-create .table-container {
  height: 350px;
  position: relative;
  margin-bottom: 16px;
}

.wm-count-create .table-container .ant-table-wrapper,
.wm-count-create .table-container .surely-table-wrapper {
  height: 100%;
}

.wm-count-create .table-container .ant-table-wrapper .ant-table,
.wm-count-create .table-container .surely-table-wrapper .surely-table {
  height: 100%;
}

.wm-count-create .table-container .ant-table-placeholder,
.wm-count-create .table-container .surely-table-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wm-count-create .table-container .ant-table-body,
.wm-count-create .table-container .surely-table-body {
  height: calc(100% - 50px) !important;
}

/* WmCountEdit 组件样式 */
.wm-count-edit .count-container {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  height: 500px;
  display: flex;
  flex-direction: column;
}

.wm-count-edit .tabs-header {
  padding: 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.wm-count-edit .filter-container {
  margin-right: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.wm-count-edit .total-count {
  margin-right: 12px;
  color: #666;
  font-size: 14px;
}

.wm-count-edit .ant-tabs-nav-wrap {
  flex: 1;
}

.wm-count-edit .ant-tabs-extra-content {
  display: flex;
  align-items: center;
}

.wm-count-edit .table-container {
  height: 450px;
  position: relative;
}

.wm-count-edit .table-container .ant-table-wrapper,
.wm-count-edit .table-container .surely-table-wrapper {
  height: 100%;
}

.wm-count-edit .table-container .ant-table-wrapper .ant-table,
.wm-count-edit .table-container .surely-table-wrapper .surely-table {
  height: 100%;
}

.wm-count-edit .table-container .ant-table-placeholder,
.wm-count-edit .table-container .surely-table-placeholder {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wm-count-edit .table-container .ant-table-body,
.wm-count-edit .table-container .surely-table-body {
  height: calc(100% - 50px) !important;
}

.wm-count-edit .actual-input-container {
  display: flex;
  align-items: center;
  gap: 4px;
}

.wm-count-edit .unit-text {
  color: #666;
}

.wm-count-edit .diff-positive {
  color: #52c41a;
}

.wm-count-edit .diff-negative {
  color: #f5222d;
}

.wm-count-edit .empty-container {
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fafafa;
  border-radius: 4px;
}

.wm-count-edit .empty-content {
  text-align: center;
  color: #999;
}

.wm-count-edit .ant-table-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
  padding: 12px 8px;
}

.wm-count-edit .ant-table-tbody > tr:hover > td {
  background-color: #e6f7ff !important;
}

.wm-count-edit .ant-table-placeholder {
  height: 350px;
}

.wm-count-edit .ant-input-number {
  width: 80px;
}

.wm-count-edit .ant-table-tbody > tr > td,
.wm-count-edit .surely-table-tbody > tr > td {
  height: 50px;
}

.wm-count-edit .row-green,
.wm-count-edit tr.row-green td {
  background-color: rgba(82, 196, 26, 0.1) !important;
}

.wm-count-edit .row-red,
.wm-count-edit tr.row-red td {
  background-color: rgba(245, 34, 45, 0.1) !important;
}

.wm-count-edit .row-green:hover > td,
.wm-count-edit tr.row-green:hover td {
  background-color: rgba(82, 196, 26, 0.15) !important;
}

.wm-count-edit .row-red:hover > td,
.wm-count-edit tr.row-red:hover td {
  background-color: rgba(245, 34, 45, 0.15) !important;
}

.wm-count-edit input[type='number']::-webkit-outer-spin-button,
.wm-count-edit input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.wm-count-edit input[type='number'] {
  -moz-appearance: textfield;
  appearance: textfield;
}
