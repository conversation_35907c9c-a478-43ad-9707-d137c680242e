# @mh-wm/count

仓库盘点组件，用于创建盘点单和录入盘点结果。

> **重要提示**：使用本组件时，必须引入样式文件 `import '@mh-wm/count/index.css'`，否则组件样式将无法正常显示。

## 安装

```bash
npm install @mh-wm/count
```

在package.json中添加依赖：

```json
{
  "dependencies": {
    "@mh-wm/count": "^1.0.0"
  }
}
```

## 样式引入

在使用组件时，需要手动引入样式文件，否则组件样式将无法正常显示：

```javascript
// 在项目入口文件（如main.js或main.ts）中引入样式
import '@mh-wm/count/index.css'
```

或者在组件中单独引入：

```vue
<script setup>
import { WmCountCreate, WmCountEdit } from '@mh-wm/count'
import '@mh-wm/count/index.css'
</script>
```

## 组件

### WmCountCreate

创建盘点单组件，点击按钮打开弹窗，可以选择盘点仓库，添加盘点品种。盘点类型固定为品种盘点，盘点人默认为当前用户，无需手动选择。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| buttonText | string | '创建盘点' | 按钮文本 |
| buttonType | string | 'default' | 按钮类型，可选值：'primary', 'default', 'dashed', 'text', 'link' |
| buttonSize | string | 'middle' | 按钮大小，可选值：'large', 'middle', 'small' |
| defaultDeptCode | string | '' | 默认选中的仓库编码 |
| onlyShowWithStock | boolean | false | 是否只显示有库存的品种 |
| modalWidth | number \| string | 1000 | 对话框宽度 |

#### 事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| success | 创建盘点单成功时触发 | countId: number (盘点单ID) |
| cancel | 取消创建盘点单时触发 | - |

#### 方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| open | 打开创建盘点单对话框 | deptCode?: string (可选的仓库编码) |
| close | 关闭创建盘点单对话框 | - |

#### 示例

```vue
<template>
  <WmCountCreate
    buttonText="新建盘点"
    buttonType="primary"
    :defaultDeptCode="deptCode"
    :onlyShowWithStock="true"
    @success="handleSuccess"
    @cancel="handleCancel"
  />
</template>

<script setup>
import { WmCountCreate } from '@mh-wm/count'
import '@mh-wm/count/index.css'  // 引入样式文件
import { ref } from 'vue'

const deptCode = ref('1001')

const handleSuccess = (countId) => {
  console.log('创建盘点单成功，盘点单ID:', countId)
}

const handleCancel = () => {
  console.log('取消创建盘点单')
}
</script>
```

### WmCountEdit

录入盘点结果组件，点击按钮打开弹窗，可以录入盘点结果。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| buttonText | string | '录入盘点' | 按钮文本 |
| buttonType | string | 'default' | 按钮类型，可选值：'primary', 'default', 'dashed', 'text', 'link' |
| buttonSize | string | 'middle' | 按钮大小，可选值：'large', 'middle', 'small' |
| modalWidth | number \| string | 1200 | 对话框宽度 |

#### 事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| success | 保存盘点结果成功时触发 | countId: number (盘点单ID) |
| cancel | 取消录入盘点结果时触发 | - |

#### 方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| open | 打开录入盘点结果对话框 | countId: number (盘点单ID) |
| close | 关闭录入盘点结果对话框 | - |

#### 示例

```vue
<template>
  <WmCountEdit
    buttonText="录入盘点结果"
    buttonType="primary"
    @success="handleSuccess"
    @cancel="handleCancel"
    ref="countEditRef"
  />

  <Button @click="openEdit">打开录入</Button>
</template>

<script setup>
import { WmCountEdit } from '@mh-wm/count'
import '@mh-wm/count/index.css'  // 引入样式文件
import { Button } from 'ant-design-vue'
import { ref } from 'vue'

const countEditRef = ref(null)

const openEdit = () => {
  countEditRef.value.open(123) // 传入盘点单ID
}

const handleSuccess = (countId) => {
  console.log('保存盘点结果成功，盘点单ID:', countId)
}

const handleCancel = () => {
  console.log('取消录入盘点结果')
}
</script>
```

## 特性

1. 支持多种方式添加盘点品种：搜索添加、按类型添加
2. 支持分页管理盘点品种，使用tab页切换不同页码
3. 支持只显示有库存的品种
4. 支持批量导入导出盘点结果
5. 支持盘点状态流转：初盘 -> 复盘 -> 盘结
