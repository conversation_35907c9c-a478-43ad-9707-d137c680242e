<template>
  <div class="maker-dept-art-example">
    <h2>MakerDeptArt 组件示例</h2>
    <p>这个组件用于品种选择和数量输入，支持部门信息传递和返回完整的表单数据。组件已简化，不依赖外部API。</p>
    
    <div class="example-section">
      <h3>基本用法</h3>
      <div class="form-section">
        <label>部门编码：</label>
        <input 
          v-model="deptCode" 
          placeholder="请输入部门编码" 
          style="margin-right: 10px; padding: 4px 8px; border: 1px solid #d9d9d9; border-radius: 4px;"
        />
        <button 
          @click="updateDeptCode" 
          style="padding: 4px 12px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;"
        >
          更新部门编码
        </button>
      </div>
      
      <div class="component-demo">
        <MakerDeptArt 
          :deptCode="deptCode"
          @addArt="handleAddArt"
          ref="makerDeptArtRef"
        />
      </div>
      
      <div class="actions">
        <button 
          @click="clearForm" 
          style="padding: 6px 16px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 8px;"
        >
          清空表单
        </button>
        <button 
          @click="clearResults" 
          style="padding: 6px 16px; background: #ff4d4f; color: white; border: none; border-radius: 4px; cursor: pointer;"
        >
          清空结果
        </button>
      </div>
    </div>
    
    <div class="result-section">
      <h3>添加的品种列表</h3>
      <div v-if="addedArts.length === 0" class="empty-state">
        暂无添加的品种
      </div>
      <div v-else class="art-list">
        <div 
          v-for="(art, index) in addedArts" 
          :key="index" 
          class="art-item"
        >
          <div class="art-info">
            <div class="art-name">
              <strong>{{ art.artName || '未选择品种' }}</strong>
              <span v-if="art.artSpec" class="art-spec">{{ art.artSpec }}</span>
            </div>
            <div class="art-details">
              <span v-if="art.producer" class="producer">生产厂家: {{ art.producer }}</span>
              <span class="quantity">数量: {{ art.totalCells }} {{ art.cellUnit }}</span>
            </div>
          </div>
          <button 
            @click="removeArt(index)"
            class="remove-btn"
          >
            删除
          </button>
        </div>
      </div>
    </div>
    
    <div class="debug-section">
      <h3>调试信息</h3>
      <div class="debug-info">
        <h4>当前部门编码:</h4>
        <pre>{{ deptCode || '未设置' }}</pre>
        
        <h4>最后添加的品种数据:</h4>
        <pre>{{ JSON.stringify(lastAddedArt, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MakerDeptArt from '../src/makerDeptArt.vue'

// 响应式数据
const deptCode = ref('001') // 默认部门编码
const addedArts = ref<any[]>([]) // 添加的品种列表
const lastAddedArt = ref(null) // 最后添加的品种数据
const makerDeptArtRef = ref() // 组件引用

// 方法
const updateDeptCode = () => {
  // 切换部门编码用于测试
  deptCode.value = deptCode.value === '001' ? '002' : '001'
  console.log('部门编码已更新为:', deptCode.value)
}

const handleAddArt = (artData: any) => {
  console.log('接收到添加品种事件:', artData)
  
  // 保存最后添加的数据用于调试
  lastAddedArt.value = artData
  
  // 添加到列表
  addedArts.value.push({
    ...artData,
    addTime: new Date().toLocaleString() // 添加时间戳
  })
  
  console.log('当前品种列表:', addedArts.value)
}

const clearForm = () => {
  // 调用组件的清空方法
  if (makerDeptArtRef.value && typeof makerDeptArtRef.value.clearForm === 'function') {
    makerDeptArtRef.value.clearForm()
    console.log('表单已清空')
  }
}

const clearResults = () => {
  addedArts.value = []
  lastAddedArt.value = null
  console.log('结果已清空')
}

const removeArt = (index: number) => {
  addedArts.value.splice(index, 1)
  console.log('已删除品种，当前列表:', addedArts.value)
}
</script>

<style scoped>
.maker-dept-art-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
}

.form-section {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.component-demo {
  margin: 20px 0;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.actions {
  margin-top: 16px;
}

.result-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.empty-state {
  text-align: center;
  color: #999;
  padding: 40px;
  font-style: italic;
}

.art-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.art-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.art-info {
  flex: 1;
}

.art-name {
  margin-bottom: 8px;
}

.art-spec {
  margin-left: 8px;
  color: #666;
  font-weight: normal;
}

.art-details {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

.remove-btn {
  padding: 4px 12px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.remove-btn:hover {
  background: #ff7875;
}

.debug-section {
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background-color: #f6f6f6;
}

.debug-info h4 {
  margin: 16px 0 8px 0;
  color: #333;
}

.debug-info pre {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}

h2 {
  color: #1890ff;
  margin-bottom: 8px;
}

h3 {
  color: #333;
  margin-bottom: 16px;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

label {
  font-weight: 500;
  color: #333;
}
</style>
