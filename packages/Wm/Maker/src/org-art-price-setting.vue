<script setup lang="ts">
import { ref, toValue } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'
import { message } from 'ant-design-vue'
import { orgArtSaveApi } from '@mh-wm/util'

import { Button, Col, Form, FormItem, Input, InputNumber,Modal, Popover, RangePicker, Row, Tag, Tooltip } from 'ant-design-vue'
const modalVisible = defineModel('visible', {
  type: Boolean,
  default: false,
})
const emit = defineEmits(['ok'])

const formRef = ref()
const formState = ref<any>({})
const rules: Record<string, Rule[]> = {
  packPrice: [
    { required: false, message: '请输入整包销售单价' },
    { type: 'number', min: 0, message: '价格不能小于0' },
  ],
  cellPrice: [
    { required: false, message: '请输入拆零销售单价' },
    { type: 'number', min: 0, message: '价格不能小于0' },
  ],
  safePacks: [
    { required: false, message: '请输入安全库存包装数' },
    { type: 'number', min: 0, message: '库存数不能小于0' },
  ],
  pctAdd: [
    { required: false, message: '请输入加成比例' },
    { type: 'number', min: 0, max: 100, message: '加成比例应在0-100之间' },
  ],
}
const pricingMethodRef = ref()

// 初始化函数
async function init(artInfo: any, pricingMethod: number) {
  console.log('初始化机构商品设置', artInfo)
  // 深拷贝artInfo，避免直接修改原对象
  formState.value = JSON.parse(JSON.stringify(toValue(artInfo)))
  pricingMethodRef.value = pricingMethod

  // 确保必要的字段存在
  if (!formState.value.orgId) {
    // 从用户信息或其他地方获取orgId，这里先设置为默认值
    formState.value.orgId = 1 // 需要根据实际情况设置
  }

  // 设置默认值
  if (!formState.value.packPrice) formState.value.packPrice = 0
  if (!formState.value.cellPrice) formState.value.cellPrice = 0
  if (!formState.value.safePacks) formState.value.safePacks = 0
  if (!formState.value.pctAdd) formState.value.pctAdd = 0
  if (!formState.value.rackNo) formState.value.rackNo = ''
}

// 处理确定按钮点击
function handleOk() {
  formRef.value
    ?.validate()
    .then(async () => {
      try {
        console.log('准备保存机构商品设置:', formState.value)

        // 构建保存数据，确保包含必要字段
        const saveData = {
          ...formState.value,
          // 确保必要字段存在
          orgId: formState.value.orgId || 1, // 需要根据实际情况获取
          artId: formState.value.artId,
          packPrice: formState.value.packPrice || 0,
          cellPrice: formState.value.cellPrice || 0,
          safePacks: formState.value.safePacks || 0,
          pctAdd: formState.value.pctAdd || 0,
          rackNo: formState.value.rackNo || '',
        }

        const res = await orgArtSaveApi(saveData)
        message.success('保存成功')
        modalVisible.value = false
        emit('ok', formState.value)
      } catch (error: any) {
        console.error('保存失败:', error)
        // 显示更详细的错误信息
        const errorMsg = error?.response?.data?.message || error?.message || '保存失败，请稍后重试'
        message.error(errorMsg)
      }
    })
    .catch((info: any) => {
      console.log('表单验证失败:', info)
      message.warning('请检查表单数据')
    })
}

// 暴露方法给父组件使用
defineExpose({
  init,
})
</script>

<template>
  <Modal v-model:open="modalVisible" title="机构商品设置" :maskClosable="false" @ok="handleOk">
    <Form ref="formRef" :model="formState" :rules="rules" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
      <Row>
        <Col :span="12">
          <FormItem label="品名">
            {{ formState.artName }}
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="规格">
            {{ formState.artSpec }}
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="生产厂家">
            {{ formState.producer }}
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="包装材质">
            {{ formState.packMaterial }}
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="单位"> {{ formState.packCells }} {{ formState.cellUnit }} / {{ formState.packUnit }} </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="货位编号" name="rackNo">
            <Input v-model:value="formState.rackNo" />
          </FormItem>
        </Col>
        <Col :span="12" v-if="pricingMethodRef != 2">
          <FormItem label="整包销售单价" name="packPrice">
            <InputNumber v-model:value="formState.packPrice" :min="0" :addon-after="formState.packUnit" />
          </FormItem>
        </Col>
        <Col :span="12" v-if="pricingMethodRef != 2">
          <FormItem label="拆零销售单价" name="cellPrice">
            <InputNumber v-model:value="formState.cellPrice" :min="0" :addon-after="formState.cellUnit" />
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="安全库存包装数" name="safePacks">
            <InputNumber v-model:value="formState.safePacks" :min="0" :addon-after="formState.packUnit" />
          </FormItem>
        </Col>
        <Col :span="12" v-if="pricingMethodRef == 2">
          <FormItem label="加成比例" name="pctAdd">
            <InputNumber v-model:value="formState.pctAdd" :min="0" addon-after="%" />
          </FormItem>
        </Col>
      </Row>
    </Form>
  </Modal>
</template>

<style scoped>
/* 可以在这里添加组件特定的样式 */
</style>
