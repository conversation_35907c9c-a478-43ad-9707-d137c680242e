<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { Button, Col, Form, FormItem, Input, InputNumber, Popover, RangePicker, Row, Tag, Tooltip } from 'ant-design-vue'
import { WmDeptArtSelect } from '@mh-inpatient-hsd/selector'

// 定义props
const props = defineProps({
  deptCode: {
    type: String,
    default: ''
  }
})

// 定义组件事件
const emit = defineEmits(['addArt'])

// 表单数据
const formState = reactive({
  deptCode: props.deptCode || '',
  artIds: [] as number[],
  hasStock: false,
})

// 缓存选中的品种数据
const selectedArtData = ref(null)

// 搜索表单数据
const searchFormModel = reactive({
  batchNo: '',
  expiry: '',
  originPlace: '',
  dateManufactured: '',
  packPrice: undefined,
  stockPacksTotal: '',
  totalPacks: undefined,
  stockCellsTotal: undefined,
  packCells: undefined,
  totalCells: '',
  cellPrice: undefined,
  packUnit: undefined,
  cellUnit: '',
  splittable: undefined,
})

// 品种选择组件引用
const artSelectRef = ref()
const totalCellsRef = ref()

// 添加品种
const onAddArt = () => {
  // 验证数量不能为空且必须大于0
  if (searchFormModel.totalCells === undefined || searchFormModel.totalCells === null || searchFormModel.totalCells === '' || Number(searchFormModel.totalCells) <= 0) {
    message.error('数量不能为空且必须大于0')
    nextTick(() => {
      totalCellsRef.value?.focus()
    })
    return
  }

  // 克隆当前表单数据
  const formData = {
    ...searchFormModel,
    // 添加选中的品种数据（如果有的话）
    artData: selectedArtData.value,
    // 如果没有选择品种，则使用表单中的基本信息
    artId: selectedArtData.value?.artId || null,
    artCode: selectedArtData.value?.artCode || '',
    artName: selectedArtData.value?.artName || '',
    artSpec: selectedArtData.value?.artSpec || '',
    producer: selectedArtData.value?.producer || '',
  }

  // 发射事件，返回表单数据
  emit('addArt', formData)

  // 清空表单
  clearFormData()

  // 聚焦回品种选择框，方便继续添加
  nextTick(() => {
    if (artSelectRef.value && typeof artSelectRef.value.focus === 'function') {
      artSelectRef.value.focus()
    }
  })
  console.log('添加品种成功，表单数据：', formData)
  // 返回表单数据，方便外部使用
  return formData
}

// 处理品种选择
const handleArtSelect = (selectedArt: any) => {
  if (!selectedArt) return

  console.log('选中品种数据:', selectedArt)

  // 将选中的品种数据缓存到新变量中
  selectedArtData.value = selectedArt

  // 更新表单中的相关字段 - 包装相关信息
  searchFormModel.packUnit = selectedArt.packUnit || ''
  searchFormModel.cellUnit = selectedArt.cellUnit || ''
  searchFormModel.splittable = selectedArt.splittable !== undefined ? selectedArt.splittable : 0
  searchFormModel.packCells = selectedArt.packCells || undefined

  // 价格相关信息（如果品种数据中包含价格）
  if (selectedArt.packPrice !== undefined) {
    searchFormModel.packPrice = selectedArt.packPrice
  }
  if (selectedArt.cellPrice !== undefined) {
    searchFormModel.cellPrice = selectedArt.cellPrice
  }

  // 如果有artId，添加到artIds数组中
  if (selectedArt.artId && !formState.artIds.includes(selectedArt.artId)) {
    formState.artIds.push(selectedArt.artId)
  }

  // 聚焦到数量输入框，方便用户直接输入数量
  nextTick(() => {
    if (totalCellsRef.value) {
      totalCellsRef.value.focus()
    }
  })
}

// 清空表单数据
const clearFormData = () => {
  console.log('开始清空MakerDeptArt组件表单数据')

  // 重置表单数据
  searchFormModel.totalCells = ''
  searchFormModel.cellUnit = ''
  searchFormModel.packUnit = undefined
  searchFormModel.splittable = undefined
  searchFormModel.packCells = undefined

  // 清空品种选择组件
  if (artSelectRef.value) {
    try {
      if (typeof artSelectRef.value.init === 'function') {
        artSelectRef.value.init()
        console.log('已调用品种选择组件的init方法')
      }
    } catch (error) {
      console.error('清空品种选择组件时出错:', error)
    }
  }

  // 清空缓存的品种数据
  selectedArtData.value = null

  console.log('MakerDeptArt组件表单数据清空完成')
}

// 聚焦到下一个输入框
const focusNext = (refName: string) => {
  nextTick(() => {
    if (refName === 'totalCellsRef') {
      if (totalCellsRef.value) {
        if (typeof totalCellsRef.value.focus === 'function') {
          totalCellsRef.value.focus()
        } else if (totalCellsRef.value.$el && typeof totalCellsRef.value.$el.focus === 'function') {
          totalCellsRef.value.$el.focus()
        } else if (totalCellsRef.value.input && typeof totalCellsRef.value.input.focus === 'function') {
          totalCellsRef.value.input.focus()
        }
      }
    }
  })
}

// 暴露方法给外部使用
defineExpose({
  clearFormData,
  clearForm: clearFormData, // 添加clearForm别名，方便外部调用
})
</script>

<template>
  <Form layout="inline" :model="searchFormModel">
    <FormItem label="品种查询" name="keyword">
      <div class="art-select-box">
        <WmDeptArtSelect
          ref="artSelectRef"
          :deptCode="formState.deptCode"
          @selected="handleArtSelect"
          :hasStock="formState.hasStock"
          style="width:500px"
          placeholder="请选择品种"
          @keydown.enter="focusNext('totalCellsRef')"
        />
      </div>
    </FormItem>
    <FormItem label="整包数量" name="totalCells">
      <InputNumber
        ref="totalCellsRef"
        v-model:value="searchFormModel.totalCells"
        style="width: 150px"
        :min="0"
        :precision="0"
        :addon-after="searchFormModel.cellUnit"
        placeholder="请输入数量"
        @keydown.enter="onAddArt()"
      />
    </FormItem>
    <FormItem>
      <Button @click="onAddArt">添加</Button>
    </FormItem>
  </Form>
</template>
