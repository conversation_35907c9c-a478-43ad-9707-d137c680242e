// 申请单相关API

/**
 * 获取申请单列表
 * @param params
 */
export function findOrderLsApi(params: any) {
  return http.post('/microhis-hsd/order/findLsByVisit', params, { appKey: 'hsd' })
}

/**
 * 获取申请单检查报告
 * @param orderId
 * @param orgId
 */
export function findInspectReportApi(orderId: number, orgId?: any) {
  return http.post('/microhis-hsd/order/inspectionReport', { orderId: orderId, orgId: orgId }, { appKey: 'hsd' })
}

/**
 * 获取申请单检验报告
 * @param orderId
 * @param orgId
 */
export function findClinicalReportApi(orderId: number, orgId?: any) {
  return http.post('/microhis-hsd/order/clinicalReport', { orderId: orderId, orgId: orgId }, { appKey: 'hsd' })
}
