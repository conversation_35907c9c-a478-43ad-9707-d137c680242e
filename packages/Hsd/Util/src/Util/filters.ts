import dayjs from 'dayjs'

/**
 * 日期格式化 YYYY-MM-DD
 * @param value
 */
export function priceFormat (value: number | undefined): string {
  if (!value) {
    return ' '
  }
  if (Number.isNaN(value) || value === 0) {
    return '0.00'
  }
  // 将价格四舍五入到最接近的整数，并将价格转为字符串进行处理
  const roundedValue = Number(Math.round(value * 100) / 100).toString()

  // 获取整数部分
  const integerPart = roundedValue.split('.')[0]

  // 对整数部分进行千分位格式化
  const integerPartFormatted = integerPart.replace(/(\d)(?=(\d{3})+$)/g, '$1,')

  let floatPart = '.00' // 定义小数部分
  const decimalPart = roundedValue.split('.')[1]

  // 如果有小数部分，处理小数部分的长度
  if (decimalPart) {
    floatPart = '.' + decimalPart.padEnd(2, '0')
  }

  // 返回格式化后的价格字符串
  return `${integerPartFormatted}${floatPart}`
}

export function numericFormat (value: number): string {
  if (!value || value < 0) {
    return ''
  }
  const dataStr = value.toString()
  // 确保数值是8位，如果不是则返回空字符串
  if (dataStr.length !== 8) {
    return ''
  }
  return `${dataStr.slice(0, 4)}-${dataStr.slice(4, 6)}-${dataStr.slice(6, 8)}`
}

/**
 * 日期格式化 YYYY年M月D日
 * @param dataStr
 * @param pattern
 * @returns
 */
export function dateFormatYMD (dataStr: any, pattern = 'YYYY年M月D日'): string {
  if (!dataStr) {
    return ''
  }
  return dayjs(dataStr).format(pattern)
}

export function dateFormatH (dataStr: any, pattern = 'YYYY-MM-DD HH:mm'): string {
  if (!dataStr) {
    return ''
  }
  return dayjs(dataStr).format(pattern)
}

/**
 * 年龄格式化
 * @param ageOfYears
 * @param ageOfDays
 * @returns
 */
export function ageFormatYearDays (ageOfYears: number | null, ageOfDays: number | null): string {
  if (ageOfDays == null || ageOfDays < 0) {
    ageOfDays = 0;
  }
  let year = ageOfYears ? ageOfYears : 0
  let month = Math.floor(ageOfDays / 30)
  let day = ageOfDays % 30

  if (year > 6) {
    return year + '岁'
  } else if (year > 0) {
    return year + '岁' + month + '月'
  } else if (month > 0 && day > 0) {
    return month + '月' + day + '天'
  } else if (month > 0) {
    return month + '月'
  } else if (day > 0) {
    return day + '天'
  } else {
    return ''
  }
}

/**
 * 数字转中文
 * @param value
 */
export function numberToChinese (value: number | string): string {
  let num = Number(value);
  if (isNaN(num) || num === Infinity || num === -Infinity) {
    return ''
  }
  const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];

  let chineseStr = '';
  let unitIndex = 0;

  while (num > 0) {
    const digit = num % 10;
    if (digit > 0) {
      chineseStr = chineseNums[digit] + chineseStr;
    } if (digit === 0) {
      chineseStr = chineseStr + chineseNums[0];
    }
    num = Math.floor(num / 10);
    unitIndex++;
  }

  // 去掉开头的零
  chineseStr = chineseStr.replace(/^零+/, '');

  // 如果结果是空字符串，表示原数字为0，需要特殊处理
  if (chineseStr === '') {
    chineseStr = '零';
  }

  return chineseStr;
}
