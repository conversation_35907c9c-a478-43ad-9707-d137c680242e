<script setup lang="ts">
import { Card } from 'ant-design-vue'
import { BaseReadRecipe } from '@mh-hsd/base'

// 处方列表
const props = defineProps({
  visitId: {
    type: Number,
    default: 0
  },
  orgId: {
    type: Number,
    default: 0
  },
  userFiles: {
    type: String,
    default: ''
  }
})

import { findRecipeLsApi, RecipeTypeEnum } from '@mh-hsd/util'

const loading = shallowRef<boolean>(false)
const recipeLs = ref<any[]>([])

async function getRecipeLs() {
  loading.value = true
  try {
    const prams = {
      orgId: props.orgId,
      S_EQ_t_recipe__Visit_ID: props.visitId,
      S_IN_t_recipe__Recipe_Type_ID: '' + RecipeTypeEnum.WEST_DRUG + ',' + RecipeTypeEnum.CHINESE_DRUG + '',
      S_NOTIN_t_recipe__Exec_Status: '4,5',
      S_ISNOTNULL_t_recipe_extra__Time_Signed: 1
    }
    const data: any = await findRecipeLsApi(prams)
    loading.value = false
    recipeLs.value = data ?? []
  } catch (err) {
    console.log(err)
    loading.value = false
  }
}

watch(() => props.visitId, (val) => {
  if (val) {
    getRecipeLs()
  }
}, {
  immediate: true,
  deep: true
})
</script>

<template>
  <div v-for="(recipe, index) in recipeLs" :key="index" class="recipe-item">
    <Card :bordered="false" m-b-5px>
      <base-read-recipe type="inline" :recipe="recipe" :file-path="userFiles"/>
    </Card>
  </div>
</template>

<style scoped lang="less">
.recipe-item {
  :deep(.ant-card-bordered) {
    border: 0 !important;
  }
  :deep(.custom-typename) {
    white-space: nowrap;
  }
}
</style>
