<script setup lang="ts">
// 健康档案明细
defineProps({
  visitId: {
    type: Number,
    required: true
  },
  orgId: {
    type: Number,
    required: true
  },
  canRef: {
    type: Boolean,
    default: false
  },
  userFiles: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['confirm'])

import VisitView from './visit/view.vue'
import RecipeList from './recipe/list.vue'
import TreatmentList from './treatment/list.vue'
import ApplyList from './apply/index.vue'

function handleRefConfirm (refType: string, value: any) {
  emit('confirm', refType, value)
}

</script>

<template>
  <div>
    <visit-view :visitId="visitId" :isRef="canRef" @confirm="handleRefConfirm"/>
    <recipe-list :visitId="visitId" :orgId="orgId" :userFiles="userFiles"/>
    <apply-list :visitId="visitId" :orgId="orgId"/>
    <treatment-list :visitId="visitId"/>
  </div>
</template>

<style scoped lang="less">
</style>
