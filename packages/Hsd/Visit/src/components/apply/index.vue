<script setup lang="ts">
// 申请单列表
import { Card, Row, Col, Button, Table } from 'ant-design-vue'
import type { TableColumnType } from 'ant-design-vue'

import { ClinicalReport, InspectionReport } from '@mh-hsd/report'

import { findOrderLsApi, ageFormatYearDays, dateFormatYMD, priceFormat, RecipeTypeEnum } from '@mh-hsd/util'

const props = defineProps({
  visitId: {
    type: Number,
    default: 0
  },
  orgId: {
    type: Number,
    default: 0
  }
})

const columns: TableColumnType[] = [
  {
    title: '检验项目',
    dataIndex: 'indicatorName'
  },
  {
    title: '结果',
    dataIndex: 'resultVal',
    align: 'center'
  },
  {
    title: '单位',
    dataIndex: 'unit',
    align: 'center'
  },
  {
    title: '参考值',
    dataIndex: 'refDesc',
    align: 'center'
  }
]

const orderLs = ref<any>([])
const clinicalReportRef = ref()
const clinicalReportVisible = shallowRef<boolean>(false)
const inspectionReportRef = ref()
const inspectionReportVisible = shallowRef<boolean>(false)

async function findOrderLs() {
  try {
    const params = {
      S_IN_t_recipe__Recipe_Type_ID: '' + RecipeTypeEnum.LABORATORY + ',' + RecipeTypeEnum.MEDICAL_CHECK + '',
      S_EQ_t_visit__Visit_ID: props.visitId,
      S_NOTIN_t_recipe__Exec_Status: '4,5',
      S_ISNOTNULL_t_recipe_extra__Time_Signed: 1,
      sidx: 't_recipe.Recipe_TypeID ASC, t_recipe_extra.Time_Signed',
      order: 'desc'
    }
    const data: any = await findOrderLsApi(params)
    orderLs.value = data ?? []
  } catch (err) {
    console.log(err)
  }
}

function handleReport (item: any) {
  if (item.recipeTypeId === RecipeTypeEnum.LABORATORY) {
    clinicalReportVisible.value = true
    nextTick(() => {
      clinicalReportRef.value.init(item.orderId, props.orgId)
    })
  } else {
    inspectionReportVisible.value = true
    nextTick(() => {
      inspectionReportRef.value.init(item.orderId, props.orgId)
    })
  }
}

const amount = computed(() => {
  return (recipeDetailLs: any) => {
    let amount = 0
    recipeDetailLs.forEach((item: any) => {
      if (item.amount) {
        amount = Number(amount) + Number(item.amount)
      }
    })
    if (amount) {
      amount = parseFloat((amount).toFixed(2))
    }
    return amount
  }
})

watch(() => props.visitId, (val) => {
  if (val) {
    findOrderLs()
  }
}, {
  immediate: true,
  deep: true
})
</script>

<template>
  <div>
    <div v-for="item in orderLs" :key="item.orderId">
      <Card :bordered="false" m-b-5px :body-style="{ padding: '20px' }">
        <h2 font-size-16px font-bold text-center>{{ item.orgName }}</h2>
        <div font-size-16px font-bold text-center>{{ item.recipeTypeId === RecipeTypeEnum.LABORATORY ? '检验单' : '检查单' }}</div>
        <Row>
          <Col :span="24">费别：{{ item.insuranceName }}</Col>
          <Col :span="12">单号：{{ item.accessionNo }}</Col>
          <Col :span="12" text-right><span>门诊号：{{ item.visitId }}</span></Col>
          <Col :span="24">
            <div class="custom-doubleline"></div>
          </Col>
          <Col :span="14">
            <span m-r-10px>姓名：{{ item.patientName }}</span>
            <span m-r-10px>性别：{{ item.patientGenderName }}</span>
            <span m-r-10px>年龄：{{ ageFormatYearDays(item.ageOfYears, item.ageOfDays) }}</span>
          </Col>
          <Col :span="10" text-right>科室：{{ item.applyDeptname }}</Col>
          <Col :span="12">执行科室：{{ item.exceDeptname }}</Col>
          <Col :span="12" text-right>
            <span>日期：{{ dateFormatYMD(item.timeCreated) }}</span>
          </Col>
          <Col :span="24" h-auto>
            <span>申请目的：</span>{{ item.purposeDesc }}
          </Col>
          <template v-if="item.recipeTypeId === RecipeTypeEnum.LABORATORY">
            <Col :span="24" h-auto>
              <span font-bold color="#00061b">检验项目：</span>
            </Col>
            <Col :span="24" p-x-30px m-b-5px>
              <Row>
                <Col v-for="detail in item.recipeDetailLs" :key="detail.lineNo" :span="24">
                  <span m-r-10px>{{ detail.artName }}</span>
                  <span v-if="detail.specimenTypeName">（<span>{{ detail.specimenTypeName }}</span>）</span>
                </Col>
              </Row>
            </Col>
          </template>
          <template v-else>
            <Col :span="24" h-auto>
              <span font-bold color="#00061b">检查项目：</span>
            </Col>
            <Col :span="24" p-x-30px m-b-5px>
              <Row>
                <Col v-for="detail in item.recipeDetailLs" :key="detail.lineNo" :span="24">
                  <span m-r-10px>{{ detail.artName }}</span>
                  <span v-if="detail.recipeDetailPosLs && detail.recipeDetailPosLs.length > 0">
                    <span>【</span>
                    <span v-for="position in detail.recipeDetailPosLs" :key="position.bodypartId" m-r-10px>
                      {{ position.bodypartName }}
                    </span>
                    <span>】</span>
                  </span>
                </Col>
              </Row>
            </Col>
          </template>
          <Col :span="12">
            <span>申请医师：{{ item.clinicianName }}</span>
          </Col>
          <Col :span="12" text-right>
            <span>金额：{{ priceFormat(amount(item.recipeDetailLs)) }}</span>
          </Col>
          <Col v-if="!item.orderReportLs || item.orderReportLs.length === 0" :span="24" text-right>
            <Button type="primary" size="small" @click="handleReport(item)">查看报告</Button>
          </Col>
        </Row>
        <div v-for="report in item.orderReportLs" :key="report.reportId">
          <div class="bline-solid"></div>
          <Table v-if="item.recipeTypeId === RecipeTypeEnum.LABORATORY" :data-source="report.itemResultLs" :columns="columns">
            <template #bodyCell="{ column, text }: any">
              <template v-if="column.dataIndex === 'result'">
                <span>{{ text }}</span>
              </template>
            </template>
          </Table>
          <template v-else>
            <div font-bold>检查所见：</div>
            <div font-size-12px m-t-5px m-b-10px>{{ report.inspectionDesc }}</div>
            <div font-bold>诊断意见：</div>
            <div font-size-12px m-t-5px m-b-10px>{{ report.conclusionDesc }}</div>
          </template>
          <div class="bline-solid"></div>
          <Row>
            <Col :span="12">
              <span>报告医生：{{ report.inspectorName }}</span>
            </Col>
            <Col :span="12" text-right>
              <span>审核医生：{{ report.qcName }}</span>
            </Col>
            <Col :span="24">
              <div class="bline-solid"></div>
            </Col>
            <Col :span="24">
              <span>重要资料 注意保存 复查带回 本报告仅用作临床参考，不能用于其他证明</span>
            </Col>
            <Col :span="24" text-right>
              <Button type="primary" size="small" @click="handleReport(item)">查看报告</Button>
            </Col>
          </Row>
        </div>
      </Card>
    </div>
    <!-- 报告 -->
    <clinical-report ref="clinicalReportRef" v-if="clinicalReportVisible" @close="clinicalReportVisible = false"/>
    <inspection-report ref="inspectionReportRef" v-if="inspectionReportVisible" @close="inspectionReportVisible = false"/>
  </div>
</template>

<style scoped lang="less">
@import url(../style.css);
</style>
