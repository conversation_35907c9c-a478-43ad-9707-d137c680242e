<script setup lang="ts">
// 健康档案
import ArchiversDetail from './components/detail.vue'
import { <PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from 'ant-design-vue'

const emit = defineEmits(['confirm', 'close'])

defineProps({
  type: {
    type: String as () => 'inline' | 'modal' | 'drawer',
    default: 'inline',
    validator: (value: string) => ['inline', 'modal', 'drawer'].includes(value)
  },
  visible: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: '诊疗查看'
  },
  visitId: {
    type: Number,
    required: true
  },
  orgId: {
    type: Number,
    required: true
  },
  canRef: {
    type: Boolean,
    default: false
  },
  userFiles: {
    type: String,
    default: ''
  }
})

function handleRefConfirm (refType: string, value: any) {
  emit('confirm', refType, value)
}

function handleClose () {
  emit('close')
}
</script>

<template>
  <div>
    <!-- 内嵌模式 -->
    <template v-if="type === 'inline'">
      <archivers-detail
        :visitId="visitId"
        :orgId="orgId"
        :canRef="canRef"
        :userFiles="userFiles"
        @confirm="handleRefConfirm"
      />
    </template>

    <!-- 弹窗模式 -->
    <Modal
      v-else-if="type === 'modal'"
      :open="visible"
      :title="title"
      width="800px"
      @cancel="handleClose"
      :footer="null"
    >
      <archivers-detail
        :visitId="visitId"
        :orgId="orgId"
        :canRef="canRef"
        :userFiles="userFiles"
        @confirm="handleRefConfirm"
      />
    </Modal>

    <!-- 抽屉式模式 -->
    <Drawer
      v-else-if="type === 'drawer'"
      :open="visible"
      :title="title"
      width="800"
      :destroyOnClose="true"
      :bodyStyle="{ padding: 0 }"
      @close="handleClose"
    >
      <template #extra>
        <Button @click="handleClose">关闭</Button>
      </template>
      <archivers-detail
        :visitId="visitId"
        :orgId="orgId"
        :canRef="canRef"
        :userFiles="userFiles"
        @confirm="handleRefConfirm"
      />
    </Drawer>
  </div>
</template>

<style scoped>
</style>
