<script setup lang="ts">
// 检查报告
import { <PERSON><PERSON>, Ta<PERSON>, TabPane, Button, Empty } from 'ant-design-vue'
import { findInspectReportApi, numberToChinese } from '@mh-hsd/util'

const emit = defineEmits(['close'])

const open = shallowRef<boolean>(false)
const activeKey = shallowRef<number>(0)
const detailActiveKey = shallowRef<number>(1)
const reportLs = shallowRef<any>([])


const init = async (orderId: number, orgId: number) => {
  activeKey.value = 0
  detailActiveKey.value = 1
  open.value = true
  reportLs.value = []
  try {
    const res: any = await findInspectReportApi(orderId, orgId)
    if (res.code === 0) {
      reportLs.value = res.data
    }
  } catch (e) {
    console.log(e)
  }
}

function onClose () {
  open.value = false
  emit('close')
}

defineExpose({
  init
})
</script>

<template>
  <Drawer v-model:open="open" title="检查报告详情" width="900" destroyOnClose :bodyStyle="{ padding: 0 }">
    <template #extra>
      <Button @click="onClose">关闭</Button>
    </template>
    <template v-if="reportLs.length > 0">
      <template v-if="reportLs.length > 1">
        <Tabs m-l-10px v-model:activeKey="activeKey">
          <TabPane v-for="(report, index) in reportLs" :key="index">
            <template #tab>{{ numberToChinese(index + 1) }}</template>
            <template v-if="report.reportUrl && report.imageUrl">
              <Tabs m-l-10px v-model:activeKey="detailActiveKey">
                <TabPane :key="1" tab="报告">
                  <div class="w-full h-full">
                    <iframe :src="report.reportUrl" title="报告内容" border="0" w-full :height="850"/>
                  </div>
                </TabPane>
                <TabPane :key="2" tab="影像">
                  <div class="w-full h-full" overflow-auto>
                    <iframe :src="report.imageUrl" title="影像内容" w-full :height="770"/>
                  </div>
                </TabPane>
              </Tabs>
            </template>
            <template v-else-if="report.reportUrl">
              <div class="w-full h-full">
                <iframe :src="report.reportUrl" title="报告内容" border="0" w-full :height="850"/>
              </div>
            </template>
            <template v-else-if="report.imageUrl">
              <div class="w-full h-full" overflow-auto>
                <iframe :src="report.imageUrl" title="影像内容" w-full :height="770"/>
              </div>
            </template>
          </TabPane>
        </Tabs>
      </template>
      <template v-else>
        <Tabs m-l-10px v-model:activeKey="detailActiveKey">
          <TabPane v-if="reportLs[0].reportUrl" :key="1" tab="报告">
            <div class="w-full h-full">
              <iframe :src="reportLs[0].reportUrl" title="报告内容" border="0" w-full :height="850"/>
            </div>
          </TabPane>
          <TabPane v-if="reportLs[0].imageUrl" :key="2" tab="影像">
            <div class="w-full h-full" overflow-auto>
              <iframe :src="reportLs[0].imageUrl" title="影像内容" w-full :height="770"/>
            </div>
          </TabPane>
        </Tabs>
      </template>
    </template>
    <div v-else class="p-4">
      <Empty description="暂无数据" />
    </div>
  </Drawer>
</template>

<style scoped lang="scss">

</style>
