\<!-- 处方显示 -->
<script setup lang="ts">
import { shallowRef, computed } from 'vue'
import { BaseModal } from '@mh-base/modal'
import RecipeCompones from './recipe.vue'

interface Props {
  // 显示方式   inline || modal
  type?: string,
  // 单条处方数据
  recipe: object,
  // modal 弹出宽度
  modalWidth?: number | string,
  filePath?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'modal',
  modalWidth: 760,
  filePath: '/UserFiles'
})

const modalTitle = computed(() => {
  if (!props.recipe) return ''
  const { patientName, recipeTypeId } = props.recipe
  const recipeTypeName = recipeTypeId === 1 ? '西(成)药处方笺' : ( recipeTypeId === 2 ? '中药处方笺' : '')
  return `${patientName} - ${recipeTypeName}`
})

const visibleModal = shallowRef(false)

const openModal = () => {
  visibleModal.value = true
}

defineExpose({
  openModal
})
</script>
<template>
  <template v-if="type === 'inline'">
    <RecipeCompones :recipe="recipe" :filePath="filePath" />
  </template>
  <template v-else-if="type === 'modal'">
    <base-modal v-model:open="visibleModal" :title="modalTitle" :width="`${modalWidth}px`" :footer="null">
      <RecipeCompones :recipe="recipe" :filePath="filePath" />
    </base-modal>
  </template>
</template>


<style scoped lang="less">
.bgfff {
  background-color: #fff;
}
.m-b-5px {
  margin-bottom: 5px;
}
:deep(.ant-table-cell) {
  padding: 5px !important;
}

:deep(.ant-table) {
  background-color: unset;
}

.custom-typename {
  position: absolute;
  text-align: center;
  font-size: 12px;
  color: #000000;
  padding: 2px 5px;
  border-radius: 2px;
  right: 5px;
  top: 5px;
  border: 1px solid #000
}
</style>
