<template>
  <FormItem name="displayName" :label-col="{span: labelTitle ? 4 : 0}" :wrapper-col="{span: labelTitle ? 20 : 24}" :style="{ 'padding-left': labelTitle ? '0' : '10px' }">
    <template v-if="labelTitle" #label>
      {{ labelTitle }}
    </template>
    <InputGroup>
      <Select
        :ref="(e) => setRef(e,'artId')"
        v-model:value="formState.displayName"
        :placeholder="placeholder"
        :open="artOpen"
        :show-arrow="false"
        :show-search="true"
        :filter-option="false"
        :dropdown-match-select-width="false"
        popupClassName="art-select-container"
        :style="{ width: '70%' }"
        :disabled="disabled"
        @search="handleSearchArt"
        @select="handleArtSelect"
        @focus="handleArtFocus"
        @blur="artOpen = false">
        <SelectOption v-if="articleLs.length > 0" value="-1" disabled class="custom-art-header">
          <div class="custom-art-name">名称</div>
          <div class="custom-art-spec">规格</div>
          <div class="custom-art-producer">生产企业</div>
          <template v-if="showArtAll">
            <div class="custom-art-name">医保编码</div>
            <div class="custom-art-other">医保等级</div>
            <div class="custom-art-price">公示单价</div>
            <div class="custom-art-spec">库存数</div>
            <div class="custom-art-spec">是否禁售</div>
          </template>
        </SelectOption>
        <SelectOption v-if="articleLs.length > 0" value="-2" disabled></SelectOption>
        <SelectOption
          v-for="item in articleLs"
          :key="item.artId"
          :value="item.artId"
          :style="{ color: (item.saleDisabled === 1 || (!item.totalPacks && !item.totalCells)) ? 'red' : '' }">
          <div class="custom-art-name">{{ item.artName }}</div>
          <div class="custom-art-spec">{{ item.artSpec || '-' }}</div>
          <div class="custom-art-producer">{{ item.producer || '-' }}</div>
          <template v-if="showArtAll">
            <div class="custom-art-name">{{ item.miCode || '-' }}</div>
            <div class="custom-art-other">{{ item.chrgitmLv || '-' }}</div>
            <div class="custom-art-price">{{ item.listPrice ? priceFormat2(item.listPrice) : '-' }}</div>
            <div class="custom-art-spec">
              <span v-if="item.totalPacks || item.totalCells">
                <span>{{ item.totalPacks ? formatNumberWithCommas(item.totalPacks) : '' }}{{ item.totalPacks ? item.packUnit : '' }}</span>
                <span>{{ item.totalCells ? formatNumberWithCommas(item.totalCells) : '' }}{{ item.totalCells ? item.cellUnit : '' }}</span>
              </span>
              <span v-else>-</span>
            </div>
            <div class="custom-art-spec">{{ item.saleDisabled === 1 ? '是' : '否' }}</div>
          </template>
        </SelectOption>
        <SelectOption v-if="pages > 1" value="-4" disabled style="height: 50px;"></SelectOption>
        <SelectOption v-if="pages > 1" value="-3" disabled class="custom-art-footer">
          <div text-right flex-auto>
            <Space>
              <Button type="link" :disabled="pageNum === 1" :style="{ color: (pageNum === 1 ? 'rgba(0,0,0,0.45) !important' : '') }" @click="handlePreArt">上一页</Button>
              <Button type="link" :disabled="finished" :style="{ color: (finished ? 'rgba(0,0,0,0.45) !important' : '') }" @click="handleNextArt">下一页</Button>
            </Space>
          </div>
        </SelectOption>
      </Select>
      <FormItemRest>
        <Select v-model:value="searchType" @change="searchTypeChange">
          <SelectOption :value="1">混合</SelectOption>
          <SelectOption :value="2">五笔</SelectOption>
          <SelectOption :value="3">拼音</SelectOption>
        </Select>
      </FormItemRest>
    </InputGroup>
  </FormItem>
  <div v-if="formState.artId" class="art-selected-info">
    <span>{{ formState.artName }}</span>
    <span v-if="formState.artSpec"> | {{ formState.artSpec || '' }}</span>
    <span v-if="formState.producer"> | {{ formState.producer }}</span>
  </div>
</template>

<script setup lang="ts">
import { FormItem, FormItemRest, InputGroup, Select, SelectOption, Space, Button } from 'ant-design-vue'
import { priceFormat2, formatNumberWithCommas } from './utils/filters'
import { useArticle } from './hooks/useArticle'
import type { FormState } from './types'
import { RecipeTypeEnum } from '@mh-hsd/util'

const props = defineProps({
  labelTitle: {
    type: String,
    default: ''
  },
  formState: {
    type: Object as () => FormState,
    default: () => ({})
  },
  disabled: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: '请输入名称/编码/五笔/拼音码'
  },
  showArtAll: {
    type: Boolean,
    default: true
  },
  recipeTypeId: {
    type: Number,
    default: RecipeTypeEnum.WEST_DRUG
  },
  storeId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['setRef', 'artConfirm'])

const {
  finished,
  searchType,
  pageNum,
  pages,
  articleLs,
  artOpen,
  artRef,
  resetArt,
  handleArtFocus,
  handleSearchArt,
  searchTypeChange,
  handlePreArt,
  handleNextArt,
  handleArtSelect,
  setInputFocus
} = useArticle({
  formState: props.formState,
  storeId: props.storeId,
  recipeTypeId: props.recipeTypeId,
  onArtConfirm: (art) => emit('artConfirm', art)
})

function setRef(el: any, name?: string) {
  artRef.value = el
  emit('setRef', el, name)
}

defineExpose({ resetArt, setInputFocus })
</script>

<style scoped lang="less">
@import './style/index.less';
</style>
