export interface FormState {
  displayName: string
  artId?: string | number
  artName?: string
  artSpec?: string
  producer?: string
  applyDeptcode?: string
  deptCode?: string
  [key: string]: any
}

export interface Article {
  artId: string | number
  artName: string
  artSpec?: string
  producer?: string
  saleDisabled?: number
  totalPacks?: number
  totalCells?: number
  packUnit?: string
  cellUnit?: string
  miCode?: string
  chrgitmLv?: string
  listPrice?: number
}
