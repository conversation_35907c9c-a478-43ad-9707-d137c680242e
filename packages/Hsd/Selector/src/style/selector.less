.art-select-container {
  padding: 0;
  max-width: calc(100% - 300px);
  overflow: auto;
  .custom-art-header {
    position: fixed !important;
    z-index: 200 !important;
    background: #F0F0F0 !important;
    color: #000000 !important;
    font-weight: 700 !important;
    max-width: calc(100% - 300px);
  }
  .rc-virtual-list-holder {
    max-height: 400px !important;
  }
  .ant-select-dropdown .ant-select-item-option .ant-select-item-option-content {
    align-items: center;
    white-space: pre-wrap;
    display: flex;
  }
  .ant-select-item-option:nth-child(2n) {
    background: rgba(0, 0, 0, 0.04);
  }
  .ant-select-item-option-active {
    background: var(--pro-ant-color-primary) !important;
  }
  .ant-select-item-option-active .ant-select-item-option-content, .ant-select-item-option-active .ant-select-item-option-content > div{
    color: #fff !important;
  }

  .custom-art-check {
    width: 40px;
    float: left;
    text-align: center;
  }
  .custom-art-name {
    width: 200px;
    float: left;
    padding-right: 10px;
    white-space: break-spaces;
    word-break: break-word;
  }
  .custom-art-spec {
    width: 140px;
    float: left;
    text-align: center;
    padding-right: 10px;
    white-space: break-spaces;
    word-break: break-word;
  }
  .custom-art-price {
    width: 80px;
    float: left;
    padding-right: 10px;
    text-align: center;
  }
  .custom-art-producer {
    width: 250px;
    float: left;
    padding-right: 10px;
    white-space: break-spaces;
    word-break: break-word;
  }
  .custom-art-other {
    width: 80px;
    float: left;
    padding-right: 10px;
    text-align: center;
  }
  .custom-art-footer {
    position: absolute !important;
    bottom: 0;
    width: 100%;
    background: #FFFFFF !important;
    border-radius: 0;
  }
}

.art-selected-info {
  font-weight: bold;
  padding: 5px;
  margin-bottom: 10px;
  border-radius: 2px;
  border: 1px solid #FFD492;
  background-color: #FFF5E2;
} 