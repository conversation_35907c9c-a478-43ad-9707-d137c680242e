# 创建新文件
# HsdArtSelector 选择器组件

一个通用的选择器组件，支持搜索、分页、五笔拼音等功能。

## 安装

```bash
npm install @mh-hsd/selector
```

## 使用方法

```vue
<template>
  <HsdArtSelector
    v-model:value="formState.displayName"
    :form-state="formState"
    label-title="选择项目"
    :disabled="false"
    :show-art-all="true"
    @art-confirm="handleArtConfirm"
  />
</template>

<script setup>
import { reactive } from 'vue'
import { HshArtSelector } from '@mh-hsd/selector'

const formState = reactive({
  displayName: '',
  artId: '',
  artName: ''
})

const handleArtConfirm = (art) => {
  if (art && art.artId) {
    formState.artId = art.artId
    formState.artName = art.artName
  }
}
</script>
```

## Props

| 参数           | 说明       | 类型      | 默认值               |
|--------------|----------|---------|-------------------|
| labelTitle   | 标签文本     | string  | ''                |
| formState    | 表单状态对象   | object  | {}                |
| disabled     | 是否禁用     | boolean | false             |
| placeholder  | 输入框占位文本  | string  | '请输入名称/编码/五笔/拼音码' |
| showArtAll   | 是否显示所有字段 | boolean | true              |
| storeId      | 仓库ID     | string  | ''                |
| recipeTypeId | 处方类型     | number  | null              |

## Events

| 事件名称 | 说明 | 回调参数 |
| --- | --- | --- |
| setRef | 设置组件引用 | (el: any, name?: string) |
| artConfirm | 选择确认 | (art: any) |

## Methods

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| resetArt | 重置选择器 | - |
| setInputFocus | 设置输入框焦点 | - |
