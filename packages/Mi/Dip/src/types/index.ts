/**
 * Dip组件的类型定义
 */

/**
 * 显示模式
 * - switch: 开关模式
 * - checkbox: 复选框模式
 * - hidden: 隐藏模式
 */
export type DisplayMode = 'switch' | 'checkbox' | 'hidden'

/**
 * 严重程度
 * - 1: 明确违规
 * - 2: 高度可疑
 * - 3: 轻度可疑
 */
export type SeverityDegree = '1' | '2' | '3'

/**
 * 处理方式
 * - 1: 继续执行
 * - 2: 返回修改
 */
export type DisposalWay = 1 | 2

/**
 * 触发场景
 * - 1: 门诊处方签名
 * - 2: 门诊预结算
 * - 3: 住院医嘱签名
 * - 4: 住院预结算
 */
export type TriggerScene = 1 | 2 | 3 | 4

/**
 * 违规项
 */
export interface ViolationItem {
  /**
   * 违规ID
   */
  jrId: string

  /**
   * 规则名称
   */
  ruleName: string

  /**
   * 违规内容
   */
  volaCont: string

  /**
   * 严重程度
   */
  sevDeg: SeverityDegree

  /**
   * 违规依据
   */
  volaEvid: string

  /**
   * 反馈内容（前端使用，不需要传给后端）
   */
  feedback?: string

  /**
   * 是否已完成反馈（前端使用，不需要传给后端）
   */
  completed?: boolean
}

/**
 * 反馈项
 */
export interface FeedbackItem {
  /**
   * 违规结果ID
   */
  warnRsltId: string

  /**
   * 处理方式
   */
  dspoWay: DisposalWay

  /**
   * 处理原因
   */
  dspoWayRea: string
}

/**
 * 反馈参数
 */
export interface FeedbackParams {
  /**
   * 警告类型
   */
  warnType: number

  /**
   * 警告列表
   */
  warns: FeedbackItem[]
}

/**
 * 分析参数
 */
export interface AnalysisParams {
  /**
   * 就诊ID
   */
  visit_id: number

  /**
   * 医嘱列表
   * 可以是数字数组，表示医嘱ID列表
   * 也可以是对象数组，表示完整的医嘱信息
   */
  oe_list: number[] | any[]

  /**
   * 机构ID
   */
  org_id: number

  /**
   * 用户ID
   */
  user_id: number

  /**
   * 触发场景
   */
  trig_scen: TriggerScene

  /**
   * 收费ID（可选）
   */
  cash_id?: number
}

/**
 * 分析类型
 * - pre: 事前分析
 * - in: 事中分析
 */
export type AnalysisType = 'pre' | 'in'

/**
 * Dip组件Props
 */
export interface DipProps {
  /**
   * 触发场景
   */
  trigScen: TriggerScene

  /**
   * 分析类型
   * @default 'pre'
   */
  analysisType?: AnalysisType

  /**
   * 显示模式
   * @default 'hidden'
   */
  displayMode?: DisplayMode

  /**
   * 标签文本
   * @default '事前事中分析'
   */
  label?: string

  /**
   * 弹窗宽度
   * @default '700px'
   */
  modalWidth?: string | number

  /**
   * 弹窗标题
   * @default '事前事中分析'
   */
  modalTitle?: string

  /**
   * 默认启用状态
   * @default true
   */
  defaultEnabled?: boolean
}
