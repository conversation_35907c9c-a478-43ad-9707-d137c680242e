// 医保险种字典
export const INSU_TYPE = {
  '310': '职工基本医疗保险',
  '320': '公务员医疗补助',
  '330': '大额医疗费用补助',
  '340': '离休人员医疗保障',
  '390': '城乡居民基本医疗保险',
  '392': '城乡居民大病医疗保险',
  '410': '长期照护保险',
  '510': '生育保险'
} as const

// 医保人员类别字典
export const PSN_TYPE = {
  '11': '在职',
  '12': '退休',
  '13': '离休',
  '14': '居民（未成年）',
  '15': '居民（成年）',
  '16': '居民（老年）',
  '18': '农牧民中',
  '32': '老红军',
  '33': '一至六级伤残军人',
  '34': '建国前老工人',
  '99': '其他人员'
} as const

// 证件类型字典
export const MDTRT_CERT_TYPE = {
  '1': '电子医保',
  '2': '身份证',
  '3': '社保卡'
} as const

export const GENDER = {
  '1': '男',
  '2': '女'
} as const

// 签约业务类型字典
export const BUSINESS_TYPE = {
  '1': '门慢门特登记',
  '992': '居民门诊定点登记',
  '993': '离休人员门诊定点登记',
  '991': '门诊两病登记',
  '991101': '居民门诊两病',
  '2': '意外伤害登记',
  '3': '就医定点医疗机构登记',
  '4': '家庭病床登记',
  '5': '家庭医生签约登记',
  '6': '转诊转院登记',
  '7': '异地急诊住院登记',
  '8': '异地安置登记',
  '9': '特检特治登记',
  '10': '特殊药品登记',
  '11': '长期带药登记',
  '12': '代购药品登记',
  '13': '生育备案登记',
  '14': '账户备案登记',
  '15': '临时医疗救助人员资格认定',
  '16': '年度救助备案登记',
  '17': '按病种结算登记',
  '18': '无医保凭证结算备案登记',
  '19': '退费备案登记',
  '20': '外检外购登记',
  '21': '居家护理备案登记',
  '23': '门诊重大疾病外出异地备案',
  '88': '非差异化登记',
  '96': '两病三师选点',
  '97': '产前检查选点',
  '99': '其他',
  '101': '门慢登记',
  '102': '门特登记',
  '103': '门慢丙肝干扰素登记备案',
  '602': '离休人员转院审批（州外）',
  '603': '转院审批(统筹内)',
  '604': '临时性转院审批(统筹外)',
  '605': '慢性病门诊转诊审批',
  '606': '（统筹区内）上级医院转下级医院审批',
  '607': '转诊审批',
  '608': '特慢病转诊审批',
  '609': '临时性转院审批(统筹内)',
  '610': '离休人员转院审批（州内）',
  '801': '长期驻外和异地安置审批(统筹内)',
  '802': '长期驻外和异地安置审批(统筹外)',
  '990105': '重特大疾病登记',
  '991301': '转省外就医',
  '991701': '城乡两病认定',
  '991801': '交叉住院',
  '994': '择日住院备案',
  '995': '重大疾病登记',
  '996': '延时就医审批',
  '997': '中医适宜技术',
  '998': '中药饮片抗肿瘤',
  '999': '罕见病备案',
  '1001': '双通道抗肿瘤药品登记',
  '1002': '双通道单独支付药品登记',
  '1003': '专项用药登记备案',
  '5101': '一类慢特病定点登记',
  '5102': '二类慢特病定点登记',
  '5103': '精神病定点登记',
  '5104': '肝炎定点登记',
  '5105': '结核病定点登记',
  '5106': '器官移植术后治疗定点登记',
  '5107': '青光眼定点登记',
  '9501': '一次性卫生器材审批',
  '9502': '补充保险结算联系函审批',
  '9503': '异地普通门诊审批',
  '9504': '异地临时性住院审批',
  '9505': '据实结算审批',
  '9506': '居民门诊统筹审批',
  '9507': '居民尿毒症与重性精神病审批',
  '9508': '重性精神病审批',
  '9509': '离休人员特殊审批',
  '9510': '特殊材料审批',
  '9511': '跨省异地住院审批',
  '9512': '工伤医疗辅助器械审批',
  '9513': '院内转科(不收起付金)',
  '9514': '两病普通门诊用药保障对象',
  '9515': '谈判药品审批',
  '9516': '生育定额补助审批',
  '9517': '急诊抢救审批',
  '9518': '日间手术审批',
  '9519': '交叉住院审批',
  '9520': '血液制品审批',
  '9521': '新生儿审批',
  '9522': '新型冠状病毒肺炎审批',
  '9523': '工伤医疗转院审批',
  '9524': '特殊药品补助审批',
  '9525': '精神病住院审批',
  '9526': '住院延期审批',
  '9527': '康复登记',
  '9528': '门特专项治疗待遇登记',
  '9529': '门诊精神病登记',
  '9530': '门诊艾滋病登记',
  '9531': '双通道备案',
  '9801': '计生手术登记备案',
  '9802': '妊娠前期检查（小卡）登记备案',
  '9803': '妊娠后期检查（大卡）登记备案',
  '9804': '分娩登记备案',
  '9805': '生育转院登记备案',
  '9901': '职工门统登记',
  '9902': '儿童统筹登记',
  '9903': '居民定点变变更登记',
  '9904': '住院康复有一项情况的',
  '9905': '老年护理登记',
  '9906': '医保个人账户结转',
  '9907': '医保个人账户取消结转',
  '99061': '住院康复有一项并伴有二三四五中任何一项或多项情况的',
  '99071': '住院康复有二三四五项中任何一项或多项情况的',
  '9908': '住院康复合并器官切开的',
  '9909': '住院康复骨关节功能障碍',
  '9910': '门慢转诊登记',
  '9911': '住院跨县域审批',
  '9912': '生育跨县域审批',
  '9913': '门诊手术跨县域审批',
  '9914': '住院登记审批',
  '9915': '隔次退票审批',
  '9916': '门诊统筹审批',
  '9926': '离休备案定点',
  '992701': '取消结算审批',
  '992801': '门诊重症病',
  '992901': '本地住院登记审批',
  '993001': '起付线审批',
  '993101': '中心报销不按结算次序退票',
  '993201': '离休人员不能使用账户备案'
} as const

// 添加类型定义以获得更好的类型推导
export type InsutypeKey = keyof typeof INSU_TYPE
export type PsntypeKey = keyof typeof PSN_TYPE
export type CerttypeKey = keyof typeof MDTRT_CERT_TYPE
export type GenderKey = keyof typeof GENDER
export type BusinessTypeKey = keyof typeof BUSINESS_TYPE
