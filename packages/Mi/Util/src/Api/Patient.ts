// 患者卡信息类型定义
export interface PatientCardVo {
    patientName: string // 姓名
    genderId: number // 性别代号
    birthDate: number // 出生日期
    certTypeId: number // 身份证件类型代号
    idcertNo: string // 身份证件号码
    telNo?: string // 联系电话号码
    cardNo?: string // 就诊卡号
    familyZonecode?: string // 户籍地区划代码
    familyAddr?: string // 户籍地址
    familyZipcode?: string // 户籍地邮编
    livingAddr?: string // 现居住地
    livingZonecode?: string // 现居地区划代码
    livingZipcode?: string // 现居地邮编
    companyAddr?: string // 工作单位通信地址
    companyZonecode?: string // 工作单位区划代码
    companyTel?: string // 工作单位电话
    companyZipcode?: string // 工作单位邮编
    careerId?: number // 职业代号
    countryCode?: string // 国家代码
    nationalityCode?: string // 民族代码
    usedName?: string // 曾用名
    birthTime?: number // 出生时间
    birthPlace?: string // 出生地
    marriageStatusId?: number // 婚姻状况代号
    degreeId?: number // 学位代号
    eduBackgroundId?: number // 学历代号
    contactorName?: string // 联系人姓名
    contactorTel?: string // 联系人电话
    relationshipId?: number // 联系人与患者关系代号
    zoneCode?: string // 地区代码
  }

// 根据身份证号获取患者信息
export function getByIdcertNo(certTypeId : number, idcertNo : string) {
    console.log('getByIdcertNo', certTypeId, idcertNo)
    return http.post('/hip-base/patient/getByIdcertNo', {certTypeId, idcertNo}, { appKey: 'hip' })
}
// 保存患者卡信息


export function savePatientCard(params : PatientCardVo){
    return http.post('/hip-base/patient/savePatientCard', params, { appKey: 'hip' })
}