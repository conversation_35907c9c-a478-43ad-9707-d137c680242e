import { http } from '@idmy/core'
import { Currents } from '@mh-base/core'

/**
 * 参数配置项接口
 */
export interface OrgParamItem {
  /**
   * 机构ID
   */
  orgId: number

  /**
   * 参数ID
   */
  paramId: number

  /**
   * 参数值
   */
  paramVal: number

  /**
   * 参数代码
   */
  paramCode: string

  /**
   * 参数名称
   */
  paramName: string

  /**
   * 值类型
   */
  valueType: string
}

/**
 * 获取机构参数配置
 * @param orgId 机构ID
 * @param paramCodeLs 参数代码列表数组
 * @returns 参数配置信息
 */
export function findOrgParamByCode(orgId: number, paramCodeLs: string[]) {
  return http.post('/hip-base/orgparam/findByCode', { orgId, paramCodeLs }, { appKey: 'hip' })
}

/**
 * 获取是否启用DIP事前事中分析
 * @returns 是否启用DIP事前事中分析，返回true表示启用，false表示禁用
 */
export async function isEnabledDip(): Promise<boolean> {
  try {
    // 使用当前机构ID
    const orgId = Currents.tenantId
    // 参数代码
    const paramCode = 'INPATIENT_ENABLED_DIP'

    // 调用API获取参数配置
    const response = await findOrgParamByCode(orgId, [paramCode])

    // 打印返回结果，方便调试
    console.log('获取DIP启用状态结果:', response)

    // 检查返回结果
    if (Array.isArray(response) && response.length > 0) {
      // 找到匹配的参数配置
      const paramItem = response.find((item: OrgParamItem) => item.paramCode === paramCode)

      if (paramItem) {
        // 参数值为1表示启用，其他值表示禁用
        return paramItem.paramVal === 1
      }
    }

    // 默认返回false（启用）
    return false
  } catch (error) {
    console.error('获取DIP启用状态失败:', error)
    // 出错时默认返回false（启用）
    return false
  }
}
