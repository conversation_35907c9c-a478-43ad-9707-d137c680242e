<script setup lang="ts">
import { Descriptions } from 'ant-design-vue'
import type { BaseInfoType } from './types'

defineProps<{
  info: BaseInfoType
}>()
</script>

<template>
  <Descriptions :column="2" size="small">
    <Descriptions.Item label="人员编号">{{ info.psn_no || '-' }}</Descriptions.Item>
    <Descriptions.Item label="姓名">{{ info.psn_name || '-' }}</Descriptions.Item>
    <Descriptions.Item label="性别">{{ info.gend || '-' }}</Descriptions.Item>
    <Descriptions.Item label="出生日期">{{ info.brdy || '-' }}</Descriptions.Item>
    <Descriptions.Item label="证件类型">{{ info.psn_cert_type || '-' }}</Descriptions.Item>
    <Descriptions.Item label="证件号码">{{ info.psn_cert_no || '-' }}</Descriptions.Item>
    <Descriptions.Item label="联系电话">{{ info.tel || '-' }}</Descriptions.Item>
    <Descriptions.Item label="联系地址">{{ info.addr || '-' }}</Descriptions.Item>
    <Descriptions.Item label="险种">{{ info.insutype || '-' }}</Descriptions.Item>
    <Descriptions.Item label="账户余额">{{ info.balc || '-' }}</Descriptions.Item>
  </Descriptions>
</template>

<style scoped>
:deep(.ant-descriptions-item) {
  padding-bottom: 8px;
}

:deep(.ant-descriptions-item-label) {
  color: #666;
  width: 80px;
}
</style> 