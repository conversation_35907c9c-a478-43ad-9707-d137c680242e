<script setup lang="ts">
import * as antDesignVue from 'ant-design-vue'
const { Select: ASelect, SelectOption: ASelectOption } = antDesignVue
import { ref, computed } from 'vue'
import type { PropType } from 'vue'
import { BUSINESS_TYPE } from '@mh-mi/util'

defineOptions({
  name: 'BusinessTypeSelect'
})

const props = defineProps({
  modelValue: {
    type: [String, Number] as PropType<string | number>,
    default: ''
  },
  placeholder: {
    type: String as PropType<string>,
    default: '请选择业务类型'
  }
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string | number): void
  (e: 'change', value: string | number): void
}>()

// 将BUSINESS_TYPE转换为选项格式
const businessTypeOptions = computed(() => {
  return Object.entries(BUSINESS_TYPE).map(([value, label]) => ({
    value,
    label
  }))
})

const handleChange = (value: string | number) => {
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<template>
  <div class="business-type-select">
    <a-select
      :value="modelValue"
      :placeholder="placeholder"
      show-search
      option-filter-prop="label"
      allow-clear
      @change="handleChange"
      class="w-full"
    >
      <a-select-option
        v-for="item in businessTypeOptions"
        :key="item.value"
        :value="item.value"
        :label="item.label"
      >
        {{ item.label }}
      </a-select-option>
    </a-select>
  </div>
</template>

<style lang="less" scoped>
.w-full {
  width: 100%;
}

.business-type-select {
  :deep(.ant-select-selector) {
    border-radius: 4px;
  }
}
</style> 