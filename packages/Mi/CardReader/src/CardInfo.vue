<script setup lang="ts">
import { Descriptions } from 'ant-design-vue'
import type { CardInfoType } from './types'
import { formatCertType, formatGender } from '@mh-mi/util'

defineProps<{
  info: CardInfoType
}>()

</script>

<template>
  <Descriptions :column="2" size="small" :colon="false">
    <Descriptions.Item label="姓名">{{ info.psn_name || '-' }}</Descriptions.Item>
    <Descriptions.Item label="性别">{{ formatGender(info.gender) }}</Descriptions.Item>
    <Descriptions.Item label="年龄">{{ info.age || '-' }}</Descriptions.Item>
    <Descriptions.Item label="民族">{{ info.nation || '-' }}</Descriptions.Item>
    <Descriptions.Item label="出生日期" :span="2">{{ info.birthday || '-' }}</Descriptions.Item>
    <Descriptions.Item label="证件类型" :span="2">{{ formatCertType(info.psn_cert_type) }}</Descriptions.Item>
    <Descriptions.Item label="证件号码" :span="2">{{ info.psn_cert_no || '-' }}</Descriptions.Item>
    <Descriptions.Item label="卡类型" :span="2">{{ info.mdtrt_cert_type || '-' }}</Descriptions.Item>
    <Descriptions.Item label="卡号" :span="2">{{ info.mdtrt_cert_no || '-' }}</Descriptions.Item>
    <Descriptions.Item label="地址" :span="2">{{ info.address || '-' }}</Descriptions.Item>
  </Descriptions>
</template>

<style scoped>
:deep(.ant-descriptions-item) {
  padding: 4px 0 !important;
}

:deep(.ant-descriptions-item-label),
:deep(.ant-descriptions-item-content) {
  font-size: 13px;
}

:deep(.ant-descriptions-item-label) {
  color: #666;
  width: 70px;
}

:deep(.ant-descriptions-item-content) {
  padding-left: 8px !important;
}
</style> 