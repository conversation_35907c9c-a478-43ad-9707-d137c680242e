<script setup lang="ts">
import { getMiDisease } from '@mh-mi/util'
import { onMounted, ref, watch } from 'vue'
import * as antDesignVue from 'ant-design-vue'
const { Table, Spin } = antDesignVue

defineOptions({
  name: 'MiDiseaseInfo',
})

const props = defineProps({
  // 人员编号
  psnNo: {
    type: String,
    default: '',
  },
  // 患者ID
  patientId: {
    type: Number,
    default: undefined,
  },
  // 就诊ID
  visitId: {
    type: Number,
    default: undefined,
  },
  psnCertType: {
    type: String,
    default: '',
  },
  psnCertNo: {
    type: String,
    default: '',
  },
})

// 慢病信息列表
const diseaseInfoList = ref<any[]>([])
// 加载状态
const loading = ref(false)
// 是否有数据
const hasData = ref(false)

// 表格列定义
const columns = [
  {
    title: '疾病编码',
    dataIndex: 'opsp_dise_code',
    key: 'opsp_dise_code',
  },
  {
    title: '疾病名称',
    dataIndex: 'opsp_dise_name',
    key: 'opsp_dise_name',
  },
  {
    title: '确诊日期',
    dataIndex: 'begndate',
    key: 'begndate',
  },
  {
    title: '状态',
    dataIndex: 'enddate',
    key: 'enddate'
  },
]

// 获取慢病信息
const getDiseaseInfo = async () => {
  if (!props.psnNo) return

  try {
    loading.value = true
    const res = await getMiDisease({
      psn_no: props.psnNo,
      patient_id: props.patientId,
      visit_id: props.visitId,
      psn_cert_type: props.psnCertType,
      psn_cert_no: props.psnCertNo,
    })
    if (res?.feedetail && Array.isArray(res.feedetail)) {
      diseaseInfoList.value = res.feedetail
      hasData.value = res.feedetail.length > 0
    } else {
      diseaseInfoList.value = []
      hasData.value = false
    }
  } catch (error) {
    console.error('获取慢病信息失败:', error)
    diseaseInfoList.value = []
    hasData.value = false
  } finally {
    loading.value = false
  }
}

// 监听 psnNo 变化
watch(
  () => props.psnNo,
  val => {
    if (val) {
      getDiseaseInfo()
    } else {
      diseaseInfoList.value = []
      hasData.value = false
    }
  }
)

// 组件挂载时获取慢病信息
onMounted(() => {
  if (props.psnNo) {
    getDiseaseInfo()
  }
})

// 暴露方法给父组件
defineExpose({
  getDiseaseInfo
})
</script>

<template>
  <div class="mi-disease-info">
    <div v-if="loading" class="loading-container">
      <Spin />
    </div>
    <div v-else-if="hasData" class="disease-info-table">
      <Table :dataSource="diseaseInfoList" :columns="columns" :pagination="false" size="small" :scroll="{ y: 240 }" />
    </div>
    <div v-else class="mi-info-empty">暂无慢病信息</div>
  </div>
</template>

<style lang="less" scoped>
.mi-disease-info {
  min-height: 100px;
  position: relative;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 150px;
}

.mi-info-empty {
  text-align: center;
  color: #999;
  padding: 30px 0;
}

.disease-info-table {
  margin: 10px 0;
}
</style>
