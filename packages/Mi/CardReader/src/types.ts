// 读卡配置类型
interface CardReaderConfig {
  type: string
  callFunction: string | null
  callUrl: string
  isEncrypted: number
  isQrCode: number
  canFast: boolean | null
}

// 读卡数据类型
interface ReadCardData {
  psn_cert_type: string
  psn_cert_no: string
  psn_name: string
  psn_sex: string
  psn_nation: string
  psn_age: string
  psn_address: string
  psn_phone: string
  insuplc_admdvs: string
  card_sn: string | null
  mdtrt_cert_type: string
  mdtrt_cert_no: string
  certno: string
  age: string
  birthday: string | number
  gender: string
  nation: string
  address: string
  read_time: string
  extra_props: any
}

// 卡片模型类型
export interface CardModelType {
  list: Array<{
    code: string
    name: string
    cardReader: string
    readCard: CardReaderConfig
    getPersonInfo: CardReaderConfig
    others: string | null
  }>
  // 传入参数
  sParams: Record<string, any>
  // 读卡数据
  readCard: ReadCardData
  // 医保数据
  miData: Record<string, any>
  // 患者信息
  patientInfo: Record<string, any>
}

// 卡信息类型
export interface CardInfoType {
  mdtrt_cert_type: string
  mdtrt_cert_no: string
  card_sn: string | null
  psn_cert_type: string
  certno: string
  psn_cert_no: string
  psn_name: string
  age: string
  birthday: string | number
  gender: string
  nation: string
  address: string
  insuplc_admdvs: string | null
  extra_props: any
}

// 医保基础信息类型
export interface BaseInfoType {
  psn_no: string
  psn_name: string
  gend: string
  brdy: string
  psn_cert_type: string
  psn_cert_no: string
  tel: string
  addr: string
  insutype: string
  balc: string | number
  [key: string]: any
}

// 医保险种类型
export interface InsuType {
  key: string
  insutype: string
  insuplc_admdvs: string
  balc: string | number
  selected: boolean
  [key: string]: any
}

// 疾病信息类型
export interface DiseaseInfoType {
  diseaseCode: string
  diseaseName: string
  registerDate: string
  status: string
  [key: string]: any
} 