<script lang="ts" setup>
import { Api, Data, Format } from '@idmy/core'
import { BillDetail } from '@mh-bcs/bill'
import { PaidStatusHelp } from '@mh-bcs/help'
import { CashType, pageBillsByCashIdFromCashBill } from '@mh-bcs/util'
import { Table, TabPane, Tabs } from 'ant-design-vue'
import { PropType } from 'vue'
import FeeTypeByCashId from './Report/FeeTypeByCashId.vue'

defineProps({
  cashId: { type: Number as PropType<number>, required: true },
  cashType: { type: String as PropType<CashType>, required: true },
})

const columns: Data[] = [
  { align: 'center', dataIndex: 'billId', title: '划价流水', width: 90 },
  { align: 'center', dataIndex: 'clinicianName', title: '开单医生', width: 100 },
  { align: 'center', dataIndex: 'applyDeptName', minWidth: 100, title: '开单科室' },
  { align: 'center', dataIndex: 'execDeptName', minWidth: 100, title: '执行科室' },
  { align: 'center', dataIndex: 'billDate', title: '开单日期', width: 90 },
  { align: 'center', dataIndex: 'recipeNo', minWidth: 130, title: '处方单号' },
  { align: 'center', dataIndex: 'paidStatus', title: '支付状态', width: 80 },
  { align: 'right', dataIndex: 'amount', fixed: 'right', title: '金额', width: 100 },
]

const tabKey = ref('main')
</script>

<template>
  <Api v-slot="{ output, page }" :input="{ params: { cashId }, pageSize: 20 }" :load="pageBillsByCashIdFromCashBill" spin>
    <Tabs v-model:activeKey="tabKey" destroyInactiveTabPane>
      <TabPane key="main" tab="划价单">
        <FeeTypeByCashId :cashId="cashId" />
        <Table :columns="columns" :dataSource="output.list" :pagination="{ ...page, hideOnSinglePage: true }" bordered rowKey="billId" size="small">
          <template #headerCell="{ column }">
            <PaidStatusHelp v-if="column.dataIndex === 'paidStatus'" />
          </template>
          <template #bodyCell="{ column, record }">
            <Format v-if="column.dataIndex === 'amount'" :value="record.amount" type="Currency" />
            <Format v-if="column.dataIndex === 'paidStatus'" :colour="false" :value="record.paidStatus" params="PaidStatus" type="Enum" />
            <Format v-if="column.dataIndex === 'refundedAmount'" :value="record.refundedAmount ?? 0" type="Currency" />
          </template>
          <template #expandedRowRender="{ record }">
            <BillDetail :billIds="[record.billId]" show-cycle-count />
          </template>
        </Table>
      </TabPane>
      <TabPane v-if="cashType !== 'INPATIENT'" key="detail" tab="划价单明细">
        <FeeTypeByCashId :cashId="cashId" />
        <BillDetail :showHeader="false" :billIds="output.list.map(row => row.billId)" />
      </TabPane>
    </Tabs>
  </Api>
</template>
