<script lang="ts" setup>
import { Api, Format } from '@idmy/core'
import { countFeeTypeAmountByCashId } from '@mh-bcs/util'
import { Descriptions, DescriptionsItem } from 'ant-design-vue'

defineProps({
  cashId: { type: Number, required: true },
})
</script>

<template>
  <Api v-slot="{ output }" :load="() => countFeeTypeAmountByCashId(cashId)" first spin type="Array">
    <Descriptions v-if="output.length" :column="5" bordered mb-8px size="small">
      <DescriptionsItem v-for="item in output" :key="item.feeTypeName" :label="item.feeTypeName" :label-style="{ width: '130px' }">
        <Format :value="item.amount" type="Currency" />
      </DescriptionsItem>
    </Descriptions>
  </Api>
</template>
