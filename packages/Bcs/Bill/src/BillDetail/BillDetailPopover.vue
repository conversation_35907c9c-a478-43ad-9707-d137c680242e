<script lang="ts" setup>
import { Data, Format } from '@idmy/core'
import { Popover, Tag } from 'ant-design-vue'
import { isNil } from 'lodash-es'

defineProps({
  data: { type: Object as PropType<Data>, required: true },
})
</script>
<template>
  <Popover :mouseEnterDelay="1" destroyTooltipOnHide placement="right">
    <template #content>
      <Format v-if="data.packageId" :value="data.packageId" component="div" prefix="组套：" type="String" />
      <Format :value="data.artId" component="div" prefix="代号：" type="String" />
      <Format :value="data.artCode" component="div" prefix="编码：" type="String" />
      <Format :value="data.artName" component="div" prefix="名称：" type="String" />
      <Format v-if="data.artSpec" :value="data.artSpec" component="div" prefix="规格：" type="String" />
      <Format :value="data.unit" component="div" prefix="单位：" type="String" />
      <Format :value="data.price" component="div" params="4" prefix="单价：" type="Currency" />
      <Format :value="data.total" component="div" params="4" prefix="数量：" type="Currency" />
      <Format :value="data.amount" component="div" prefix="金额：" type="Currency" />
      <Format :value="data.discounted ?? 0" component="div" prefix="折让金额：" type="Currency" />
      <Format :value="data.derated ?? 0" component="div" prefix="减免金额：" type="Currency" />
      <Format v-if="!isNil(data.selfPaidPct)" :value="data.selfPaidPct" component="div" prefix="自付比例：" type="Percent" />
      <Format v-if="data.cellPrice" :value="data.cellPrice" component="div" params="4" prefix="制剂单价：" type="Currency" />
      <Format v-if="data.packPrice" :value="data.packPrice" component="div" params="4" prefix="包装单价：" type="Currency" />
      <Format v-if="data.packTotal" :value="data.packTotal" component="div" params="4" prefix="包装数量：" type="Currency" />
      <Format v-if="data.packUnit" :value="data.packUnit" component="div" prefix="包装单位：" type="String" />
      <Format v-if="data.billId" :value="data.billId" component="div" prefix="划价流水：" type="String" />
      <Format :value="`${data.feeTypeName} (${data.feeTypeId})`" component="div" prefix="费用类别：" type="String" />
      <Format v-if="data.miCode" :value="data.miCode" component="div" prefix="医保编码：" type="String" />
      <Format v-if="data.oeNo" :value="data.oeNo" component="div" prefix="医嘱号：" type="String" />
      <Format v-if="data.cycleCount" :value="data.cycleCount" component="div" prefix="周期数：" type="String" />
      <Format v-if="data.freqCode" :value="data.freqCode" component="div" prefix="频次代码：" type="String" />
      <Tag v-if="data.isCompound" color="red">复方</Tag>
      <Tag v-if="data.isEd" color="red">基本药物</Tag>
      <Tag v-if="data.nonMedicalFlag" color="red">非医疗</Tag>
      <Tag v-if="data.approvedFlag" color="red">医院审核</Tag>
      <Tag v-if="data.uploadedFlag" color="red">医保上传</Tag>
    </template>
    <slot />
  </Popover>
</template>
