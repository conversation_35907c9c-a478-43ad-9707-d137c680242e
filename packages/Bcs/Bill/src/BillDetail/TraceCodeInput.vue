<script lang="ts">
const api = (params) => http.post(`/api/bcs/BillDetailTrackCode/createOrUpdate`, params, { appKey: 'bcs' })
</script>
<script setup lang="ts">
import { Input } from 'ant-design-vue'


const DRUGS_CAT_TYPE_ID = [101, 102, 103, 104, 105]

const { billDetail } = defineProps({
  billDetail: { type: Object, required: true },
})

const trackCodes = ref(billDetail.trackCodes?.split(',') ?? [])

const enter = async (e: KeyboardEvent) => {
  const arr = trackCodes.value.filter(s => !!s)
  await api({
    billId: billDetail.billId,
    lineNo: billDetail.lineNo,
    trackCodes: arr.join(','),
  })
  Message.success(`录入成功`)
  e.preventDefault()
  const target = e.target as HTMLInputElement
  const currentRow = target.closest('tr')
  if (!currentRow) return

  // 尝试在当前行找下一个输入框
  const inputs = Array.from(currentRow.querySelectorAll('input'))
  const currentIndex = inputs.indexOf(target)
  if (currentIndex < inputs.length - 1) {
    inputs[currentIndex + 1].focus()
    return
  }

  // 如果当前行没有下一个输入框，查找下一行
  let nextRow = currentRow.nextElementSibling
  while (nextRow) {
    const firstInput = nextRow.querySelector('input')
    if (firstInput) {
      firstInput.focus()
      return
    }
    nextRow = nextRow.nextElementSibling
  }
}
</script>
<template>
<Input v-for="(_, idx) in billDetail.packTotal"
       :key="`${billDetail.billId}${billDetail.lineNo}${idx}`"
       class="w-100% m-y-1px"
       v-model:value="trackCodes[idx]"
       @keydown.enter="enter($event, idx)"
       :allowClear="true"
       v-if="DRUGS_CAT_TYPE_ID.includes(billDetail.catTypeId)"/>
</template>
