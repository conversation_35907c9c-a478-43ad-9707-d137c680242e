<script lang="ts" setup>
import { Api, Data, useLoading } from '@idmy/core'
import { getInvoiceByCashId, payUndo, red } from '@mh-bcs/util'
import { Alert, Button } from 'ant-design-vue'
import { PropType } from 'vue'

const props = defineProps({
  cashId: { type: Number as PropType<number>, required: true },
})
const router = useRouter()
const [onOk, okLoading] = useLoading(async (invoice: Data) => {
  if (!invoice?.droppedFlag) {
    try {
      await red(invoice?.invId)
    } catch (e) {
      console.error(e)
    }
  }
  await payUndo(props.cashId)
  await Modal.ok()
  await router.replace('/work/charging?tabKey=Settlement')
})
</script>

<template>
  <Alert message="请确认患者的后续流程尚未开始，例如：是否【未领取药物】、【未进行检查】、【未进行检验】或【未接受治疗】等。" type="warning" />
  <Api v-slot="{ output: invoice }" :load="() => getInvoiceByCashId(cashId)" first type="Object">
    <div flex-center mt-16px>
      <Button :loading="okLoading" type="primary" @click="onOk(invoice)">确认：后续流程尚未开始</Button>
    </div>
  </Api>
</template>
