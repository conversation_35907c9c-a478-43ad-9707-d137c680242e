<script lang="ts" setup>
import { Data } from '@idmy/core'
import Undo from './index.vue'

const { data } = defineProps({
  data: { type: Object as PropType<Data>, required: true },
  component: { type: String, default: 'a' },
})

const emit = defineEmits(['ok'])

const [openUndo] = useLoading(() =>
  Modal.open({
    component: Undo,
    width: 1,
    title: '重新结算',
    props: { cashId: data.cashId },
    onClose: (isOk: boolean) => {
      isOk && emit('ok')
    },
  })
)
</script>
<template>
  <template v-if="!data.checkId && data.amount > 0">
    <component v-bind="$attrs" :is="component" v-if="data.actionType === 'BLUE'" @click="openUndo">重新结算</component>
  </template>
</template>
