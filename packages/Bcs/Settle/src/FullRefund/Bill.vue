<script lang="ts" setup>
import { View } from '@idmy/antd'
import { Api, Data, Format, useLoading } from '@idmy/core'
import { BillDetail } from '@mh-bcs/bill'
import { createRedBillByBlueBillIds, listBluesWithExecStatusByCashId, listUnpaidRedsByCashId } from '@mh-bcs/util'
import { Button, Table, Tag, Tooltip } from 'ant-design-vue'

import { PropType } from 'vue'

defineProps({
  cashId: { type: Number as PropType<number>, required: true },
})

const redColumns: Data[] = [
  { align: 'center', dataIndex: 'patientName', width: 90, title: '患者' },
  { align: 'center', dataIndex: 'visitId', width: 90, title: '诊疗流水' },
  { align: 'center', dataIndex: 'recipeNo', width: 130, title: '处方号' },
  { align: 'center', dataIndex: 'billId', title: '划价流水', width: 90 },
  { align: 'center', dataIndex: 'blueBillId', title: '原划价流水', width: 90 },
  { align: 'left', dataIndex: 'billAbstract', minWidth: 130, title: '备注' },
  { align: 'center', dataIndex: 'billDate', title: '开单日期', width: 90 },
  { align: 'right', dataIndex: 'amount', title: '退费金额', width: 100 },
]

const columns: Data[] = [
  { align: 'center', dataIndex: 'refundableStatus', title: '执行状态', width: 80 },
  { align: 'center', dataIndex: 'recipeNo', minWidth: 130, title: '处方号' },
  { align: 'center', dataIndex: 'billId', title: '划价流水', width: 90 },
  { align: 'center', dataIndex: 'applyDeptName', minWidth: 100, title: '开单科室' },
  { align: 'center', dataIndex: 'clinicianName', title: '开单医生', width: 100 },
  { align: 'center', dataIndex: 'execDeptName', minWidth: 100, title: '执行科室' },
  { align: 'center', dataIndex: 'billDate', title: '开单日期', width: 90 },
  { align: 'right', dataIndex: 'amount', title: '收费金额', width: 100 },
  { align: 'right', dataIndex: 'refundedAmount', title: '已退金额', width: 100 },
]

const redRefBills = ref([])
const loadRedBills = ({ output }) => {
  redRefBills.value = output.map(row => row.blueBillId)
}

const selected = reactive<{ keys: number[]; rows: Data[] }>({
  keys: [],
  rows: [],
})

const rowSelection = {
  onChange: (keys: number[], rows: Data[]) => {
    selected.keys = keys
    selected.rows = rows
  },
  getCheckboxProps: (record: Data) => ({
    disabled: redRefBills.value.includes(record.billId),
  }),
}

const redRef = ref()
const onLoad = () => {
  redRef.value.onLoad()
}

const refreshing = ref(false)
const [onCreateRedBill, ing] = useLoading(async () => {
  await createRedBillByBlueBillIds(selected.keys)
  selected.keys = []
  selected.rows = []
  refreshing.value = true
  nextTick(() => {
    refreshing.value = false
  })
  onLoad()
})

const first = ref(false)
const onLoadBlue = () => {
  nextTick(() => {
    if (!first.value) {
      const el: any = document.querySelector('.surely-table-checkbox-input')
      el.click()
      first.value = true
    }
  })
}
</script>

<template>
  <Api ref="redRef" v-slot="{ output }" :load="() => listUnpaidRedsByCashId(cashId)" first spin type="Array" @load="loadRedBills">
    <Api v-if="!refreshing" ref="blueRef" v-slot="{ output: blues }" :load="() => listBluesWithExecStatusByCashId(cashId)" spin type="Array" @load="onLoadBlue">
      <View right-class="f1" title="红冲单">
        <template #left>已经产生的红单</template>
        <template #right>
          <Tooltip>
            <template #title>收费系统主动向其他系统发起红单生成。正常流程应该由其他系统（医生开红单、药房退药开红单）产生红单</template>
            <Button :disabled="!selected.keys.length" :loading="ing" type="primary" @click="onCreateRedBill()"> 生成红冲单 </Button>
          </Tooltip>
          <slot :blues="blues" :reds="output" />
        </template>
        <Table :columns="redColumns" :dataSource="output" :pagination="false" rowKey="billId">
          <template #emptyText>
            <div class="h-40px" flex items-center>暂无数据</div>
          </template>
          <template #bodyCell="{ column, record }">
            <Format v-if="column.dataIndex === 'billDate'" :value="String(record.billDate)" type="Date" />
            <Format v-if="column.dataIndex === 'amount'" :value="record.amount" type="Currency" />
          </template>
          <template #expandedRowRender="{ record }">
            <BillDetail :billIds="[record.billId]" />
          </template>
        </Table>
      </View>
      <View right-class="f1" title="原划价单">
        <Table :columns="columns" :dataSource="blues" :pagination="false" :row-selection="rowSelection" :rowClassName="row => (redRefBills.includes(row.billId) ? 'red-delete' : '')" rowKey="billId">
          <template #bodyCell="{ column, record }">
            <Format v-if="column.dataIndex === 'billDate'" :value="String(record.billDate)" type="Date" />
            <Format v-else-if="column.dataIndex === 'amount'" :value="record.amount" type="Currency" />
            <Format v-else-if="column.dataIndex === 'paidStatus'" :bordered="false" :component="Tag" :value="record.paidStatus" params="PaidStatus" type="Enum" />
            <Format v-else-if="column.dataIndex === 'refundedAmount'" :class="record.refundedAmount ? 'error' : ''" :value="record.refundedAmount ?? 0" type="Currency" />
            <Format v-else-if="column.dataIndex === 'refundableStatus'" :value="record.execStatus" params="ExecStatus" type="Enum" />
          </template>
          <template #expandedRowRender="{ record }">
            <BillDetail :billIds="[record.billId]" />
          </template>
        </Table>
      </View>
    </Api>
  </Api>
</template>
<style lang="less">
.red-delete div {
  text-decoration: line-through !important;
  color: red !important;
}
</style>
