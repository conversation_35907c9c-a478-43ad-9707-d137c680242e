<script lang="ts" setup>
import { Data, Format } from '@idmy/core'
import { InputSearch, Spin } from 'ant-design-vue'
import { isNil } from 'lodash-es'
import Nothing from './svg/icon_user.svg'
import Man from './svg/icon_user_man.svg'
import Woman from './svg/icon_user_woman.svg'

const emit = defineEmits(['update:modelValue'])

const { data } = defineProps({
  data: {
    type: Array as PropType<Data[]>,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  height: {
    type: Number,
    default: 0
  },
})

const selected = defineModel()
const keyword = ref('')
const filter = computed(() => data.filter(row => row.name.includes(keyword.value)))
watch(
  () => filter.value,
  () => {
    if (filter.value.length === 1) {
      selected.value = filter.value[0].key
    } else {
      if (keyword.value) {
        selected.value = ''
      }
    }
  }
)
</script>

<template>
  <div>
    <InputSearch v-model:value="keyword" placeholder="患者姓名/性别/年龄" />
    <div class="patient-list" :style="{ maxHeight: height - 32 + 'px', height: height - 32 + 'px' }">
      <div flex-center h-full v-if="loading">
        <Spin />
      </div>
      <template v-else>
        <div v-for="row in filter" :key="row.billId" class="patient-item" :class="{ 'patient-item-selected': selected === row.key }" @click="selected = row.key">
          <div class="patient-avatar" v-if="row.billType !== 'OTHER'">
            <img :src="Man" v-if="row.gender === 'MAN'" alt="男" />
            <img :src="Woman" v-else-if="row.gender === 'WOMAN'" alt="女" />
            <img :src="Nothing" v-else alt="未知" />
          </div>
          <div class="patient-info">
            <div class="patient-name">
              <div>{{ row.name0 }}</div>
              <div text-12px>{{ row.billCount }}</div>
            </div>
            <div class="patient-details">
              <span class="patient-age">
                <template v-if="!isNil(row.ageOfYears)">{{ row.ageOfYears }} 岁</template>
              </span>
              <Format type="Currency" :value="row.amount" />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<style lang="less" scoped>
.patient-list {
  overflow-y: auto;
  border: solid 1px #ddd;
  border-top: none;
  border-right: none;
}

.patient-item {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: #eee;
  }
}

.patient-item-selected {
  background-color: var(--primary-color-5);
  color: white !important;

  .patient-details {
    color: #eee !important;
  }

  &:hover {
    background-color: var(--primary-color-5);
  }
}

.patient-avatar {
  width: 30px;
  height: 30px;
  margin-right: 8px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.patient-info {
  flex: 1;
}

.patient-name {
  font-size: 14px;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
}

.patient-details {
  font-size: 12px;
  color: #888;
  display: flex;
  justify-content: space-between;
}
</style>
