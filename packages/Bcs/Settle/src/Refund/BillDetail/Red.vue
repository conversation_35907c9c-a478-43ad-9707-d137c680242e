<script lang="ts" setup>
import { add, Api, Data, Format } from '@idmy/core'
import { listMergedUnpaidByCashId } from '@mh-bcs/util'
import { Collapse, CollapsePanel, Space } from 'ant-design-vue'
import { PropType } from 'vue'
import Detail from './Detail.vue'

const { blueAmount } = defineProps({
  blueAmount: { type: Number, required: true },
  cashId: { type: Number as PropType<number>, required: true },
})

const emits = defineEmits(['load'])

const redAmount = ref(0)
const load = ({ output }: Data) => {
  redAmount.value = output.reduce((a: number, b: Data) => add(a, b.amount), 0)
  emits('load', output, redAmount.value)
}

const activeKey = defineModel('activeKey')
</script>
<template>
  <Collapse v-model:activeKey="activeKey" :bordered="true" accordion>
    <CollapsePanel key="1" :header="`本次退费#${cashId}`">
      <template #extra>
        <Format :value="redAmount" prefix="总金额：" type="Currency" value-class="error b" />
      </template>
      <Api v-slot="{ output: data }: Data" :load="() => listMergedUnpaidByCashId(cashId)" first spin type="Array" @load="load">
        <Detail :data="data" :height="127" />
      </Api>
      <div class="wp100" flex-center>
        <Space class="mt-8px fs16">
          <Format :value="blueAmount" component="div" suffix=" (退费前)" type="Currency" />
          <Format class="b" component="div" type="String" value="-" />
          <Format :value="-redAmount" component="div" suffix=" (本次退费)" type="Currency" />
          <Format class="b" component="div" type="String" value="=" />
          <Format :value="add(blueAmount, redAmount)" class="primary" component="div" suffix=" (剩余)" type="Currency" />
        </Space>
      </div>
    </CollapsePanel>
  </Collapse>
</template>
