<script lang="ts" setup>
import { add, Data, Format } from '@idmy/core'
import { BillDetailPopover } from '@mh-bcs/bill'
import { cloneDeep } from 'lodash-es'

const { data, merge } = defineProps({
  merge: { type: Boolean as PropType<boolean>, default: false },
  data: { type: Array as PropType<Data[]>, required: true },
  height: { type: Number as PropType<number>, default: 200 },
})

const highlightedId = inject<any>('highlightedId')
const isHighlighted = (row: Data) => (highlightedId.value === row.artId ? 'highlighted ' : undefined)
const key = (row: Data) => `${row.artId}${row.unit ?? 'null'}${row.price}`

const rows = computed(() => {
  let arr: [] = cloneDeep(data)
  if (merge) {
    const map = new Map()
    arr.forEach((row: Data) => {
      const k = key(row)
      const val = map.get(k)
      if (val) {
        val.total = add(val.total, row.total)
        val.price = add(val.price, row.price)
        val.amount = add(val.amount, row.amount)
      } else {
        map.set(k, row)
      }
    })
    arr = Array.from(map.values())
  }
  return arr.filter(row => row.total !== 0)
})
</script>

<template>
  <div :style="{ height: `${height}px`, maxHeight: `${height}px` }" class="oa">
    <div v-for="row in rows" :class="isHighlighted(row)" class="cd lh-22px" flex justify-between @mouseleave="highlightedId = null" @mouseover="highlightedId = row.artId">
      <div>
        <BillDetailPopover :data="row">
          {{ row.artName }}
        </BillDetailPopover>
      </div>
      <div>
        <Format :value="row.amount" type="Currency" />
      </div>
    </div>
  </div>
</template>
<style lang="less">
.highlighted {
  background-color: rgba(255, 255, 0, 0.3) !important;
}
</style>
