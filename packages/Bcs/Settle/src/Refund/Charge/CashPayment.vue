<script lang="ts" setup>
import { View } from '@idmy/antd'
import { add, clearObject, Data, Dialog, Format, Modal, useLoading } from '@idmy/core'
import { TransStatusHelp } from '@mh-bcs/help'
import { RefundButton } from '@mh-bcs/pay'
import { ChargeContext, chargeInjectKey, listAllPaymentsByCashId, refundCancel, refundInjectKey, refundPayment, settleUndo } from '@mh-bcs/util'
import { Checkbox, Table, Tag } from 'ant-design-vue'
import Safe from '../../Charge/Safe.vue'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})
const refundCtx = inject<any>(refundInjectKey)

const cps = ref<Data[]>([])

const [onLoad, loading] = useLoading(async () => {
  const payMap = new Map()
  try {
    const arr = await listAllPaymentsByCashId(ctx.blueCashId as number)
    const all: Data[] = []
    arr.forEach((row: Data) => {
      if (row.status !== 'CANCELED' && row.status !== 'ERR') {
        all.push(row)
        if (row.status === 'OK') {
          payMap.set(row.paymentType, { ...row, amount: add(payMap.get(row.paymentType)?.amount ?? 0, row.amount) })
        }
      }
    })
    cps.value = all
  } catch {
    cps.value = []
  }

  payMap.forEach((row: Data) => {
    if (!((row.amount == 0 && row.refundableAmount > 0 && row.paymentType === 'MI_FUND') || row.amount > 0)) {
      payMap.delete(row.paymentType)
    }
  })
  payMap.forEach((row: Data) => {
    row.amount = Math.min(row.amount, Math.abs(ctx.unpaidAmount ?? row.amount))
  })
  refundCtx.payMap.value = payMap
})

const showHistory = ref(true)
const payments = computed(() =>
  cps.value.filter(item => {
    if (!showHistory.value && item.cashId !== ctx.cashId) {
      return false
    }
    if (item.paymentType === 'MI_FUND' && item.status === 'UN') {
      if (!ctx.miTrans.miTransId) {
        return false
      }
    }
    return true
  })
)

ctx.miFundCashPayment = computed(() => payments.value.find((item: Data) => item.paymentType === 'MI_FUND' && item.status === 'UN'))

const [onRefund, refunding] = useLoading(async (row: Data, refresh = true) => {
  if (row.paymentType === 'MI_FUND') {
    if (row.status === 'OK') {
      try {
        await settleUndo(row.miTransId)
      } catch {
        await new Promise((resolve, reject) => {
          Modal.b.open({
            component: Safe,
            title: '医保错误是否忽略继续退费？',
            width: 2,
            onClose: (isOk: boolean) => {
              isOk ? resolve(isOk) : reject('取消')
            },
          })
        })
      }
    }
    clearObject(ctx.miTrans)
  }
  await refundPayment(ctx.cashId, row.cashId, row.lineNo, row.paymentType, Math.abs(row.amount ?? 0))
  refresh && (await ctx.onLoad())
})

ctx.onRefundPayment = onRefund

const onOk = () => ctx.onLoad()

refundCtx.paymentTotal = computed(() => {
  const group = {
    amount: 0,
    mi: 0,
    self: 0,
    currentMi: 0,
    currentCash: 0,
  }
  payments.value.forEach((row: Data) => {
    group.amount = add(group.amount, row.amount)
    if (row.paymentType === 'MI_FUND') {
      group.mi = add(group.mi, row.amount)
    } else {
      group.self = add(group.self, row.amount)
    }
    if (row.cashId === ctx.cashId) {
      if (row.paymentType === 'MI_FUND') {
        group.currentMi = add(group.currentMi, row.amount)
      } else if (row.paymentType === 'CASH') {
        group.currentCash = add(group.currentCash, row.amount)
      }
    }
  })
  return group
})

ctx.isFullAmtMiPay = computed(() => {
  if (refundCtx.paymentTotal.value.mi > 0) {
    if (refundCtx.paymentTotal.value.mi === ctx.totalAmount) {
      if (ctx.isZero) {
        return ctx.miFundCashPayment?.status === 'UN'
      }
    }
  }
  return false
})

let columns: Data[] = [
  {
    align: 'center',
    customRender: ({ index }: Data) => index + 1,
    dataIndex: 'no',
    title: '#',
    width: 45,
  },
  { align: 'center', dataIndex: 'type', title: '支付时期', width: 80 },
  { align: 'center', dataIndex: 'cashId', title: '结算流水', width: 80 },
  { align: 'center', dataIndex: 'status', title: '交易状态', width: 80 },
  { align: 'center', dataIndex: 'paymentMode', title: '支付方式', width: 110 },
  { align: 'right', dataIndex: 'amount', title: '支付金额', width: 90 },
  { align: 'left', dataIndex: 'notes', title: '备注', minWidth: 90 },
  { align: 'center', dataIndex: 'op', title: '操作', width: 70 },
]

if (cfg.setting?.partialRefundDisabled) {
  columns = [
    { align: 'center', dataIndex: 'cashId', title: '结算流水', width: 80 },
    { align: 'center', dataIndex: 'status', title: '交易状态', width: 80 },
    { align: 'center', dataIndex: 'paymentMode', title: '支付方式', width: 100, ellipsis: true },
    { align: 'right', dataIndex: 'amount', title: '支付金额', width: 90 },
    { align: 'left', dataIndex: 'notes', title: '备注', minWidth: 90 },
    { align: 'center', dataIndex: 'op', title: '操作', width: 70, fixed: 'right' },
  ]
}

let closable = false
watch([() => payments.value, () => ctx.isZero, () => ctx.finished, () => ctx.preSettleLoading], () => {
  if (ctx.finished) {
    closable = false
  } else if (ctx.isZero) {
    closable = false
  } else if (ctx.preSettleLoading) {
    closable = false
  } else {
    closable = !Boolean(payments.value.find(row => row.cashId === ctx.cashId))
  }
})

Modal.setClose(async () => {
  if (closable) {
    try {
      await Dialog.confirm({
        title: '是否取消本次结算？',
        content: '取消后下次结算将会重新计算结算金额。',
        cancelText: '取消重结',
        okText: '保留稍后处理',
      })
      await Modal.close()
    } catch {
      await refundCancel(ctx.cashId)
    }
  } else {
    await Modal.close()
  }
})

defineExpose({
  onLoad,
})
</script>
<template>
  <View right-class="f1" title="全部交易记录（含收费和退费）">
    <template v-if="!cfg.setting?.partialRefundDisabled" #left>
      <Checkbox v-model:checked="showHistory">显示历史支付</Checkbox>
    </template>
    <Table
      :bordered="true"
      :columns="columns"
      :dataSource="payments"
      :loading="loading"
      :minHeight="130"
      :pagination="false"
      :rowHeight="30"
      :rowKey="row => `${row.cashId}${row.lineNo}`"
      size="small"
    >
      <template #emptyText>
        <div class="h-40px" flex items-center>暂无数据</div>
      </template>
      <template #headerCell="{ column }">
        <TransStatusHelp v-if="column.dataIndex === 'status'" />
      </template>
      <template #bodyCell="{ column: { dataIndex }, record: row }">
        <template v-if="dataIndex === 'type'">
          <template v-if="row.cashId === ctx.cashId">本次支付</template>
          <template v-else>历史支付</template>
        </template>
        <template v-if="dataIndex === 'amount'">
          <Format :value="row.amount" type="Currency" />
        </template>
        <template v-if="dataIndex === 'status'">
          <Tag v-if="row.paymentType === 'MI_FUND' && row.status === 'UN'" class="dark-orange"> 预结算 </Tag>
          <Format v-else :bordered="false" :component="Tag" :value="row.status" params="TransStatus" type="Enum" />
        </template>
        <template v-if="dataIndex === 'op' && row.cashId === ctx.cashId">
          <template v-if="!(row.status === 'OK' && ['MI_FUND', 'EPP'].includes(row.paymentType) && row.amount <= 0)">
            <RefundButton v-if="!ctx.finished" :api="() => onRefund(row)" :disabled="ctx.finishing || refunding" ghost size="small" tip="确认要操作吗？" @ok="onOk">
              {{ row.paymentType === 'MI_FUND' && row.status === 'UN' ? '删除' : '退费' }}
            </RefundButton>
          </template>
        </template>
      </template>
    </Table>
  </View>
</template>
