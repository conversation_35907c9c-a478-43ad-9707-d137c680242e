<script lang="ts" setup>
import { Data, Format, Message, useLoading } from '@idmy/core'
import { ChargeContext, chargeInjectKey, getInvoiceByCashId, getPrevCashId, red } from '@mh-bcs/util'
import { Button, Popover, Space, Tag } from 'ant-design-vue'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})

const invoice = ref<Data>({})
const [onLoadInvoice] = useLoading(async () => {
  const cashId = await getPrevCashId(ctx.cashId)
  if (cashId) {
    invoice.value = await getInvoiceByCashId(cashId as number)
  }
}, true)

const [onRed, redLoading] = useLoading(async () => {
  await red(invoice.value.invId, '退费红冲')
  Message.success('发票红冲成功')
  await onLoadInvoice()
})
</script>

<template>
  <Space size="large">
    <Popover v-if="invoice?.invId" :mouseLeaveDelay="0" placement="left">
      <template #title>
        <div font-normal>
          <div>红冲原发票或上一次退费产生的发票</div>
          <div>发票流水: {{ invoice.invId }}</div>
          <div>结算流水: {{ invoice.cashId }}</div>
          <div v-if="invoice.invCode">发票代码: {{ invoice.invCode }}</div>
          <div v-if="invoice.invNo">发票号码: {{ invoice.invNo }}</div>
          <div v-if="invoice.checkCode">校验码: {{ invoice.checkCode }}</div>
          <Format v-if="invoice.amount" :value="invoice.amount" prefix="发票金额：" type="Currency" />
          <Tag v-if="invoice.droppedFlag" color="red" ml-8px>已红冲</Tag>
        </div>
      </template>
      <Button :disabled="invoice.droppedFlag" :loading="redLoading" type="primary" @click="onRed()">发票红冲</Button>
    </Popover>
    <slot :invoice="invoice" />
  </Space>
</template>
