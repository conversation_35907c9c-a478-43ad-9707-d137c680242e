<script lang="ts" setup>
import { Data, useLoading } from '@idmy/core'
import { ChargeContext, chargeInjectKey, createInvoice, getInvoiceByCashId, getPrevCashId, hasPrintTemplate, printRefund, red, refundFinish, refundInjectKey } from '@mh-bcs/util'
import { Button } from 'ant-design-vue'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})
const refundCtx = inject<any>(refundInjectKey)

const invoice = ref<Data>({})
const [onLoadInvoice] = useLoading(async () => {
  const cashId = await getPrevCashId(ctx.cashId)
  if (cashId) {
    invoice.value = await getInvoiceByCashId(cashId as number)
  }
}, true)

const [onRed] = useLoading(async () => {
  await red(invoice.value.invId, '退费红冲')
  await onLoadInvoice()
})

const showPrintBill = ref(false)
hasPrintTemplate(`outpatient:refund`).then((has: boolean) => {
  showPrintBill.value = has
})

const [onFinish] = useLoading(async () => {
  ctx.finishing = true
  await refundCtx.onFullRefund()
  await ctx.onLoad()
  if (!invoice.value?.invId) {
    return
  } else if (!invoice.value.droppedFlag) {
    await onRed()
  }
  try {
    if (ctx.isZero) {
      if (ctx.finished === false && ctx.isFullAmtMiPay === false && ctx.paying === false) {
        const inv = await refundFinish(ctx.cashId)
        await ctx.onLoad()
        if (inv && ctx.totalAmount !== 0) {
          await createInvoice(inv.cashId)
        }
        if (showPrintBill.value) {
          printRefund(ctx.cashId, ctx.cashType)
        }
        await refundCtx.onFinish(ctx.cashId)
      }
    }
  } finally {
    ctx.finishing = false
  }
})
</script>

<template>
  <Button :disabled="ctx.finished || ctx.isFullAmtMiPay || ctx.finishing" :loading="ctx.finishing" type="primary" @click="onFinish()"> 完成退费 </Button>
</template>
