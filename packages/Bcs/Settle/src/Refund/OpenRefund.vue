<script lang="ts" setup>
import { cfg, Data } from '@idmy/core'
import Refund from './index.vue'

const { data } = defineProps({
  data: { type: Object as PropType<Data>, required: true },
  component: { type: String, default: 'a' },
})

const emit = defineEmits(['ok'])

const [onRefund] = useLoading(async () => {
  Modal.open({
    component: Refund,
    onClose: () => {
      emit('ok')
    },
    props: { redCash: data },
    title: `退费#${data.cashId}【${data.payer}】`,
    width: cfg.setting?.partialRefundDisabled ? 4 : 5,
  })
})
</script>

<template>
  <component :is="component" v-bind="$attrs" v-if="data.status === 'ING' && data.actionType === 'RED'" @click="onRefund">继续退费</component>
</template>
