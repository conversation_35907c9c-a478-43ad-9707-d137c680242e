<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { Bar<PERSON>hart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必要的组件
use([
  <PERSON>vas<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

interface Props {
  data: {
    insurance: number
    pooledFund: number
    personalAccount: number
    mutualAid: number
    selfPay: number
  }
}

const props = defineProps<Props>()

// 计算图表配置
const chartOption = computed(() => {
  // 计算累积值用于瀑布图
  const pooledFund = props.data.pooledFund
  const personalAccount = pooledFund + props.data.personalAccount
  const mutualAid = personalAccount + props.data.mutualAid
  const totalAmount = mutualAid + props.data.selfPay // 总费用 = 医保 + 自费

  return {
    title: {
      text: '费用构成分析',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const data = params[0]
        return `${data.name}: ¥${Math.abs(data.value).toLocaleString()}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['统筹', '个账', '共济', '医保小计', '自费', '总费用'],
      axisLabel: {
        fontSize: 12,
        interval: 0,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '金额(元)',
      axisLabel: {
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万'
          }
          return value.toLocaleString()
        }
      }
    },
    series: [
      {
        name: '费用构成',
        type: 'bar',
        stack: 'waterfall',
        data: [
          {
            value: props.data.pooledFund,
            itemStyle: { color: '#52c41a' },
            label: {
              show: true,
              position: 'top',
              formatter: () => props.data.pooledFund > 0 ? `¥${props.data.pooledFund.toLocaleString()}` : ''
            }
          },
          {
            value: props.data.personalAccount,
            itemStyle: { color: '#faad14' },
            label: {
              show: true,
              position: 'top',
              formatter: () => props.data.personalAccount > 0 ? `¥${props.data.personalAccount.toLocaleString()}` : ''
            }
          },
          {
            value: props.data.mutualAid,
            itemStyle: { color: '#722ed1' },
            label: {
              show: true,
              position: 'top',
              formatter: () => props.data.mutualAid > 0 ? `¥${props.data.mutualAid.toLocaleString()}` : ''
            }
          },
          {
            value: props.data.insurance,
            itemStyle: { color: '#1890ff' },
            label: {
              show: true,
              position: 'top',
              formatter: () => `¥${props.data.insurance.toLocaleString()}`,
              fontWeight: 'bold'
            }
          },
          {
            value: props.data.selfPay,
            itemStyle: { color: '#f5222d' },
            label: {
              show: true,
              position: 'top',
              formatter: () => `¥${props.data.selfPay.toLocaleString()}`
            }
          },
          {
            value: totalAmount,
            itemStyle: { color: '#13c2c2' },
            label: {
              show: true,
              position: 'top',
              formatter: () => `¥${totalAmount.toLocaleString()}`,
              fontWeight: 'bold'
            }
          }
        ],
        barWidth: '60%'
      },
      // 辅助系列用于创建瀑布效果
      {
        name: '辅助',
        type: 'bar',
        stack: 'waterfall',
        itemStyle: {
          color: 'transparent'
        },
        data: [
          0, // 统筹从0开始
          pooledFund, // 个账从统筹结束位置开始
          personalAccount, // 共济从个账结束位置开始
          0, // 医保小计从0开始显示总医保金额
          mutualAid, // 自费从医保结束位置开始
          0 // 总费用从0开始显示总金额
        ]
      }
    ]
  }
})
</script>

<template>
  <VChart :option="chartOption" />
</template>
