<script lang="ts" setup>
import { ModalButton } from '@idmy/antd'
import { Modal, useLoading } from '@idmy/core'
import type { CashType } from '@mh-bcs/util'
import { payCancel } from '@mh-bcs/util'
import { Checkbox, Form, FormItem, Input, Radio, RadioGroup } from 'ant-design-vue'

const { cashId, cashType } = defineProps({
  cashId: { type: Number, required: true },
  cashType: { type: String as PropType<CashType>, required: true },
})

const notesTypes: any[] = [
  { value: 1, label: '选错了' },
  { value: 2, label: '医保报销太少' },
  { value: 3, label: '患者不做了' },
  { value: 4, label: '没药了' },
  { value: 5, label: '结算异常' },
  { value: 6, label: '退号' },
  { value: 0, label: '其他原因' },
]

const cancelBill = ref(false)

const type = ref<number>(1)
watch(
  () => type.value,
  (val: number) => {
    cancelBill.value = val === 2 || val === 3 || val === 4
  }
)

const other = ref()
const [onOk, loading] = useLoading(async () => {
  let notes: string = ''
  if (type.value === 0) {
    notes = other.value
  } else {
    notes = notesTypes.find(item => item.value === type.value).label as string
  }
  await payCancel(cashId, notes, cancelBill.value, cashType)
  await Modal.b.ok()
})
</script>

<template>
  <Form layout="horizontal">
    <FormItem>
      <RadioGroup v-model:value="type" class="w-100%">
        <Radio v-for="row in notesTypes" :value="row.value">
          {{ row.label }}
        </Radio>
      </RadioGroup>
    </FormItem>
    <FormItem v-if="type === 0">
      <Input v-model:value="other" class="w-100%" placeholder="其他原因" />
    </FormItem>
    <FormItem />
    <FormItem class="mt-16px!">
      <div flex-between>
        <Checkbox v-model:checked="cancelBill">
          <span class="error">作废划价单并释放占用库存</span>
        </Checkbox>
        <ModalButton :loading="loading" ok-text="确认" @ok="onOk()" />
      </div>
    </FormItem>
  </Form>
</template>
