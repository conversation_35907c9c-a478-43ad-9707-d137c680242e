<script lang="ts" setup>
import { Format, useLoading } from '@idmy/core'
import { ChargeContext, chargeInjectKey, getPrepaidBalanceByVisitId } from '@mh-bcs/util'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})

const [onLoad] = useLoading(
  async () => {
    if (ctx.cashType === 'INPATIENT') {
      const { balance } = await getPrepaidBalanceByVisitId(ctx.visitId as number)
      ctx.prepaid = balance
    }
  },
  true,
  0
)

defineExpose({
  onLoad,
})
</script>
<template>
  <Format v-if="ctx.prepaid > 0" :value="ctx.prepaid" prefix="预交金：" type="Currency" value-class="b" />
</template>
