<script lang="ts" setup>
import { ModalButton } from '@idmy/antd'
import { Format, Modal, useLoading } from '@idmy/core'
import { getPrepaidBalanceByVisitId, prepaidUncharged } from '@mh-bcs/util'
import { Radio, RadioGroup } from 'ant-design-vue'

const { visitId } = defineProps({
  visitId: { type: Number, required: true },
})

const state = ref({
  amt: 0,
  mode: 'CASH',
})

const prepaidBalance = ref(0)
useLoading(async () => {
  const { balance } = await getPrepaidBalanceByVisitId(visitId)
  prepaidBalance.value = balance
}, true)

const [onOk, okLoading] = useLoading(async () => {
  await prepaidUncharged(visitId, prepaidBalance.value)
  await Modal.b.ok()
})
</script>

<template>
  <Format :value="prepaidBalance ?? 0" component="div" prefix="预交余额：" suffix=" (应退给患者的金额)" type="Currency" value-class="error fs20 b" />
  <div class="mt-16px">
    退款方式：
    <RadioGroup v-model:value="state.mode">
      <Radio :disabled="true" value="return"> 原路返回 </Radio>
      <Radio value="cash"> 现金 </Radio>
    </RadioGroup>
  </div>
  <ModalButton v-if="prepaidBalance > 0" :loading="okLoading" cancelText="保留预交金" class="mt-16px" okText="确认退款" @ok="onOk" />
</template>

<style lang="less" scoped></style>
