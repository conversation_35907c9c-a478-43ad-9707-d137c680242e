<script lang="ts" setup>
import { Dict, View } from '@idmy/antd'
import { add, Api, Data, Message, useCache, useLoading } from '@idmy/core'
import { Currents } from '@mh-base/core'
import {
  CashTransStatusEnum,
  ChargeContext,
  chargeInjectKey,
  getVisitInfo,
  inHospitalFeeUndo,
  inHospitalFeeUpload,
  inHospitalPreSettle,
  inHospitalSettle,
  MiTransStatusEnum,
  outHospitalRegister,
  outHospitalUnregister,
  pay,
  updateMiStatus,
} from '@mh-bcs/util'
import { Button, Checkbox, Dropdown, Menu, MenuItem, Space, Tag } from 'ant-design-vue'
import { isNil } from 'lodash-es'
import MiTransAmt from './MiTransAmt.vue'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})

const autoDischarged = useCache('autoDischarged', true, -1, { global: true, never: true })

const model = reactive({
  acctUsedFlag: Currents.tenantId === 360103001,
  visitId: ctx.visitId,
  cashId: ctx.cashId,
  cashType: ctx.cashType,
  insuranceTypeId: ctx.insuranceTypeId,
  medTypeId: ctx.medTypeId ?? 21,
  diseaseCode: ctx.diseaseCode,
  insuplcAdmdvs: undefined,
  psnSetlway: 1,
})

const card = reactive(ctx.card ?? {})

const canPreSettle = computed(() => {
  if (settleLoading.value || isNil(model.insuranceTypeId) || isNil(card.readCard?.psn_cert_no)) {
    return false
  } else {
    return isNil(ctx.miTrans.miTransId)
  }
})

const canSettle = computed(() => {
  if (preSettleLoading.value || isNil(ctx.miTrans?.miTransId)) {
    return false
  } else if (!ctx.isRefund && !(ctx.prepaid && ctx.unpaidAmount <= 0) && ctx.isZero && !ctx.isFullAmtMiPay) {
    return false
  } else {
    return ctx.miFundCashPayment?.status === CashTransStatusEnum.waiting && ctx.miTrans.transStatus > 0
  }
})

const [onSettle, settleLoading] = useLoading(async (isLoad = false) => {
  if (!canSettle.value) {
    return
  }
  Object.assign(
    ctx.miTrans,
    await inHospitalSettle({
      card,
      psnSetlway: model.psnSetlway,
      cashTransId: ctx.miFundCashPayment.transId,
      mdtrt_cert_no: card.params.mdtrt_cert_no,
      mdtrt_cert_type: card.params.mdtrt_cert_type,
    })
  )

  if (ctx.miFundCashPayment.status === CashTransStatusEnum.waiting && ctx.miTrans.transStatus === MiTransStatusEnum.settled) {
    await pay({
      cashId: ctx.cashId,
      payAmount: add(ctx.miTrans.fundPayAmt, ctx.miTrans.acctPayAmt, ctx.miTrans.multiAidAmt),
      miFundAmt: ctx.miTrans.fundPayAmt,
      miAcctAmt: ctx.miTrans.acctPayAmt,
      miMultiAidAmt: ctx.miTrans.multiAidAmt,
      paymentType: 'miFund',
    })
  }
  isLoad && (await ctx.onLoad())
})

ctx.miOnSettle = onSettle

const [onPreSettle, preSettleLoading] = useLoading(async () => {
  const { transId: cashTransId } = await pay({
    cashId: ctx.cashId,
    isPrePay: true,
    payAmount: 0,
    paymentType: 'miFund',
  })
  try {
    Object.assign(
      ctx.miTrans,
      await inHospitalPreSettle({
        acctUsedFlag: model.acctUsedFlag,
        psnSetlway: model.psnSetlway,
        visitId: model.visitId,
        medTypeId: model.medTypeId,
        insuranceTypeId: model.insuranceTypeId,
        insuplcAdmdvs: model.insuplcAdmdvs,
        mdtrt_cert_no: card.params.mdtrt_cert_no,
        mdtrt_cert_type: card.params.mdtrt_cert_type,
        cashTransId,
        psn_no: card.miData?.baseinfo?.psn_no,
        card,
      })
    )
    ctx.uploadFailedList = ctx.miTrans.upload_failed_list
  } catch (e) {
    console.error(e)
    return await ctx.onLoad()
  }

  if (!ctx.miTrans.miTransId) {
    Message.error('未返回医保交易ID')
    return await ctx.onLoad()
  }

  await updateMiStatus({
    miAcctAmt: ctx.miTrans.acctPayAmt ?? 0,
    miFundAmt: ctx.miTrans.fundPayAmt ?? 0,
    familyAcctAmt: ctx.miTrans.multiAidAmt ?? 0,
    miTransId: ctx.miTrans.miTransId,
    transId: cashTransId,
  })
  await ctx.onLoad()
  if (ctx.isRefund) {
    preSettleLoading.value = false
    await onSettle()
    await ctx.onLoad()
  }
})
ctx.preSettleLoading = preSettleLoading

const loaded = ({ output }: Data) => {
  ctx.visit = output
}

//region 读卡
const cardParams = computed(() => ({
  business_type_id: '104',
  medical_type: model.medTypeId,
  org_id: Currents.tenantId,
  user_id: Currents.id,
  visit_id: ctx.visitId,
}))

const onRead = async (val: any) => {
  if (val.miData?.baseinfo?.psn_cert_type && !val.readCard?.psn_cert_type) {
    val.readCard.psn_cert_type = val.miData.baseinfo?.psn_cert_type
    val.readCard.psn_cert_no = val.miData.baseinfo?.certno
    val.readCard.psn_no = val.miData.baseinfo?.psn_no
    val.readCard.psn_name = val.miData.baseinfo?.psn_name
    val.readCard.psn_type = val.miData.baseinfo?.psn_type
  }
  Object.assign(card, val)
}
//endregion

const loading = ref(false)
const [onOutHospitalRegister] = useLoading(async () => {
  loading.value = true
  try {
    await outHospitalRegister({
      card: ctx.card,
      visitId: ctx.visitId,
    })
    Message.success('医保出院登记成功')
  } finally {
    loading.value = false
  }
})

const [onOutHospitalUnregister] = useLoading(async () => {
  loading.value = true
  try {
    await outHospitalUnregister({
      card: ctx.card,
      visitId: ctx.visitId,
    })
    Message.success('撤销出院登记成功')
  } finally {
    loading.value = false
  }
})

const [onInHospitalFeeUpload] = useLoading(async () => {
  loading.value = true
  try {
    await inHospitalFeeUpload({
      visitId: ctx.visitId,
      cashId: ctx.cashId,
      mdtrt_cert_type: card.params?.mdtrt_cert_type,
      mdtrt_cert_no: card.params?.mdtrt_cert_no,
      card,
    })
    Message.success('费用明细上传成功')
  } finally {
    loading.value = false
  }
})

const [onInHospitalFeeUndo] = useLoading(async () => {
  loading.value = true
  try {
    await inHospitalFeeUndo({
      visitId: ctx.visitId,
      cashId: ctx.cashId,
      mdtrt_cert_type: card.params?.mdtrt_cert_type,
      mdtrt_cert_no: card.params?.mdtrt_cert_no,
      card,
    })
    Message.success('费用明细撤销成功')
  } finally {
    loading.value = false
  }
})

const [onInHospitalFeeRepeat] = useLoading(async () => {
  loading.value = true
  try {
    await onInHospitalFeeUndo()
    await onInHospitalFeeUpload()
    Message.success('费用重传成功')
  } finally {
    loading.value = false
  }
})
</script>
<template>
  <Api v-slot="{ output: visit }: Data" :load="() => getVisitInfo(ctx.visitId)" first type="Object" @load="loaded">
    <div flex items-end justify-between>
      <div class="f1">
        <View :indicator="false" :loading="ctx.isZero && !ctx.isFullAmtMiPay" loading-area="content" right-class="f1" title="医保支付">
          <div>
            诊断：
            <Tag v-for="item in visit.visitDiagList" class="font-bold text-16px error" color="red">
              {{ item.diagName }}
            </Tag>
          </div>
          <div flex items-center mt-8px>
            <BaseReadIndentity :isMIOpenSelect="false" :params="cardParams" type="inline" @success="onRead" />
            <MedicalInsurance v-model="model" :data="card.miData?.insuinfo" :miTransStatus="ctx.miTrans.transStatus" :patientId="visit.patientId" />
          </div>
          <template #left>
            <MiTransAmt />
          </template>
          <template #right>
            <Space>
              <Checkbox v-if="!ctx.midway" v-model:checked="autoDischarged"> 自动出院 </Checkbox>
              <Dropdown>
                <Button :loading="loading" size="small" type="primary"> 医保操作 </Button>
                <template #overlay>
                  <Menu>
                    <MenuItem :disabled="loading" @click="onOutHospitalUnregister()"> 医保出院撤销 </MenuItem>
                    <MenuItem :disabled="loading" @click="onOutHospitalRegister()"> 医保出院登记 </MenuItem>
                    <MenuItem :disabled="loading" @click="onInHospitalFeeUpload"> 医保费用上传(本次结算) </MenuItem>
                    <MenuItem :disabled="loading" @click="onInHospitalFeeUndo"> 医保费用撤销 </MenuItem>
                    <MenuItem :disabled="loading" @click="onInHospitalFeeRepeat"> 医保费用重传(本次结算) </MenuItem>
                  </Menu>
                </template>
              </Dropdown>
              <Dict v-model="model.psnSetlway" :clearable="false" class="w-120px!" clazz="MiPayMethod" size="small" />
              <Checkbox v-model:checked="model.acctUsedFlag">使用个人账户</Checkbox>
            </Space>
          </template>
        </View>
      </div>
      <div pl-8px w-64px>
        <Button :disabled="!canPreSettle" :loading="preSettleLoading" h-60px mb-4px type="primary" @click="onPreSettle()">预结 </Button>
        <Button :disabled="!canSettle" :loading="settleLoading" type="primary" @click="onSettle(true)">结算 </Button>
      </div>
    </div>
  </Api>
</template>
