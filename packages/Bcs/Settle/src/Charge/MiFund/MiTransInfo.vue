<script lang="ts" setup>
import { View } from '@idmy/antd'
import { Format } from '@idmy/core'
import { ChargeContext, chargeInjectKey } from '@mh-bcs/util'
import { Descriptions, DescriptionsItem } from 'ant-design-vue'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})
</script>

<template>
  <View title="医保信息">
    <Descriptions :column="5" bordered size="small">
      <DescriptionsItem :contentStyle="{ width: '160px' }" label="医疗总费用">
        <Format :value="ctx.miTrans.amount" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem :contentStyle="{ width: '160px' }" label="医保统筹金额">
        <Format :value="ctx.miTrans.fundPayAmt" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem :contentStyle="{ width: '160px' }" label="个人账户金额">
        <Format :value="ctx.miTrans.acctPayAmt" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem :contentStyle="{ width: '160px' }" label="个人账户余额">
        <Format :value="ctx.miTrans.acctBalance" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem :contentStyle="{ width: '160px' }" label="个账共济支付">
        <Format :value="ctx.miTrans.multiAidAmt" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem label="个人负担总额">
        <Format :value="ctx.miTrans.patientPartAmt" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem label="医院负担总额">
        <Format :value="ctx.miTrans.hospitalPartAmt" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem label="现金支付金额">
        <Format :value="ctx.miTrans.cashPayAmt" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem label="先行支付金额">
        <Format :value="ctx.miTrans.preSelfpayAmt" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem label="超限价自费金额">
        <Format :value="ctx.miTrans.overlimitAmt" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem label="实际支付起付线">
        <Format :value="ctx.miTrans.payLineAmt" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem label="符合政策范围">
        <Format :value="ctx.miTrans.inScopeAmt" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem label="非医疗金额">
        <Format :value="ctx.miTrans.nonNedicalAmt" type="Currency" />
      </DescriptionsItem>
      <DescriptionsItem label="结算起始日期">
        <Format :value="String(ctx.miTrans.periodStart)" params="MM-DD" type="Date" />
      </DescriptionsItem>
      <DescriptionsItem label="结算截至日期">
        <Format :value="String(ctx.miTrans.periodEnd)" params="MM-DD" type="Date" />
      </DescriptionsItem>
    </Descriptions>
  </View>
</template>
