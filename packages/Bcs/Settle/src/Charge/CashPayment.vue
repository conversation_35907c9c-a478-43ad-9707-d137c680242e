<script lang="ts" setup>
import { View } from '@idmy/antd'
import { add, clearObject, Data, Format, Modal, useLoading } from '@idmy/core'
import { TransStatusHelp } from '@mh-bcs/help'
import { RefundButton } from '@mh-bcs/pay'
import { CashTransStatusEnum, ChargeContext, chargeInjectKey, listFullPaymentsByCashId, refundPayment, settleUndo } from '@mh-bcs/util'
import { Space, Table, Tag } from 'ant-design-vue'
import Safe from './Safe.vue'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})

const cps = ref<any>([])
const [onLoad, loading] = useLoading(async () => {
  try {
    const arr = await listFullPaymentsByCashId(ctx.cashId)
    cps.value = arr.filter((row: Data): boolean => {
      if (row.status === 'CANCELED' || row.status === 'ERR') {
        return false
      } else if (row.status !== 'ING' && row.amount > 0 && row.amount !== row.refundedAmount) {
        return true
      } else if (row.paymentType === 'MI_FUND' && row.amount === 0 && row.status !== 'ING') {
        // 医保预结算
        return true
      } else {
        return row.status === 'ING'
      }
    })
  } catch {
    cps.value = []
  }
})

const payments = computed(() =>
  cps.value.filter(item => {
    if (item.paymentType === 'MI_FUND' && item.status === 'UN') {
      if (!ctx.miTrans.miTransId) {
        return false
      }
    }
    return true
  })
)

ctx.miFundCashPayment = computed(() => payments.value.find(item => item.paymentType === 'MI_FUND' && item.status === 'UN'))

watch(
  [() => payments.value, () => ctx.isZero, () => ctx.finished, () => ctx.preSettleLoading],
  () => {
    if (ctx.finished) {
      Modal.setClosable(true)
    } else if (ctx.isZero && ctx.prepaid <= 0) {
      Modal.setClosable(false)
    } else if (payments.value.length) {
      Modal.setClosable(false)
    } else if (ctx.preSettleLoading) {
      Modal.setClosable(false)
    } else {
      Modal.setClosable(true)
    }
  },
  { immediate: true }
)

const [onRefund] = useLoading(async (row: Data) => {
  if (row.paymentType === 'MI_FUND') {
    if (row.status === 'OK') {
      try {
        await settleUndo(row.miTransId)
      } catch {
        await new Promise((resolve, reject) => {
          Modal.b.open({
            component: Safe,
            title: '医保错误是否忽略继续退费？',
            width: 2,
            onClose: (isOk: boolean) => {
              isOk ? resolve(isOk) : reject('取消')
            },
          })
        })
      }
    }
    clearObject(ctx.miTrans)
  }
  await refundPayment(row.cashId, row.cashId, row.lineNo, row.paymentType, row.amount)
  await ctx.onLoad()
})

const onOk = () => ctx.onLoad()

const paymentTotal = computed(() => {
  const group = {
    total: 0,
    mi: 0,
    self: 0,
  }
  payments.value.forEach((row: Data) => {
    group.total = add(group.total, row.amount)
    if (row.paymentType === 'MI_FUND') {
      group.mi = add(group.mi, row.amount)
    } else {
      group.self = add(group.self, row.amount)
    }
  })
  return group
})

ctx.isFullAmtMiPay = computed(() => {
  if (paymentTotal.value.mi > 0) {
    if (paymentTotal.value.mi === ctx.totalAmount) {
      if (ctx.isZero) {
        return ctx.miFundCashPayment?.status === CashTransStatusEnum.waiting
      }
    }
  }
  return false
})

const columns = [
  { align: 'center', dataIndex: 'status', title: '交易状态', width: 90 },
  { align: 'center', dataIndex: 'paymentType', title: '支付方式', width: 120 },
  { align: 'right', dataIndex: 'amount', title: '支付金额', width: 120 },
  { align: 'center', dataIndex: 'transId', title: '交易流水', width: 90 },
  { align: 'center', dataIndex: 'op', title: '操作', width: 70 },
]
defineExpose({
  onLoad,
})
</script>
<template>
  <View right-class="f1" title="已支付资金">
    <template #left>
      <Space>
        <Format :value="paymentTotal.total" prefix="总额：" type="Currency" value-class="text-16px font-bold primary" />
        <Format :value="paymentTotal.mi" prefix="医保：" type="Currency" value-class="text-16px font-bold primary" />
        <Format :value="paymentTotal.self" prefix="自费：" type="Currency" value-class="text-16px font-bold primary" />
      </Space>
    </template>
    <Table :columns="columns" :dataSource="payments" :height="200" :loading="loading" :pagination="false" bordered rowKey="lineNo" size="small">
      <template #emptyText>
        <div flex h-40px items-center justify-center>暂无数据</div>
      </template>
      <template #headerCell="{ column }">
        <TransStatusHelp v-if="column.dataIndex === 'status'" />
      </template>
      <template #bodyCell="{ column: col, record: row }">
        <Format v-if="col.dataIndex === 'paymentType'" :value="row.paymentType" type="Enum" params="PaymentType" />
        <Format v-if="col.dataIndex === 'amount'" :value="row.amount" type="Currency" />
        <template v-if="col.dataIndex === 'status'">
          <Tag v-if="row.paymentType === 'MI_FUND' && row.status === 'UN'" class="dark-orange"> 预结算 </Tag>
          <Format v-else :bordered="false" :component="Tag" :value="row.status" params="TransStatus" type="Enum" />
        </template>
        <template v-if="col.dataIndex === 'op'">
          <RefundButton v-if="!ctx.finished" :api="() => onRefund(row)" :disabled="ctx.finishing" ghost size="small" tip="确认要退费吗？（金额将原路返回）" @ok="onOk">
            {{ row.paymentType === 'MI_FUND' && row.status === 'UN' ? '删除' : '退费' }}
          </RefundButton>
        </template>
      </template>
    </Table>
  </View>
</template>
