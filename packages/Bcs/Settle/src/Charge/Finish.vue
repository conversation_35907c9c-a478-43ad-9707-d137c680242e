<script lang="ts" setup>
import { getCache, Modal, sleep, useLoading } from '@idmy/core'
import { ChargeContext, chargeInjectKey, createInvoice, openPrepaidRefund, payOk, printCash } from '@mh-bcs/util'
import { Button } from 'ant-design-vue'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})

defineProps({
  loaded: { type: Boolean, required: true },
})

const canFinish = computed(() => ctx.isZero || (ctx.prepaid && ctx.unpaidAmount < 0))

const [onFinish, finishing] = useLoading(async () => {
  if (ctx.prepaid && ctx.unpaidAmount <= 0) {
    await ctx.miOnSettle()
    await ctx.onLoad()
    await sleep(500)
  }
  if (canFinish.value && ctx.finished === false && ctx.isFullAmtMiPay === false && ctx.paying === false) {
    await payOk(ctx.cashId)
    await ctx.onLoad()
    Modal.setClosable(true)
    if (getCache('autoDischarged', false, { global: true }) || ctx.cashType !== 'INPATIENT') {
      ctx.totalAmount && (await createInvoice(ctx.cashId))
      printCash(ctx.cashId, ctx.cashType)
    }
    await nextTick()
    ctx.prepaid && (await openPrepaidRefund(ctx.visitId))
    await Modal.ok(ctx.cashId)
  }
})
ctx.finishing = finishing

if (ctx.cashType !== 'INPATIENT') {
  watch([() => ctx.isZero, () => ctx.isFullAmtMiPay], () => {
    ctx.autoFinish && nextTick(() => setTimeout(() => onFinish(), 400))
  })
}
</script>

<template>
  <Button v-if="canFinish" :disabled="ctx.finished || ctx.isFullAmtMiPay || ctx.finishing || !loaded" :loading="ctx.finishing" type="primary" @click="onFinish()"> 完成结算 </Button>
</template>
