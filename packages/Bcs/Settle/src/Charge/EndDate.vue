<script lang="ts" setup>
import { DatePicker } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'

const { startDate } = defineProps({
  startDate: { type: String, required: true },
})

const endDate = defineModel('endDate', { type: String })

const disabledDate = (current: Dayjs) => current < dayjs(startDate).subtract(1, 'day').endOf('day') || current > dayjs()

endDate.value = dayjs().format('YYYYMMDD')
</script>

<template>
  <DatePicker v-model:value="endDate" :disabled-date="disabledDate" placeholder="费用截止日期(含)" value-format="YYYYMMDD" />
</template>
