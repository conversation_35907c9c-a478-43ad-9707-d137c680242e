<script lang="ts" setup>
const opened = ref(false)
import { Collapse, CollapsePanel, Modal, Tag } from 'ant-design-vue'
</script>

<template>
  <span class="cp" @click="opened = !opened">交易状态<a>?</a></span>
  <Modal v-model:open="opened" :footer="null" destroy-on-close title="交易状态说明" width="800px">
    <div class="trnas-status-help">
      <div>
        <Tag>未交易</Tag>
        此状态表示没有任何支付行为发生。可能是患者还未到达支付环节，或者未在系统中登记支付请求。
      </div>
      <div>
        <Tag :bordered="false" class="bgc-dark-orange">交易中</Tag>
        表明支付流程已经启动，但尚未完成。交易可能正在进行验证或者等待第三方支付服务提供商的确认。
      </div>
      <div>
        <Tag :bordered="false" class="bgc-error">已失败</Tag>
        此状态意味着交易未能完成。以下是一些可能的原因：
        <ul>
          <li>患者提供的支付信息有误（如错误的卡号、密码、过期日期、二维码失效等）。</li>
          <li>交易发起时，患者的支付账户余额不足。</li>
          <li>系统错误或网络连接问题导致交易中断。</li>
          <li>第三方支付服务提供商拒绝了交易。</li>
        </ul>
        <p class="ml-20px">在遇到【已失败】您可能需要指导患者检查支付信息或尝试使用其他支付方式。</p>
      </div>
      <div>
        <Tag :bordered="false" class="bgc-success">已成功</Tag>
        交易已经顺利完成，资金已经从患者的账户中扣除，并且医院的账户已经收到相应的款项。
      </div>
      <div>
        <Tag :bordered="false" class="bgc-gray">已撤销</Tag>
        此状态指的是一个先前已成功的交易被取消。这可能是因为患者请求退款，或者发现交易存在错误（如重复收费或错误的收费金额）。
      </div>
      <Collapse>
        <CollapsePanel header="操作指南">
          <div>
            <div>
              <Tag>未交易</Tag>
              状态下，请确认是否有待处理的支付请求。
            </div>
            <div>
              <Tag :bordered="false" class="bgc-dark-orange">交易中</Tag>
              状态下，请监控交易进展，如果长时间未改变状态，可能需要联系技术支持。
            </div>
            <div>
              <Tag :bordered="false" class="bgc-error">已失败</Tag>
              状态下，您可能需要与患者沟通，以便解决支付问题并重新发起交易。
            </div>
            <div>
              <Tag :bordered="false" class="bgc-success">已成功</Tag>
              状态下，无需额外操作，但可以确认交易记录。
            </div>
            <div>
              <Tag :bordered="false" class="bgc-gray">已撤销</Tag>
              状态下，请确保系统已经正确处理退款，并且患者的账户已收到退款。
            </div>
          </div>
        </CollapsePanel>
        <CollapsePanel header="支付状态和交易状态的区别">
          <strong>范围不同：</strong>
          <ul>
            <li>支付状态更侧重于描述一个完整的支付流程中划价单所处的不同阶段。</li>
            <li>交易状态则专注于每次具体的支付尝试的结果，它反映了每一次独立的财务交互情况。</li>
          </ul>
          <strong>关注点不同：</strong>
          <ul>
            <li>支付状态（针对划价单），交易状态（针对交易系统）</li>
            <li>对于收费员而言，了解支付状态可以帮助他们掌握整体的账务处理进度，比如哪些患者还没有付款、哪些正在处理中等。</li>
            <li>而交易状态则更多地用于监控每一笔具体支付行为的成功与否，特别是在出现异常情况时能迅速定位问题所在并采取相应措施。</li>
          </ul>
        </CollapsePanel>
      </Collapse>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.trnas-status-help {
  ul {
    margin-top: 4px;
  }

  div {
    margin-bottom: 4px;
  }
}
</style>
