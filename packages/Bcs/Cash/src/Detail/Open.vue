<script lang="ts" setup>
import { FrownFilled } from '@ant-design/icons-vue'
import { Data } from '@idmy/core'
import Detail from './index.vue'

const { position, data, onLoad } = defineProps({
  onLoad: { type: FrownFilled },
  data: { type: Object as PropType<Data>, required: true },
  position: { type: String, default: 'right' },
  component: { type: String, default: 'a' },
})

const emit = defineEmits(['ok'])

const [open] = useLoading(() =>
  Modal.open({
    component: Detail,
    escClosable: true,
    maskClosable: true,
    position,
    props: { cashId: data.cashId, onLoad },
    title: `结算详情`,
    width: 4,
  })
)
</script>
<template>
  <component v-bind="$attrs" :is="component" @click="open">
    <slot>
      {{ cashId }}
    </slot>
  </component>
</template>
