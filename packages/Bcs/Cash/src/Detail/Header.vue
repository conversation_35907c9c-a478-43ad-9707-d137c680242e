<script lang="ts" setup>
import { View } from '@idmy/antd'
import { add, Api, cfg, Data, useLoading } from '@idmy/core'
import { PrintButton } from '@mh-bcs/print'
import { FullRefund } from '@mh-bcs/settle'
import { pageCash, printCash } from '@mh-bcs/util'
import { Button, Space, Tag } from 'ant-design-vue'

const CreateRedBill = FullRefund

const { data, load } = defineProps({
  data: { type: Array as PropType<Data[]>, required: true },
  load: { type: Function, required: true },
})

const inputParams = computed(() => {
  return {
    params: { cashId: data[0].cashId },
    pageNo: 1,
    pageSize: 1,
  }
})

const totalAmt = computed(() => data.reduce((pre, cur) => add(pre, cur.amount), 0))

const getData = async params => {
  const tmp = await pageCash(params)
  return tmp.list?.[0] ?? []
}

const onPrintCash = (row: Data) => {
  printCash(row.cashId, row.cashType, { mode: 'preview' })
}

const [openCreateRedBill] = useLoading(async (cash: Data) => {
  await Modal.close()
  Modal.open({
    component: CreateRedBill,
    onClose: () => {
      load()
    },
    props: { cashId: cash.blueCashId ?? cash.cashId, cashType: 'OUTPATIENT' },
    title: `红冲退费#${cash.blueCashId ?? cash.cashId}【${cash.payer}】`,
    width: cfg.setting?.partialRefundDisabled ? 4 : 5,
  })
})
</script>

<template>
  <Api v-slot="{ output: row }: Data" :input="inputParams" :load="getData" first spin>
    <View class="mb-16px" line right-class="f1" title="基本信息">
      <template v-if="row.cashType === 'OUTPATIENT'" #left>
        <Tag v-if="totalAmt === 0" color="red">已全退</Tag>
        <template v-else-if="cfg.setting?.cashierBackoutEnabled && row.visitId">
          <Button size="small" type="primary" @click="openCreateRedBill(row)">开红冲单</Button>
        </template>
      </template>
      <template v-if="row.cashType === 'OUTPATIENT'" #right>
        <Space v-if="row.actionType === 'BLUE' && cfg.setting?.cashPrintTicket !== 0">
          <PrintButton v-if="row.refundedAmount != row.amount && row.status === 'OK' && totalAmt !== 0" code="outpatient:charge" size="small" text="补打收费单" @click="onPrintCash(row)" />
        </Space>
      </template>
    </View>
  </Api>
</template>
