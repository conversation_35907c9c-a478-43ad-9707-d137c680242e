<script lang="ts" setup>
import { View } from '@idmy/antd'
import { add, Api, Data, Format, subtract } from '@idmy/core'
import { PrintButton } from '@mh-bcs/print'
import { pageCash, printRefund } from '@mh-bcs/util'
import { Descriptions, DescriptionsItem, Tag } from 'ant-design-vue'
import { isNil } from 'lodash-es'

const { cashId } = defineProps({
  cashId: { type: Number as PropType<number>, required: true },
})

const inputParams = computed(() => {
  return {
    params: { cashId },
    pageNo: 1,
    pageSize: 1,
  }
})

const getData = async params => {
  const tmp = await pageCash(params)
  return tmp.list?.[0] ?? []
}
</script>

<template>
  <Api v-slot="{ output: row }: Data" :input="inputParams" :load="getData" first spin>
    <View right-class="f1" title="结算信息">
      <template v-if="row.cashType === 'OUTPATIENT'" #right>
        <PrintButton
          v-if="row.actionType == 'RED' && row.status === 'OK'"
          code="outpatient:refund"
          size="small"
          text="补打退费单"
          @click="printRefund(row.cashId, row.cashType, { mode: 'preview' })"
        />
      </template>
      <template #left>
        <Format :bordered="false" :component="Tag" :value="row.status" params="CashStatus" type="Enum" />
      </template>
      <Descriptions :column="4" bordered size="small">
        <DescriptionsItem :labelStyle="{ width: '100px' }" label="患者姓名">{{ row.payer }}</DescriptionsItem>
        <DescriptionsItem :labelStyle="{ width: '100px' }" label="开单医生">{{ row.clinicianName }}</DescriptionsItem>
        <DescriptionsItem :labelStyle="{ width: '100px' }" label="收费类型">
          <Format :value="row.cashType" params="CashType" type="Enum" />
        </DescriptionsItem>
        <DescriptionsItem :labelStyle="{ width: '100px' }" label="诊疗流水">{{ row.visitId }}</DescriptionsItem>
        <template v-if="add(row.discounted ?? 0, row.derated ?? 0) !== 0">
          <DescriptionsItem :labelStyle="{ width: '100px' }" label="总费用">
            <Format :value="row.totalAmount" type="Currency" />
          </DescriptionsItem>
          <DescriptionsItem :labelStyle="{ width: '100px' }" label="优惠金额">
            <Format :value="add(row.discounted ?? 0, row.derated ?? 0)" type="Currency" />
          </DescriptionsItem>
        </template>
        <DescriptionsItem :labelStyle="{ width: '100px' }" label="结算金额">
          <Format :value="row.amount" type="Currency" />
        </DescriptionsItem>
        <template v-if="!isNil(row.miFundAmt)">
          <DescriptionsItem :labelStyle="{ width: '100px' }" label="医保支付">
            <Format :value="add(row.miFundAmt, row.miAcctAmt, row.familyAcctAmt)" type="Currency" />
          </DescriptionsItem>
          <DescriptionsItem :labelStyle="{ width: '200px' }" label="自费金额">
            <Format :value="subtract(row.amount, add(row.miFundAmt, row.miAcctAmt, row.familyAcctAmt))" type="Currency" />
          </DescriptionsItem>
        </template>
        <DescriptionsItem :labelStyle="{ width: '100px' }" label="收费员">{{ row.payee }}</DescriptionsItem>
        <DescriptionsItem :labelStyle="{ width: '100px' }" label="结算日期">
          <Format :value="String(row.cashDate)" type="Date" />
        </DescriptionsItem>
        <DescriptionsItem :labelStyle="{ width: '100px' }" label="是否交账">
          <Format :suffix="row.checkId ? `(${row.checkId})` : ''" :value="!!row.checkId" type="Boolean" />
        </DescriptionsItem>
        <DescriptionsItem label="医保险种" v-if="row.insuranceTypeId">
          <Format :value="row.insuranceTypeId" params="InsuranceType" type="Dict" />
        </DescriptionsItem>
        <DescriptionsItem label="医疗类别">
          <Format :value="row.medTypeId" params="MedType" type="Dict" />
        </DescriptionsItem>
        <DescriptionsItem v-if="row.notes" label="备注">{{ row.notes }}</DescriptionsItem>
      </Descriptions>
    </View>
  </Api>
</template>
