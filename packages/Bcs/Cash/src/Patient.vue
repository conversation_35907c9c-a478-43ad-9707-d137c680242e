<script lang="ts" setup>

import { Api, Format } from '@idmy/core';
import { getPatient } from '@mh-bcs/util';
import { Popover } from 'ant-design-vue'


defineProps({
  patientId: { type: Number },
})
</script>
<template>
<Popover :mouseEnterDelay="1" placement="right">
  <template #content>
    <Api v-slot="{ output }" :immediate="true" :load="() => patientId ? getPatient(patientId) : {}" spin type="Object">
      <Format :value="output.patientName" component="div" prefix="患者姓名："/>
      <Format :value="output.telNo" component="div" prefix="患者手机："/>
      <Format :value="output.certTypeId" component="div" prefix="证件类型："/>
      <Format :value="output.idcertNo" component="div" prefix="证件号码："/>
    </Api>
  </template>
  <slot/>
</Popover>

</template>
