<template>
  <div v-if="isShowInfo" v-show="transStatus !== null">
    <div v-if="transStatus === 1" class="primary">
      <a-spin size="small" />
      等待{{ text }}结果中…
    </div>
    <div v-else-if="transStatus === 2" class="danger">
      {{ text }}失败
      <template v-if="isShowError"> ：{{ transResultMsg }}</template>
    </div>
    <div v-else-if="transStatus === 3" class="primary">{{ text }}成功</div>
  </div>
</template>
<script lang="ts">
import { getTransStatus } from '@mh-bcs/util'

export default defineComponent({
  props: {
    isPay: { type: Boolean, default: true },
    isShowError: { type: Boolean, default: true },
    isShowInfo: { type: Boolean, default: true },
  },
  data() {
    return {
      transStatus: undefined,
      transResultMsg: undefined,
      timer: 0,
    }
  },
  beforeUnmount() {
    this.reset()
  },
  computed: {
    text() {
      return this.isPay ? '支付' : '退费'
    },
  },
  methods: {
    reset() {
      this.transStatus = undefined
      this.transResultMsg = undefined
      clearInterval(this.timer)
    },
    async start(cashId: number) {
      this.reset()
      await this.start0(cashId)
    },
    async start0(cashId: number) {
      clearInterval(this.timer)
      try {
        const result = await getTransStatus(cashId, this.isPay ? 1 : 2)
        if (result) {
          const { transStatus, transResultMsg } = result
          this.transStatus = transStatus
          this.transResultMsg = transResultMsg
          if (transStatus === 1) {
            this.timer = setInterval(() => this.start0(cashId), 1000)
          } else {
            this.$emit('ok', transStatus === 3, { error: transResultMsg, cashId })
            clearInterval(this.timer)
          }
        } else {
          this.$emit('ok', true, { cashId })
          clearInterval(this.timer)
        }
      } catch {
        this.transStatus = undefined
        this.transResultMsg = undefined
      }
    },
  },
})
</script>
