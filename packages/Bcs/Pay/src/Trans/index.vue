<template>
  <div v-if="staring && (state[1]?.count || state[2]?.count)" class="primary">
    <Spin size="small" />
    等待
    <template v-if="state[1]?.count">支付</template>
    <template v-if="state[1]?.count && state[2]?.count">/</template>
    <template v-if="state[2]?.count">退费</template>
    结果中…
  </div>
</template>
<script lang="ts">
import { getUnprocessedByAccountId, getUnprocessedTrans } from '@mh-bcs/util'
import { Spin } from 'ant-design-vue'

export default {
  components: {
    Spin,
  },
  data() {
    return {
      max: 0,
      staring: false,
      state: {
        1: {
          count: 0,
        },
        2: {
          count: 0,
        },
      },
      timer: 0,
    }
  },
  beforeDestroy() {
    this.stop()
  },
  methods: {
    stop() {
      clearInterval(this.timer)
    },
    async start(params) {
      this.stop()
      this.state = await this.api(params)
      if (this.state[1]?.count || this.state[2]?.count) {
        if (this.max++ < 10) {
          this.timer = setInterval(() => this.start(params), 1000)
        }
      } else {
        this.$emit('ok')
        this.stop()
      }
      this.staring = true
    },
    api(params) {
      if (params.cashId) {
        return getUnprocessedTrans(params)
      } else {
        return getUnprocessedByAccountId(params)
      }
    },
  },
}
</script>
