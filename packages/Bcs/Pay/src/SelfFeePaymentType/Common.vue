<script lang="ts" setup>
import { ChargeContext, chargeInjectKey, usePay } from '@mh-bcs/util'
import { Button, InputNumber } from 'ant-design-vue'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})

const inputRef = ref()
const onFocus = () => nextTick(() => inputRef.value?.focus())
onMounted(onFocus)
watch(
  () => ctx.unpaidAmount,
  () => onFocus()
)

const { onPay, disabled } = usePay(ctx)
</script>
<template>
  <div class="mt-16px" flex justify-between>
    <div flex items-center>
      <span class="mr-8px">收费金额</span>
      <InputNumber ref="inputRef" v-model:value="ctx.payAmount" :controls="false" :max="ctx.unpaidAmount" :min="0" :precision="2" class="w-180px fs24 h-36px" />
    </div>
    <Button :disabled="disabled || ctx.paying" :loading="ctx.paying" class="h-80px w-64px" type="primary" @click="onPay()">支付 </Button>
  </div>
</template>
