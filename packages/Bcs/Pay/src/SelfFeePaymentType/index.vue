<script lang="ts" setup>
import { View } from '@idmy/antd'
import { add, Data, type EnumProps, Format, newError, subtract } from '@idmy/core'
import { allPaymentTypes, ChargeContext, chargeInjectKey, PaymentTypeEnum, PaymentTypes, toPaymentType } from '@mh-bcs/util'
import { allOrgPaymentTypes } from '@mh-hip/util'
import { Select, SelectOption, Space } from 'ant-design-vue'
import { isEmpty } from 'lodash-es'
import Cash from './Cash.vue'
import Common from './Common.vue'

defineProps({
  title: { type: String, default: '自费支付' },
  combine: { type: Boolean, default: false },
})

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})

const loaded = ref(false)
const notTrans = reactive([])
const sorted = reactive<PaymentTypeEnum[]>([])
allOrgPaymentTypes()
  .then(async (rows: Data[]) => {
    if (rows.length === 0) {
      throw newError('当前机构没有设置支付方式')
    }
    const map = new Map<number, boolean>()
    const all = await allPaymentTypes()
    all.forEach(row => map.set(row.paymentId, !row.transFlag))

    rows = rows.filter((row: Data) => {
      if (row.paymentId === PaymentTypeEnum.PREPAID) {
        return false
      }
      const bol = row.paymentId !== PaymentTypeEnum.MI_FUND
      if (ctx.cashType === 'OUTPATIENT' || ctx.cashType === 'REG') {
        return bol && row.forOutpatient === 1
      } else if (ctx.cashType === 'INPATIENT') {
        return bol && row.forInpatient === 1
      } else {
        return bol && row.forOthers === 1
      }
    })
    rows.sort((a: Data, b: Data) => (a.opDisplayOrder ?? 0) - (b.opDisplayOrder ?? 0))
    for (const row of rows) {
      sorted.push(row.paymentId)
      if (row.paymentId !== PaymentTypeEnum.CASH && map.get(row.paymentId)) {
        row.paymentType = toPaymentType(row.paymentId)
        notTrans.push(row)
      }
    }
    ctx.paymentType = toPaymentType(sorted[0])
  })
  .finally(() => {
    loaded.value = true
  })

const getPaymentTypeName = (id: PaymentTypeEnum): string => {
  const name = PaymentTypes.find((item: EnumProps) => item.value === id)?.title
  if (name) {
    return name
  } else {
    throw newError('找不到支付方式名称')
  }
}

const currentPayAmount = computed(() => add(ctx.payAmount, ...notTrans.map(row => row.payAmount)))
</script>

<template>
  <View
    v-if="loaded"
    :indicator="false"
    :loading="(ctx.isZero && !ctx.isFullAmtMiPay) || ctx.unpaidAmount < 0 || (!isEmpty(ctx.miFundCashPayment) && !ctx.miTrans.miTransId)"
    :title="title"
    line
    right-class="f1"
  >
    <template #left>
      <Select v-if="!combine" v-model:value="ctx.paymentType" :disabled="ctx.paying" class="w-130px!" size="small">
        <SelectOption v-for="id in sorted" :key="toPaymentType(id)" :value="toPaymentType(id)">
          {{ getPaymentTypeName(id) }}
        </SelectOption>
      </Select>
      <Space v-else size="middle">
        <Format
          v-if="!ctx.paying && !ctx.isZero && currentPayAmount !== ctx.unpaidAmount"
          :class="currentPayAmount > ctx.unpaidAmount ? 'primary b' : 'error b'"
          :prefix="currentPayAmount > ctx.unpaidAmount ? '多：' : '少：'"
          :value="subtract(currentPayAmount, ctx.unpaidAmount)"
          type="Currency"
        />
      </Space>
    </template>
    <div class="pl-16px">
      <Cash v-if="ctx.paymentType === 'CASH'" :combine="combine" :first="toPaymentType(25)" :notTrans="notTrans" />
      <Common v-if="['MI_ACCOUNT', 'POS', 'OTHER', 'CREDIT', 'VOUCHER', 'ALIPAY', 'WECHAT'].includes(ctx.paymentType)" />
    </div>
  </View>
</template>
