<template>
  <Popconfirm :disabled="finished || disabled" :title="tip" cancel-text="取消" danger ok-text="确认" placement="left" @confirm="onClick">
    <Button :disabled="finished || disabled" :loading="loading" :size="size" type="primary" v-bind="$attrs">
      <slot />
      <TransStatusLoop ref="loopRef" :is-pay="false" :is-show-info="false" @ok="onOk" />
    </Button>
  </Popconfirm>
</template>
<script lang="ts" setup>
import { Message } from '@idmy/core'
import { Button, Popconfirm } from 'ant-design-vue'
import TransStatusLoop from './Trans/TransStatusLoop.vue'

defineOptions({ name: 'RefundButton' })

const { api } = defineProps({
  api: { type: Function, required: true },
  disabled: { type: Boolean, default: false },
  size: { type: String, default: 'default' },
  tip: { type: String, default: '确认要操作吗？' },
})

const emit = defineEmits(['ok'])

const finished = ref(false)
const cashId = ref(undefined)
const loading = ref(false)
const loopRef = ref()

watch(
  () => finished.value,
  val => {
    if (val) {
      loading.value = false
    }
  }
)

const onOk = async (isOk, { error }) => {
  if (cashId.value) {
    if (isOk) {
      finished.value = true
      try {
        emit('ok', cashId.value)
      } catch {
        loading.value = false
      }
    } else {
      Message.error(error)
      finished.value = false
      loading.value = false
    }
  } else {
    finished.value = true
    emit('ok', null)
  }
}

const onClick = async () => {
  try {
    cashId.value = undefined
    finished.value = false
    loading.value = true
    const result = await api()
    if (result.cashId) {
      cashId.value = result.cashId
      await loopRef.value.start(result.cashId)
    } else {
      await onOk(true, null)
    }
  } catch {
    loading.value = false
  }
}
</script>
