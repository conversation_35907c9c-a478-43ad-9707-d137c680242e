import type { EnumProps } from '@idmy/core'
import { addEnum } from '@idmy/core'
import { CashType, PaymentType } from '../Base'

export enum CashActionTypeEnum {
  Normal = 1,
  Refunded = 2,
}

export enum MiTransStatusEnum {
  uploaded = -2,
  canceled = -1,
  unsettle = 0,
  preSettle = 1,
  settled = 2,
}

export enum CashTransStatusEnum {
  waiting = 0,
  transaction = 1,
  fail = 2,
  success = 3,
  cancel = 4
}

export enum CashTypeEnum {
  reg = 1,
  outpatient = 2,
  inpatient = 3,
  deposit = 4,
  physical_examination = 5,
  hp = 6,
  third_party = 9
}


export function toCashType(value: number): CashType {
  return CashTypeEnum[value] as CashType
}

export const cashTypes = [
  { title: '挂号收费', value: CashTypeEnum.reg },
  { title: '门诊收费', value: CashTypeEnum.outpatient },
  { title: '住院收费', value: CashTypeEnum.inpatient },
  { title: '预交金充值', value: CashTypeEnum.deposit },
  { title: '体检收费', value: CashTypeEnum.physical_examination },
  { title: '互联网医院收费', value: CashTypeEnum.hp },
  { title: '第三方收费', value: CashTypeEnum.third_party },
]

addEnum('CertType', (): EnumProps[] => [
  { title: '居民身份证', value: '01' },
  { title: '中国人民解放军军官证', value: '02' },
  { title: '中国人民武装警察警官证', value: '03' },
  { title: '香港特区护照/港澳居民来往内地通行证', value: '04' },
  { title: '澳门特区护照/港澳居民来往内地通行证', value: '05' },
  { title: '台湾居民来往大陆通行证', value: '06' },
  { title: '外国人永久居留证', value: '07' },
  { title: '外国人护照', value: '08' },
  { title: '残疾人证', value: '09' },
  { title: '军烈属证明', value: '10' },
  { title: '外国人就业证', value: '11' },
  { title: '外国专家证', value: '12' },
  { title: '外国人常驻记者证', value: '13' },
  { title: '台港澳人员就业证', value: '14' },
  { title: '回国（来华）定居专家证', value: '15' },
  { title: '社会保障卡', value: '90' },
  { title: '其他身份证件', value: '99' },
])

addEnum('ExecStatus', (): EnumProps[] => [
  { title: '待接单', value: 0 },
  { title: '已接单', value: 1 },
  { title: '已开始', value: 2 },
  { title: '已完成', value: 3 },
  { title: '已撤销', value: 4 },
  { title: '已驳回', value: 5 },
])


export enum PaymentTypeEnum {
  CASH = 11,
  PREPAID = 12,
  CHECK = 13,
  VOUCHER = 14,
  MI_FUND = 21,
  MI_FAMILY = 22,
  BI = 23,
  EPP = 24,
  POS = 25,
  OTHER = 26,
  MI_ACCOUNT = 27,
  CREDIT = 28,
  ALIPAY = 29,
  WECHAT = 30
}

export function toPaymentType(value: number): PaymentType {
  return PaymentTypeEnum[value] as PaymentType
}

export const PaymentTypes: EnumProps[] = [
  { title: '现金', value: PaymentTypeEnum.CASH },
  { title: '预交账户', value: PaymentTypeEnum.PREPAID },
  { title: '支票', value: PaymentTypeEnum.CHECK },
  { title: '代金券', value: PaymentTypeEnum.VOUCHER },
  { title: '医保统筹', value: PaymentTypeEnum.MI_FUND },
  { title: '医保代支', value: PaymentTypeEnum.MI_FAMILY },
  { title: '商业保险', value: PaymentTypeEnum.BI },
  { title: '电子支付', value: PaymentTypeEnum.EPP },
  { title: 'POS刷卡', value: PaymentTypeEnum.POS },
  { title: '其他移动支付', value: PaymentTypeEnum.OTHER },
  { title: '医保个账', value: PaymentTypeEnum.MI_ACCOUNT },
  { title: '挂账', value: PaymentTypeEnum.CREDIT },
  { title: '支付宝', value: PaymentTypeEnum.ALIPAY },
  { title: '微信', value: PaymentTypeEnum.WECHAT },
]

export enum ClinicTypeEnum {
  outpatient = 1,
  inpatient = 2,
  emergency = 3,
  examination = 4,
  detection = 5,
  fulfillment = 6,
  other = 9
}

declare module '@idmy/core' {
  interface IEnum {
    CertType: () => Promise<EnumProps[]> | EnumProps[]
    ExecStatus: () => Promise<EnumProps[]> | EnumProps[]
    InsuranceType: () => Promise<EnumProps[]> | EnumProps[]
  }
}
