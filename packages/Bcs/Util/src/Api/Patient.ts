import { Data } from '@idmy/core'

export function getPatient(patientId: number): Promise<Data> {
  return http.post<Data>('/hip-base/patient/get', { patientId }, { appKey: 'hip' })
}

/**
 * 获取患者信息
 * @param patientId
 */
export function patientInfoApi(patientId: number) {
  return http.post('/hip-base/patient/orgInfo', { patientId }, { appKey: 'hip' })
}

/**
 * 获取患者信息
 */
export function getPatientIdByIdcertNo(certTypeId: number, idcertNo: string) {
  return http.post('patientApi.getPatientIdByIdcertNo', { certTypeId, idcertNo }, { appKey: 'hip' })
}
