import { add, Data, format, http } from '@idmy/core'
import { Currents, getPrintTemplate } from '@mh-base/core'
import qrcode from 'qrcode'

export async function hasPrintTemplate(code: string) {
  const tmp = await getPrintTemplate(code)
  return <PERSON><PERSON>an(tmp.title)
}


export async function getCashInfo(cashId: number): Promise<Data> {
  const data = await http.post<Data>('/api/bcs/print/getCashInfo', { cashId }, { appKey: 'bcs' })
  data.name = `姓名：${data.name ?? ''} ${data.gender ?? ``}`
  data.clinician = `医生：${data.clinician ?? ''} ${data.dept ?? ''}`
  data.patientNo = `病历号：${data.patientNo ?? ''}`
  data.cashId = `票据号：${cashId}`
  data.payMode = `结算方式：${(data.miFundPay || data.miAccountPay) > 0 ? '医保' : '自费'}`
  data.payee = `收费员：${data.payee ?? ''}`
  data.amount = `合计金额：${format(data.amount ?? 0, 'Currency')}`
  data.miFundPay = `医保统筹：${format(data.miFundPay ?? 0, 'Currency')}`
  data.miAccountPay = `医保个账：${format(data.miAccountPay ?? 0, 'Currency')}`
  data.selfFee = `个人缴费：${format(data.selfFee ?? 0, 'Currency')}`
  data.acctBalance = `个账余额：${format(data.trans?.acctBalance ?? 0, 'Currency')}`
  data.date = `结算时间：${format(data.date, 'Datetime')}`
  data.printTime = `打印时间：${format(Date.now(), 'Datetime')}`
  data.invNo = data.invoice?.invNo ? '0' + data.invoice?.invNo : ''
  data.invCode = data.invoice?.invCode ?? ''
  data.checkCode = data.invoice?.checkCode ?? ''
  if (data.regType) {
    data.regType = `挂号类型：${data.regType}`;
  }
  if (data.reg) {
    data.aptNo = data.reg.aptNo
  }
  if (data.qrcode) {
    data.qrcode = `data:image/png;base64,${data.qrcode}`;
  } else if (data.url) {
    data.qrcode = await new Promise((resolve, reject) => {
      qrcode.toDataURL(data.url, { margin: 0 }, (err: any, url: string) => {
        url ? resolve(url) : reject(err)
      })
    })
  }

  if (Currents.tenantId === *********) {
    const map = new Map()
    data.items.forEach(row => {
      const item = map.get(row.feeCatId)
      if (item) {
        item.amount = add(item.amount, row.amount)
      } else {
        map.set(row.feeCatId, { artName: row.feeCatName, amount: row.amount })
      }
    })
    const out = []
    map.forEach((value) => {
      value.amount = format(value.amount, 'Currency')
      out.push(value)
    })
    data.items = out
  } else {
    data.items.forEach((item: Data) => {
      item.amount = format(item.amount ?? 0, 'Currency')
      item.total = format(item.total ?? 0, 'Currency');
      item.price = format(item.price ?? 0, 'Currency', 4)
    })
  }
  return data
}
