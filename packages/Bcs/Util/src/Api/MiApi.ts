import type { Data } from '@idmy/core'
import { http, Notification } from '@idmy/core'
import { Currents } from '@mh-base/core'
import { isShandong } from '../Util'
import { MiTrans } from './MiTrans'


const cache = {}


export async function initCurrentMiSetting() {
  try {
    await currentMiSetting()
  } catch {}
}

async function currentMiSetting() {
  const tmp = cache[Currents.tenantId]
  if (tmp) {
    return tmp
  }
  const data = await http.post('/mcisp/miorgsetting/current', { org_id: Currents.tenantId }, { appKey: 'mcisp' })
  if (data) {
    cache[Currents.tenantId] = data
    return data
  } else {
    return null
  }
}

export function hasMiSetting() {
  if (isShandong()) {
    return false
  }
  try {
    return Boolean(cache[Currents.tenantId])
  } catch {
    return false
  }
}

export class MiLocalError extends Error {
  constructor(message: string) {
    super(message)
  }
}

async function localPost(path: string, params: Data) {
  try {
    return http.post<Data>(path, params, { timeout: 60 * 1000 * 10, appKey: 'card', ignoreError: true })
  } catch (e) {
    await Notification.error({ title: '医保异常', content: e.msg })
    throw new MiLocalError(e.msg)
  }
}

const PATH = '/api/mi_rest_call'

async function post(params: Data): Promise<Data> {
  const setting = await currentMiSetting()
  if (setting?.endpointType === 2 || Currents.tenantId === 360103001) {
    return localPost(PATH, params)
  } else {
    return http.post(PATH, params, { appKey: 'micsp' })
  }
}

export async function outpatientPreSettle(ins: Data): Promise<MiTrans> {
  const data = await post({
    acct_used_flag: ins.acctUsedFlag ? 1 : 0,
    action: 'OP_TRANS',
    business_type_id: '104',
    card: ins.card?.readCard,
    cash_trans_id: ins.transId,
    insuplc_admdvs: ins.insuplcAdmdvs,
    insurance_type_id: ins.insuranceTypeId,
    mdtrt_cert_no: ins.mdtrt_cert_no,
    mdtrt_cert_type: ins.mdtrt_cert_type,
    medical_type_id: ins.medTypeId,
    opter: Currents.id,
    org_id: Currents.tenantId,
    psn_no: ins.psn_no,
    psn_setlway: ins.psnSetlway,
  })
  if (data.transId) {
    data.miTransId = data.transId
  }
  return data
}

export function outpatientSettle(ins: Data): Promise<MiTrans> {
  return post({
    action: 'OP_TRANS_DONE',
    business_type_id: '104',
    card: ins.card?.readCard,
    mdtrt_cert_no: ins.mdtrt_cert_no,
    mdtrt_cert_type: ins.mdtrt_cert_type,
    mi_trans_id: ins.miTransId,
    opter: Currents.id,
    org_id: Currents.tenantId,
    psn_setlway: 1,
  })
}

export function inHospitalRegister(ins: Data) {
  return post({
    action: 'IPADM',
    card: ins.card?.readCard,
    mdtrt_cert_no: ins.mdtrt_cert_no,
    mdtrt_cert_type: ins.mdtrt_cert_type,
    opter: Currents.id,
    org_id: Currents.tenantId,
    visit_id: ins.visitId,
  })
}

export function inHospitalUnregister(ins: Data) {
  return post({
    action: 'UNDO_IPADM',
    card: ins.card?.readCard,
    mdtrt_cert_no: ins.mdtrt_cert_no,
    mdtrt_cert_type: ins.mdtrt_cert_type,
    opter: Currents.id,
    org_id: Currents.tenantId,
    visit_id: ins.visitId,
  })
}

// 住院费用上传
export function inHospitalFeeUpload(ins: Data) {
  return post({
    action: 'IPFDUP',
    card: ins.card?.readCard,
    mdtrt_cert_no: ins.mdtrt_cert_no,
    mdtrt_cert_type: ins.mdtrt_cert_type,
    opter: Currents.id,
    org_id: Currents.tenantId,
    visit_id: ins.visitId,
  })
}

export function inHospitalFeeUndo(ins: Data) {
  return post({
    action: 'UNDOIPFDUP',
    card: ins.card?.readCard,
    mdtrt_cert_no: ins.mdtrt_cert_no,
    mdtrt_cert_type: ins.mdtrt_cert_type,
    opter: Currents.id,
    org_id: Currents.tenantId,
    visit_id: ins.visitId,
  })
}

export async function inHospitalPreSettle(ins: Data) {
  const data = await post({
    acct_used_flag: ins.acctUsedFlag ? 1 : 0,
    action: 'IPPAID',
    card: ins.card?.readCard,
    cash_trans_id: ins.cashTransId,
    insuplc_admdvs: ins.insuplcAdmdvs,
    insurance_type_id: ins.insuranceTypeId,
    mdtrt_cert_no: ins.mdtrt_cert_no,
    mdtrt_cert_type: ins.mdtrt_cert_type,
    medical_type_id: ins.medTypeId,
    opter: Currents.id,
    org_id: Currents.tenantId,
    visit_id: ins.visitId,
  })
  if (data.transId) {
    data.miTransId = data.transId
  }
  return data
}

export function inHospitalSettle(ins: Data) {
  return post({
    action: 'IP_TRANS_DONE',
    card: ins.card?.readCard,
    cash_trans_id: ins.cashTransId,
    mdtrt_cert_no: ins.mdtrt_cert_no,
    mdtrt_cert_type: ins.mdtrt_cert_type,
    mi_trans_id: ins.miTransId,
    opter: Currents.id,
    org_id: Currents.tenantId,
  })
}

export function outHospitalRegister(ins: Data) {
  return post({
    action: 'IPDIS',
    card: ins.card?.readCard,
    opter: Currents.id,
    org_id: Currents.tenantId,
    visit_id: ins.visitId,
  })
}

export function outHospitalUnregister(ins: Data) {
  return post({
    action: 'UNDO_IPDIS',
    card: ins.card?.readCard,
    mdtrt_cert_no: ins.mdtrt_cert_no,
    mdtrt_cert_type: ins.mdtrt_cert_type,
    opter: Currents.id,
    org_id: Currents.tenantId,
    visit_id: ins.visitId,
  })
}

export function settleUndo(miTransId: number) {
  return post({
    action: 'UNDO_TRANS',
    business_type_id: '104',
    mi_trans_id: miTransId,
    opter: Currents.id,
    org_id: Currents.tenantId,
  })
}

export function printSettleList(visit_id: number, trans_id: number) {
  return localPost('/api/mi/OtherMethod', {
    method: 'PrintSettleList',
    org_id: Currents.tenantId,
    trans_id,
    user_id: Currents.id,
    visit_id,
  })
}
