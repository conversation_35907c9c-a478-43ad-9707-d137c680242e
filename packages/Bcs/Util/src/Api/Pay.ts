import type { Data } from '@idmy/core'
import { http, useLoading } from '@idmy/core'
import { CashType, PaymentType } from '../Base'
import { ChargeContext } from './Charge'


export interface BeforePayIn {
  billIds: number[]
  cashType: CashType
  startDate?: number
  endDate?: number
  midway: boolean
  visitId: number
}

export interface PayIn {
  accountId?: number
  authCode?: string
  cashId?: number
  cashType: CashType
  endDate?: number
  isPrePay?: boolean
  medTypeId?: number | string
  miAcctAmt?: number
  miFundAmt?: number
  miMultiAidAmt?: number
  payAmount?: number
  paymentId?: number
  paymentType: PaymentType
  startDate?: number
  visitId?: number
}

export interface PayResult {
  absoluteError: boolean
  cashId: number
  data: any
  msg: string
  transId: number
}


export function payUndo(cashId: number) {
  return http.post(`/api/bcs/PayUndo/undo`, { cashId }, { appKey: 'bcs' })
}

export async function createRedBillByBlueBillIds(billIds: number[]) {
  return http.post('/api/bcs/Pay/createRedBillByBlueBillIds', { billIds }, { appKey: 'bcs' })
}

export function payOfZero(billIds: number[]) {
  return http.post('/api/bcs/Pay/payOfZero', { billIds }, { appKey: 'bcs' })
}

export function refundOfZero(billIds: number[]) {
  return http.post('/api/bcs/Pay/refundOfZero', { billIds }, { appKey: 'bcs' })
}

export function getPrepaidBalanceByPatientIdOrVisitId(visitId: number, patientId?: number) {
  return http.post('/api/bcs/Pay/getPrepaidBalanceByPatientIdOrVisitId', { visitId, patientId }, { appKey: 'bcs' })
}

export function updateMiStatus(params: any) {
  return http.post('/api/bcs/Pay/updateMiStatus', params, { appKey: 'bcs' })
}

export function getUnprocessedTrans(cashId: number, actionType?: number) {
  return http.post('/api/bcs/Pay/getUnprocessedTrans', { cashId, actionType }, { appKey: 'bcs' })
}

export function getTransStatus(cashId: number, actionType?: number) {
  return http.post('/api/bcs/Pay/getTransStatus', { cashId, actionType }, { appKey: 'bcs' })
}

export function beforePay(payIn: BeforePayIn): Promise<number> {
  return http.post('/api/bcs/Pay/beforePay', payIn, { appKey: 'bcs', ignoreError: true })
}

export function pay(payIn: PayIn): Promise<PayResult> {
  if (payIn.cashType === 'DEPOSIT') {
    return http.post('/api/bcs/deposit/DepositPay/pay', payIn, { appKey: 'bcs' })
  } else {
    return http.post('/api/bcs/Pay/pay', payIn, { appKey: 'bcs' })
  }
}

export function payOk(cashId: number) {
  return http.post(`/api/bcs/Pay/payOk/${cashId}`, undefined, { appKey: 'bcs' })
}

export function payCancel(cashId: number, notes: string, cancelBill: boolean, cashType: CashType) {
  return http.post('/api/bcs/Pay/payCancel', { cashId, notes, cancelBill, cashType }, { appKey: 'bcs' })
}


export function usePay(ctx: ChargeContext, pays: Data[] = []) {
  const logs = reactive<any>([])
  const pushLog = (msg: string, hasError = false, date = new Date()) => logs.push({ date, msg, hasError })
  const loopRef = ref()
  const isRetry = ref(false)
  const payOk = ref(false)

  const reset = () => {
    logs.splice(0, logs.length)
    ctx.authCode = undefined
    ctx.paying = false
    isRetry.value = false
    payOk.value = false
    nextTick(() => {
      loopRef.value.reset()
    })
  }

  const [onPay] = useLoading(async () => {
    if (ctx.paying) {
      return
    }
    ctx.paying = true
    try {
      await ctx.miOnSettle?.()
      if (ctx.payAmount > 0) {
        payOk.value = false
        const { absoluteError, msg, cashId } = await pay({
          cashId: ctx.cashId,
          authCode: ctx.authCode,
          accountId: ctx.accountId,
          visitId: ctx.visitId,
          cashType: ctx.cashType,
          payAmount: ctx.payAmount,
          paymentType: ctx.paymentType,
        })
        ctx.cashId = cashId
        pushLog(msg, true)
        await loopRef.value.start(ctx.cashId)
        isRetry.value = absoluteError
        ctx.paying = !isRetry.value
      }
    } catch {
      reset()
    } finally {
      ctx.paying = false
      await ctx.onLoad(ctx.cashId)
    }
  })

  const disabled = computed(() => {
    if (payOk.value) {
      return true
    }
    if (ctx.preSettleLoading) {
      return true;
    }
    if (ctx.isFullAmtMiPay) {
      return false
    } else if (ctx.allowPayZero) {
      return ctx.payAmount < 0
    } else {
      return ctx.payAmount <= 0
    }
  })

  return { onPay, disabled, isRetry, loopRef, logs, reset, payOk }
}
