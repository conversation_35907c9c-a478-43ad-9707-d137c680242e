import { add, Data, http, PageOut } from '@idmy/core'
import { BillType, PaidStatus } from '../Base'
import { listRecipeTypeIdsByRecipeIds } from './Recipe'

export interface Bill {
  // 划价单流水号
  billId: number

  // 原费用单流水号
  blueBillId?: number

  // 结算流水号
  cashId?: number

  // 收费状态
  paidStatus: PaidStatus

  // 诊疗流水号
  visitId: number

  // 医疗类别代号
  medTypeId?: number

  // 划价单类型代号
  billType: BillType

  // 折后金额
  amount: number

  // 总费用
  totalAmount: number

  // 折让金额
  discounted?: number

  // 减免金额
  derated?: number

  // 患者姓名
  name: string

  // 创建时间
  createdAt: string

  // 录单用户ID
  userId?: number

  // 非医疗金额
  nonMedicalAmt?: number

  // 机构ID
  orgId: number

  // 开单科室代码
  applyDeptCode?: string

  // 执行科室代码
  execDeptCode?: string

  // 处方ID
  recipeNo?: string

  // 费用摘要
  notes?: string

  // 划价日期
  billDate?: number

  // 开单医师ID
  clinicianId?: number

  // 出入库单流水号
  wmbId?: number

  // 费用所属期起
  periodStart?: number

  // 费用所属期止
  periodEnd?: number

  // 处方流水号
  recipeId?: number

  // 医疗单元代号
  sectionId?: number

  // 患者ID
  patientId?: number

  // 挂号ID
  regId?: number

  // 中药付数
  times?: number

  // 处置流水号
  treatId?: number

  // 医保错误行数
  miErrCount?: number

  // 诊疗类型代号
  clinicTypeId?: number

  // 医保险种代号
  insuranceTypeId?: number

  // 管床医生ID
  bedsideCID?: number

  payer?: string
}


export function clearData() {
  return http.post(`/api/bcs/Bill/clearData`, {}, { appKey: 'bcs' })
}

export function listBluesWithExecStatusByCashId(cashId: number) {
  return http.post(`/api/bcs/Bill/listBluesWithExecStatusByCashId/${cashId}`, {}, { appKey: 'bcs' })
}

export function listUnpaidRedsByCashId(cashId: number): Promise<Bill[]> {
  return http.post(`/api/bcs/Bill/listUnpaidRedsByCashId/${cashId}`, {}, { appKey: 'bcs' })
}

export function listBillIdsByCashIdFromCashBill(cashId: number) {
  return http.post('/api/bcs/Bill/listIdsByCashIdFromCashBill/' + cashId, {}, { appKey: 'bcs' })
}

export function listFullBillsByIds(ids: number[]): Promise<Bill[]> {
  return http.post('/api/bcs/Bill/listFullByIds', ids, { appKey: 'bcs' })
}

export function getBill(id: number): Promise<Bill> {
  return http.post('/api/bcs/Bill/get/' + id, {}, { appKey: 'bcs' })
}

export function billPageApi(params: any) {
  return http.post('/api/bcs/Bill/page', params, { appKey: 'bcs' })
}

export function refundBillApi(params: any) {
  return http.post('/api/bcs/Bill/refundBill', params, { appKey: 'bcs' })
}

export function refundBillDetailApi(params: any) {
  return http.post('/api/bcs/Bill/refundBillDetail', params, { appKey: 'bcs' })
}

export function listBillsByCashIdFromCashBill(cashId: number): Promise<Bill[]> {
  return http.post('/api/bcs/Bill/listByCashIdFromCashBill/' + cashId, {}, { appKey: 'bcs' })
}

export function pageBillsByCashIdFromCashBill(params: Data): Promise<PageOut<Bill>> {
  return http.post('/api/bcs/Bill/pageByCashIdFromCashBill', params, { appKey: 'bcs' })
}

async function cpm(arr: Data[]) {
  try {
    const ids = arr.filter((item: any) => Boolean(item.recipeId)).map((item: any) => item.recipeId)
    const recipeTypeIds = ids.length ? await listRecipeTypeIdsByRecipeIds(ids) : []
    for (const item of arr) {
      let gender = ''
      if (item.gender === 'MAN') {
        gender = ' 男 '
      } else if (item.gender === 'WOMAN') {
        gender = ' 女 '
      }
      item.name0 = item.name;
      item.name = ` ${item.name}${gender}${item.ageOfYears ?? ''}`;
      if (item.recipeId && recipeTypeIds[item.recipeId] === 2) {
        item.isCpm = true
      }
    }
  } catch (e) {
    console.error(e)
  }
}

export async function listUnpaid(patientId?: number): Promise<Bill[]> {
  let data = await http.post<Bill[]>(`/api/bcs/Bill/listUnpaid`, {}, { appKey: 'bcs' })
  const map = new Map()
  if (patientId) {
    data = data.filter((item: Data) => item.patientId === patientId)
  }

  for (const item of data) {
    map.set(item.billId, item)
  }

  for (const item of data) {
    const blue = map.get(item.blueBillId)
    if (blue) {
      if (add(blue.amount, item.amount) === 0) {
        map.get(item.blueBillId).delete = true
        map.get(item.billId).delete = true
      }
    }
  }

  data = Array.from(map.values()).filter((item: Data) => !item.delete)
  await cpm(data)
  return data
}

export function updateBillTimes(id: number, times: number) {
  return http.post('/api/bcs/Bill/updateTimes', { id, times }, { appKey: 'bcs' })
}
