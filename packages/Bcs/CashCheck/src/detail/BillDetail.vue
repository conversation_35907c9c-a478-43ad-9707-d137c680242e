<script lang="ts" setup>
import { add, Data, Format } from '@idmy/core'
import { CashActionTypeEnum, CashTypeEnum } from '@mh-bcs/util'
import { Table } from 'ant-design-vue'
import BillDetailPopover from '~/components/bill/BillDetailPopover.vue'
import { findPaidByFeeTypeId } from '../services.ts'

const { feeTypeId, cashTypeIds, actionType, checkId, endAt } = defineProps({
  feeTypeId: { type: Number, required: true },
  cashTypeIds: { type: Array as PropType<CashTypeEnum[]>, required: true },
  actionType: { type: Number as PropType<CashActionTypeEnum>, required: true },
  checkId: { type: Number },
  endAt: { type: Object },
})

const columns: Data[] = [
  { align: 'left', dataIndex: 'artName', minWidth: 120, title: '项目' },
  { align: 'right', dataIndex: 'price', title: '单价', width: 90 },
  { align: 'right', dataIndex: 'total', title: '数量', width: 75 },
  { align: 'right', dataIndex: 'amount', title: '小计', width: 100 },
]

const key = (row: Data) => {
  return `${row.artId}${row.unit ?? 'null'}${row.price}`
}

const merge = ref(true)

async function findData() {
  let arr: [] = await findPaidByFeeTypeId(cashTypeIds, feeTypeId, actionType, checkId, endAt)
  if (merge.value) {
    const map = new Map()
    arr.forEach((row: Data) => {
      const k = key(row)
      const val = map.get(k)
      if (val) {
        val.total = add(val.total, row.total)
        val.price = add(val.price, row.price)
        val.amount = add(val.amount, row.amount)
      } else {
        map.set(k, row)
      }
    })
    arr = Array.from(map.values())
  }
  arr = arr.filter(row => row.total !== 0)
  for (let i = 0; i < arr.length; i++) {
    arr[i].idx = i
  }
  return arr
}
</script>

<template>
  <Api v-slot="{ output, onLoad }" :load="findData" spin type="Array">
    <View right-class="f1" title="项目明细">
      <template #right>
        <Checkbox v-model:checked="merge" size="small" @change="onLoad">正负合并</Checkbox>
      </template>
      <Table :columns="columns" :dataSource="output" :pagination="false" bordered rowKey="idx" size="small">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'artName'">
            <BillDetailPopover :data="record">
              {{ record.artName }}
              <Tag v-if="record.artSpec">{{ record.artSpec }}</Tag>
              <Tag v-if="record.unit" color="purple">{{ record.unit }}</Tag>
            </BillDetailPopover>
          </template>
          <Format v-if="column.dataIndex === 'discount'" :value="add(record.discounted ?? 0, record.derated ?? 0)" type="Currency" />
          <Format v-if="column.dataIndex === 'total'" :value="record.total" type="Currency" />
          <Format v-if="column.dataIndex === 'amount'" :value="record.amount" type="Currency" />
          <Format v-if="column.dataIndex === 'price'" :value="record.price" params="4" type="Currency" />
          <template v-if="column.dataIndex === 'selfPaidPct'">
            <Format :value="record.selfPaidPct" type="Percent" />
          </template>
        </template>
      </Table>
    </View>
  </Api>
</template>
