<script lang="ts" setup>
import { add, Api, format, Format, subtract } from '@idmy/core'
import { CashActionTypeEnum, CashTypeEnum, pageCash } from '@mh-bcs/util'

const { cashTypeIds, actionType, checkId, paymentId, insuranceTypeId, endAt, paymentName, insuranceTypeIdNotNull } = defineProps({
  cashTypeIds: { type: Array as PropType<CashTypeEnum[]>, required: true },
  actionType: { type: Number as PropType<CashActionTypeEnum>, required: true },
  checkId: { type: Number as PropType<number> },
  paymentId: { type: Number as PropType<number> },
  paymentName: { type: String as PropType<string> },
  insuranceTypeId: { type: Number as PropType<number> },
  insuranceTypeIdNotNull: { type: Boolean as PropType<boolean> },
  endAt: { type: Object },
})

const userStore = useUserStore()

const inputParams = ref({
  params: {
    S_IN_t_cash__cash_type_id: cashTypeIds?.join(','),
    S_EQ_t_cash__action_type: actionType,
    S_EQ_t_cash__insurance_type_id: insuranceTypeId,
    S_EQ_t_cash__check_id: checkId,
    S_EQ_t_cash__user_id: checkId ? null : Currents.id,
    paymentId,
    paymentName,
    statuss: [1],
  },
  pageSize: 20,
})

if (insuranceTypeIdNotNull) {
  inputParams.value.params.S_ISNOTNULL_t_cash__insurance_type_id = 1
}

if (!checkId) {
  inputParams.value.params.S_ISNULL_t_cash__check_id = true
}

if (endAt) {
  inputParams.value.params.S_LE_t_cash__time_created = format(endAt, 'Date', 'YYYY-MM-DD HH:mm:ss')
}
console.info(inputParams.value)

const columns = [
  {
    align: 'center',
    customRender: ({ index }: any) => index + 1,
    dataIndex: 'no',
    fixed: 'left',
    title: '#',
    width: 45,
  },
  { align: 'center', dataIndex: 'cashId', fixed: 'left', title: '结算流水', width: 90 },
  { align: 'center', dataIndex: 'patientName', title: '患者姓名', width: 90 },
  { align: 'center', dataIndex: 'clinicianName', title: '开单医生', width: 90 },
  { align: 'right', dataIndex: 'presettleAmount', title: '总费用', width: 90 },
  { align: 'right', dataIndex: 'discounts', title: '优惠金额', width: 80 },
  { align: 'right', dataIndex: 'amount', title: '结算金额', width: 100 },
  { align: 'right', dataIndex: 'miFundAmt', title: '医保支付', width: 80 },
  { align: 'right', dataIndex: 'selfFee', title: '自费金额', width: 80 },
  { align: 'center', dataIndex: 'createdAt', title: '创建时间', width: 170 },
  { align: 'center', dataIndex: 'cashTypeId', title: '收费类型', width: 100 },
  { align: 'center', dataIndex: 'paymentNames', title: '支付方式', width: 120, ellipsis: true },
  { align: 'center', dataIndex: 'insuranceTypeId', title: '医保险种', width: 120, ellipsis: true },
  { align: 'center', dataIndex: 'cashtDate', title: '结算日期', width: 100 },
  { align: 'center', dataIndex: 'visitId', title: '诊疗流水', width: 90 },
  { align: 'center', dataIndex: 'billCount', title: '划价数', width: 60 },
]
</script>

<template>
  <Api v-slot="{ output, page }" :input="inputParams" :load="pageCash" spin>
    <Table :columns="columns" :dataSource="(output as any).list" :pagination="{...page, showTotal: (total: number) => `共${total}条`}" rowKey="cashId">
      <template #bodyCell="{ column: col, record: row }">
        <Format v-if="col.dataIndex === 'insuranceTypeId'" :value="row.insuranceTypeId" params="InsuranceType" type="Dict" />
        <Format v-else-if="col.dataIndex === 'checkId'" :value="!!row.checkId" type="Boolean" />
        <Format v-else-if="col.dataIndex === 'cashTypeId'" :value="row.cashTypeId" params="CashType" type="Enum" />
        <Format v-else-if="col.dataIndex === 'miFundAmt'" :value="row.miFundAmt ?? '-'" type="Currency" />
        <Format v-else-if="col.dataIndex === 'selfFee'" :value="subtract(row.amount, row.miFundAmt ?? 0)" type="Currency" />
        <Format v-else-if="col.dataIndex === 'createdAt'" :value="row.createdAt" type="Datetime" />
        <Format v-else-if="col.dataIndex === 'cashtDate'" :value="String(row.cashtDate)" type="Date" />
        <Format v-else-if="col.dataIndex === 'discounts'" :value="add(row.discounted ?? 0, row.derated ?? 0)" type="Currency" />
        <Format v-else-if="col.dataIndex === 'presettleAmount'" :value="row.presettleAmount" type="Currency" />
        <Format v-else-if="col.dataIndex === 'amount'" :value="row.amount" type="Currency" />
        <Format v-else-if="col.dataIndex === 'checkId'" :value="!!row.checkId" type="Boolean" />
      </template>
    </Table>
  </Api>
</template>
