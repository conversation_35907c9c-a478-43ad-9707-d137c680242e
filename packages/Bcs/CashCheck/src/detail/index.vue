<script lang="ts" setup>
import { clearObject, format, Format, useCache, useLoading } from '@idmy/core'
import { lodapPrintHtml } from '@mh-base/core'
import { CashTypeEnum, cashTypes, listSql } from '@mh-bcs/util'
import { isEmpty } from 'lodash-es'
import { getLastCashCheck } from '../services.ts'
import CashCheckPayment from './CashCheckPayment.vue'
import CashPayment from './CashPayment.vue'
import CashRed from './CashRed.vue'
import FeeType from './FeeType.vue'

const userStore = useUserStore()

const { cashCheck, checkId, endAt } = defineProps({
  checkId: { type: Number },
  endAt: { type: Object },
  cashCheck: {
    type: Object,
    default: () => {
      return {}
    },
  },
})

const refreshing = ref(false)
const onRefresh = () => {
  refreshing.value = true
  nextTick(() => {
    refreshing.value = false
  })
}

const last = ref({})
const [onLoad] = useLoading(async (userId: any) => {
  const tmp = await getLastCashCheck(userId)
  if (tmp) {
    tmp.lastChecked = tmp.timeChecked
    tmp.timeChecked = Date.now()
    last.value = tmp
  } else {
    return {}
  }
})
onMounted(() => {
  if (checkId) {
    last.value = cashCheck
  } else {
    onLoad(Currents.id)
  }
})

const printAreaRef = ref()
const onPrint = () => {
  lodapPrintHtml(printAreaRef.value, {
    title: '收费员交账表',
    mode: 'preview',
    style: 'padding: 5mm',
  })
}

let defaultVal
if (Currents.orgId === 360103001) {
  defaultVal = 'aa'
} else if (Currents.orgId === 431100001) {
  defaultVal = 'b'
} else {
  defaultVal = 'a'
}

const miShowMode = useCache<string>('checkCashMiShowMode1', defaultVal, -1, { never: true, global: true })
const isShowRefund = useCache<boolean>('checkCashIsShowRefund', false, -1, { never: true, global: true })
const isMergeReg = useCache<boolean>('checkCashIsMergeReg', true, -1, { never: true, global: true })
const isMergePrepaid = useCache<boolean>('checkCashIsMergePrepaid', true, -1, { never: true, global: true })
const isMergeAll = useCache<boolean>('checkCashIsMergeAll', false, -1, { never: true, global: true })
const isShowRate = useCache<boolean>('checkCashIsShowRate', false, -1, { never: true, global: true })

const total = reactive({
  all: 0,
  mi: 0,
  cash: 0,
})

const load = (all, mi, cash) => {
  total.all += all
  total.mi += mi
  total.cash += cash
}

const hasData = reactive<any>({})
const [onLoadHasData, hasLoading] = useLoading(async () => {
  clearObject(hasData)
  let sql = ''
  if (checkId) {
    sql = `select cash_type_id cashTypeId
		       from microhis_bcs.t_cash
		       where check_id = ${checkId}
				     and validated_flag = 1
		       group by cash_type_id`
  } else {
    sql = `select cash_type_id cashTypeId
           from microhis_bcs.t_cash
           where check_id is null
             and user_id = ${Currents.id}
             and org_id = ${Currents.orgId}
             and validated_flag = 1
             and time_created <= '${format(endAt, 'Date', 'YYYY-MM-DD HH:mm:ss')}'
           group by cash_type_id`
  }
  const arr = await listSql(sql)
  if (isMergeAll.value && arr.length) {
    hasData[CashTypeEnum.outpatient] = true
  } else {
    const hasInpatient = !!arr.find(row => row.cashTypeId === CashTypeEnum.inpatient)
    for (const row of arr) {
      if (isMergeReg.value && row.cashTypeId === CashTypeEnum.reg) {
        continue
      }
      if (isMergePrepaid.value && row.cashTypeId === CashTypeEnum.deposit) {
        if (hasInpatient) {
          continue
        }
      }
      hasData[row.cashTypeId] = true
    }
  }
}, true)

watch([() => isMergeReg.value, () => isMergePrepaid.value, () => isMergeAll.value], () => {
  total.all = 0
  total.cash = 0
  total.mi = 0
  onRefresh()
  onLoadHasData()
})

const toCashTypeIds = (cashTypeId: CashTypeEnum) => {
  if (isMergeAll.value) {
    return [CashTypeEnum.reg, CashTypeEnum.outpatient, CashTypeEnum.inpatient, CashTypeEnum.deposit]
  } else {
    if (cashTypeId === CashTypeEnum.outpatient) {
      if (isMergeReg.value) {
        return [CashTypeEnum.reg, CashTypeEnum.outpatient]
      }
    }
    if (cashTypeId === CashTypeEnum.inpatient) {
      if (isMergePrepaid.value) {
        return [CashTypeEnum.inpatient, CashTypeEnum.deposit]
      }
    }
    return [cashTypeId]
  }
}
</script>

<template>
  <template v-if="!refreshing">
    <CashCheckPayment v-if="checkId" :checkId="checkId" />
    <div flex items-center justify-between mb-8px>
      <Space>
        <Popover placement="right">
          <Button size="small">医保展示</Button>
          <template #content>
            <RadioGroup v-model:value="miShowMode">
              <Radio value="a">不区分险种</Radio>
              <Radio value="b">区分险种</Radio>
              <Radio value="aa">医保现金不区分险种</Radio>
              <Radio value="bb">医保现金区分险种</Radio>
            </RadioGroup>
          </template>
        </Popover>
        <Checkbox v-model:checked="isShowRefund">退费记录</Checkbox>
        <Checkbox v-model:checked="isShowRate">支付占比</Checkbox>
        <Checkbox v-model:checked="isMergeAll" :disabled="hasLoading">合并全部</Checkbox>
        <template v-if="!isMergeAll">
          <Checkbox v-model:checked="isMergeReg" :disabled="hasLoading">合并门诊/挂号</Checkbox>
          <Checkbox v-model:checked="isMergePrepaid" :disabled="hasLoading">合并住院/预交金</Checkbox>
        </template>
      </Space>
      <Button type="primary" @click="onPrint">打印</Button>
    </div>
    <div ref="printAreaRef">
      <div class="font-bold text-18px mb-8px tac">收费交账表</div>
      <div flex justify-between>
        <span class="mr-8px">收费员：{{ last?.casher ?? userStore.nickname }}</span>
        <div>
          交账日期：
          <template v-if="last?.lastChecked">
            <Format :value="last.lastChecked" type="Datetime" />
            至
          </template>
          <Format :value="last?.timeChecked ?? endAt" type="Datetime" />
        </div>
      </div>
      <hr />
      <template v-for="ct in cashTypes">
        <template v-if="hasData[ct.value]">
          <div v-if="!isMergeAll" class="font-bold text-16px tac mt-8px">{{ ct.title }}</div>
          <CashPayment :cashType="ct.value" :cashTypeIds="toCashTypeIds(ct.value)" :checkId="checkId" :endAt="endAt" :isShowRate="isShowRate" :miShowMode="miShowMode" @load="load" />
          <template v-if="ct.value !== CashTypeEnum.deposit">
            <FeeType :cashTypeIds="toCashTypeIds(ct.value)" :checkId="checkId" />
          </template>
          <CashRed v-if="isShowRefund" :cashTypeIds="toCashTypeIds(ct.value)" :checkId="checkId" />
        </template>
      </template>
      <div v-if="isEmpty(hasData)" flex items-center justify-center>
        <a-empty />
      </div>
      <div class="flex jcfe mt-8px">
        <Format :value="total.all" prefix="费用总额：" type="Currency" value-class="primary font-bold " />
        <Format :value="total.mi" class="ml-16px" prefix="医保总额：" type="Currency" value-class="primary font-bold " />
        <Format :value="total.cash" class="ml-16px" prefix="交账总额：" type="Currency" value-class="error font-bold" />
      </div>
      <div class="mt-8px" flex justify-between>
        <div>制表：{{ last?.casher ?? userStore.nickname }}</div>
        <div>审核：</div>
        <div>会计：</div>
        <div style="width: 140px">出纳：</div>
      </div>
    </div>
  </template>
</template>
<style lang="less"></style>
