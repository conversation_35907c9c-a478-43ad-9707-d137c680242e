<script lang="ts" setup>
import { Api, Data, Dialog, Modal, useLoading } from '@idmy/core'
import { pageCashCheck, undo } from '@mh-bcs/util'
import { Table } from 'ant-design-vue'
import Detail from './detail/index.vue'

const apiRef = ref()
const onLoad = () => apiRef.value.onLoad()

function openDetail(cashCheck: Data) {
  Modal.open({
    component: Detail,
    title: `已交账明细#${cashCheck.checkId}`,
    escClosable: true,
    maskClosable: true,
    props: { cashCheck, checkId: cashCheck.checkId },
    onClose: (isOk: boolean) => {
      isOk && onLoad()
    },
  })
}

const columns = [
  {
    align: 'center',
    customRender: ({ index }: any) => index + 1,
    dataIndex: 'no',
    fixed: 'left',
    title: '#',
    width: 45,
  },
  { align: 'center', dataIndex: 'checkId', fixed: 'left', title: '交账流水号', width: 90 },
  { align: 'center', dataIndex: 'casher', fixed: 'left', title: '收费员', width: 90 },
  { align: 'center', dataIndex: 'lastChecked', title: '上次交账时间', width: 100 },
  { align: 'center', dataIndex: 'timeChecked', title: '本次交账时间', width: 100 },
  { align: 'center', dataIndex: 'cashCount', title: '结算笔数', width: 100 },
  { align: 'center', dataIndex: 'op', title: '操作', width: 100 },
]
const userStore = useUserStore()

const [onUndo, undoing] = useLoading(async (checkId: number) => {
  await Dialog.confirm({ title: '撤销', content: '确定要撤销本次交账吗？' })
  await undo(checkId)
  await onLoad()
})
</script>

<template>
  <Api ref="apiRef" v-slot="{ input, output, page, rowKey, onLoad }" :input="{ pageSize: 20 }" :load="pageCashCheck" row-key="checkId" spin>
    <Form :colon="false" :model="input.params" layout="inline" @finish="onLoad({ pageNo: 1 })">
      <FormItem label="起止日期">
        <RangePicker v-model:value="input.params.timeCheckeds" class="w-222px" value-format="YYYY-MM-DD" @change="onLoad()" />
      </FormItem>
      <FormItem>
        <template #label>
          收费员<a
            @click="
              input.params.S_EQ_t_user_code__user_name = userStore.userInfo?.username
              onLoad()
            "
            >「我」</a
          >
        </template>
        <Input v-model:value="input.params.S_EQ_t_user_code__user_name" class="w-100px" />
      </FormItem>
      <FormItem>
        <Button html-type="submit" type="primary">查询</Button>
      </FormItem>
    </Form>
    <Table :columns="columns" :data-source="(output as any).list" :pagination="page" :row-key="rowKey" size="small">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'checkId'">
          <Button size="small" type="link" @click="openDetail(record)">
            {{ record.checkId }}
          </Button>
        </template>
        <template v-if="column.dataIndex === 'op'">
          <Button :loading="undoing" size="small" type="link" @click="onUndo(record.checkId)"> 撤销交账 </Button>
        </template>
      </template>
    </Table>
  </Api>
</template>
