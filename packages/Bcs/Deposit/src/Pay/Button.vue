<script setup lang="ts">
import { Button } from 'ant-design-vue'
import Pay from './Pay.vue'

defineOptions({ name: 'DepositPayButton' })

const { accountId, visitId } = defineProps({
  accountId: { type: Number },
  visitId: { type: Number },
})

const emit = defineEmits(['ok'])

const [open] = useLoading(async () => {
  Modal.open({
    component: Pay,
    title: '预交金充值',
    width: 2,
    props: {
      visitId,
      accountId,
    },
    onClose: (isOk: boolean) => {
      isOk && Message.success('充值成功')
      emit('ok')
    },
  })
})
</script>

<template>
  <Button type="primary" :disabled="!accountId || !visitId" @click="open">充值</Button>
</template>
