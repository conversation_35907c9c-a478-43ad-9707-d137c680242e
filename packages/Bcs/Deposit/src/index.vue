<script setup lang="ts">
import Page from './Page.vue'
import PayButton from './Pay/Button.vue'
import RefundButton from './Refund/Button.vue'

const { accountId, visitId } = defineProps({
  showCond: { type: Boolean, default: true },
  accountId: { type: Number },
  visitId: { type: Number },
  showButton: { type: Boolean, default: true },
})

const pageRef = ref()
const onLoad = () => pageRef.value.onLoad()
</script>

<template>
  <Page :accountId="accountId" :visitId="visitId" :showCond="showCond" :showButton="showButton" ref="pageRef">
    <template #button>
      <PayButton :accountId="31596" :visitId="88000" @ok="onLoad" />
      <RefundButton :accountId="31596" :visitId="88000" @ok="onLoad" />
    </template>
    <template #op v-slot="{ row, col }"> </template>
  </Page>
</template>
