<script setup lang="ts">
import { Api, clearObject, Format, format } from '@idmy/core'
import { depositRefund, listPlusPaymentTypesAmt } from '@mh-bcs/util'
import { PaymentTypeEnum } from '@mh-hip/payment-type'
import { SUPPORT_BACKTRACK_PAYMENT_TYPES } from '@mh-hip/util'
import { Button, InputNumber, Popover, Space, Table } from 'ant-design-vue'
import Page from '../Page.vue'

const { accountId, visitId } = defineProps({
  showCond: { type: Boolean, default: true },
  accountId: { type: Number },
  visitId: { type: Number },
  showButton: { type: Boolean, default: true },
})

const pageRef = ref()
const [onLoad] = useLoading(async () => {
  pageRef.value.onLoad()
})

const state = reactive({
  paymentType: 'CASH',
  amount: null,
})

const [onRefund, refunding] = useLoading(async row => {
  if (isNil(state.amount)) {
    Message.warn('请输入退费金额')
    return
  }
  await depositRefund(state.paymentType, state.amount)
  Message.success('退费成功')
  clearObject(state)
  onLoad()
})

const columns = [
  { align: 'center', dataIndex: 'paymentType', title: '支付方式', width: 150 },
  { align: 'right', dataIndex: 'amount', title: '支付金额', width: 150 },
  { align: 'center', dataIndex: 'support', title: '原路返回', width: 150 },
  { align: 'right', dataIndex: 'refundableAmount', title: '可退金额', width: 150 },
]
</script>

<template>
  <Api :load="() => listPlusPaymentTypesAmt(accountId)" v-slot="{ output, loading }" type="Array" first>
    <div flex justify-between>
      <Format prefix="可退金额：" value-class="color-error text-18px font-bold" suffix=" 元" type="Current" :value="1410" />
      <Space class="flex justify-end">
        退费方式
        <PaymentTypeEnum
          :clearable="false"
          v-model="state.paymentType"
          class="w-130px!"
          :filter="row => output.map(i => i.paymentType).includes(row.key) || !SUPPORT_BACKTRACK_PAYMENT_TYPES.includes(row.key)"
        />
        退费金额
        <InputNumber class="w-120px" v-model:value="state.amount" />
        <Button type="primary" @click="onRefund" :loading="refunding">确认</Button>
      </Space>
    </div>
    <Table size="small" bordered :columns="columns.filter(row => row.show !== false)" :dataSource="output" :pagination="false" :loading="loading" :rowKey="row => `${row.paymentType}`">
      <template #emptyText>
        <div flex-center h-40px>暂无数据</div>
      </template>
      <template #bodyCell="{ column: col, record: row }">
        <template v-if="col.dataIndex === 'paymentType'">
          <Popover :mouseEnterDelay="0.3" :overlayInnerStyle="{ width: '800px' }" placement="bottom">
            <template #content>
              <Page
                ref="pageRef"
                :accountId="accountId"
                :visitId="visitId"
                :showCond="false"
                :showNo="false"
                :showOp="false"
                :params="{
                  transStatus: 'OK',
                  paymentTypes: [row.paymentType],
                  amounts: {
                    l: 'GT',
                    r: 0,
                  },
                }"
              />
            </template>
            <Format color-primary type="Enum" params="PaymentType" :value="row.paymentType" />
          </Popover>
        </template>
        <template v-if="col.dataIndex === 'support'">
          <template v-if="SUPPORT_BACKTRACK_PAYMENT_TYPES.includes(row.paymentType)">支持</template>
          <template v-else>不支持</template>
        </template>
        <template v-if="col.dataIndex === 'amount'">
          <Format type="Currency" :value="row.amount" />
        </template>
        <template v-if="col.dataIndex === 'refundableAmount'">
          <template v-if="SUPPORT_BACKTRACK_PAYMENT_TYPES.includes(row.paymentType)">
            <Format type="Currency" :value="row.refundableAmount" />
          </template>
          <template v-else>-</template>
        </template>
      </template>
    </Table>
  </Api>
</template>
