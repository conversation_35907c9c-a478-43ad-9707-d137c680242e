<script setup lang="ts">
import { Button } from 'ant-design-vue'
import Refund from './Refund.vue'

defineOptions({ name: 'DepositRefundButton' })

const { accountId, visitId } = defineProps({
  accountId: { type: Number },
  visitId: { type: Number },
})

const emit = defineEmits(['ok'])

const [open] = useLoading(async () => {
  Modal.open({
    component: Refund,
    title: '预交金退费',
    width: 3,
    props: {
      visitId,
      accountId,
    },
    onClose: () => emit('ok'),
  })
})
</script>

<template>
  <Button type="primary" :disabled="!accountId || !visitId" @click="open">退费</Button>
</template>
