<script setup lang="ts">
import { Enum } from '@idmy/antd'
import { Api, Format } from '@idmy/core'
import { pageDepositAccount } from '@mh-bcs/util'
import { PaymentTypeEnum } from '@mh-hip/payment-type'
import { Button, Col, Form, FormItem, Input, InputNumber, RangePicker, Row, Select, Space, Table, Tag } from 'ant-design-vue'

const { accountId, visitId, showNo, showOp, params, pageSize } = defineProps({
  params: { type: Object, default: () => ({}) },
  transStatus: { type: String },
  showNo: { type: Boolean, default: true },
  showOp: { type: Boolean, default: true },
  showCond: { type: Boolean, default: true },
  pageSize: { type: Number, default: 10 },
  accountId: { type: Number },
  visitId: { type: Number },
  showButton: { type: Boolean, default: true },
})

const emit = defineEmits(['load'])

const columns = [
  {
    align: 'center',
    customRender: ({ index }: any) => index + 1,
    dataIndex: 'no',
    fixed: 'left',
    title: '序号',
    width: 50,
    show: showNo,
  },
  { align: 'center', dataIndex: 'cashId', title: '收款流水' },
  { align: 'center', dataIndex: 'accountId', title: '账户ID', show: !accountId },
  { align: 'center', dataIndex: 'visitId', title: '诊疗流水', show: !visitId },
  { align: 'center', dataIndex: 'paymentType', title: '支付方式' },
  { align: 'center', dataIndex: 'transStatus', title: '交易状态' },
  { align: 'center', dataIndex: 'payee', title: '收费员' },
  { align: 'center', dataIndex: 'payer', title: '付款人', show: !visitId && !accountId },
  { align: 'right', dataIndex: 'amount', title: '金额' },
  { align: 'right', dataIndex: 'balance', title: '余额' },
  { align: 'left', dataIndex: 'notes', title: '备注' },
  { align: 'center', dataIndex: 'createdAt', title: '交易时间', width: 150 },
  { align: 'center', dataIndex: 'op', title: '操作', width: 100, show: showOp },
]

const input = ref({
  params: {
    amounts: {
      l: 'EQ',
      r: undefined,
    },
    balances: {
      l: 'EQ',
      r: undefined,
    },
    notes: undefined,
    transStatus: undefined,
    cashId: undefined,
    payer: undefined,
    payee: undefined,
    visitId,
    accountId,
    transStatuses: [],
    paymentTypes: [],
    createdAts: [],
    ...params,
  },
  pageSize,
  sorts: ['createdAt', 'desc', 'acctNo', 'desc'],
})

const apiRef = ref()
const [onLoad] = useLoading(async () => {
  apiRef.value.onLoad()
})

defineExpose({
  onLoad,
})
</script>
<template>
  <Api ref="apiRef" v-slot="{ output, loading, page }" :input="input" :load="pageDepositAccount" @load="$emit('load', $event.output.list)" first>
    <Form v-if="showCond" :colon="false" :model="input.params" class="oh" @finish="onLoad({ pageNo: 1 })">
      <Row :gutter="24">
        <Col :span="6">
          <FormItem label="金额">
            <div flex>
              <Select
                class="w-100px!"
                mr-1px
                v-model:value="input.params.amounts.l"
                :options="[
                  { label: '等于', value: 'EQ' },
                  { label: '不等于', value: 'NE' },
                  { label: '大于', value: 'GT' },
                  { label: '大于等于', value: 'GE' },
                  { label: '小于', value: 'LT' },
                  { label: '小于等于', value: 'LE' },
                ]"
              />
              <InputNumber v-model:value="input.params.amounts.r" class="f1" />
            </div>
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="余额">
            <div flex>
              <Select
                class="w-100px!"
                mr-1px
                v-model:value="input.params.balances.l"
                :options="[
                  { label: '等于', value: 'EQ' },
                  { label: '不等于', value: 'NE' },
                  { label: '大于', value: 'GT' },
                  { label: '大于等于', value: 'GE' },
                  { label: '小于', value: 'LT' },
                  { label: '小于等于', value: 'LE' },
                ]"
              />
              <InputNumber v-model:value="input.params.balances.r" class="f1" />
            </div>
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="交易时间">
            <RangePicker v-model:value="input.params.createdAts" class="w-100%" show-time value-format="YYYY-MM-DD HH:mm:ss" @change="onLoad()" />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="结算流水">
            <InputNumber v-model:value="input.params.cashId" :controls="false" class="w-100%" placeholder="结算流水" />
          </FormItem>
        </Col>
      </Row>
      <Row :gutter="24">
        <Col :span="6">
          <FormItem label="收费人">
            <Input v-model:value="input.params.payee" class="w-100%" />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="备注">
            <Input v-model:value="input.params.notes" class="w-100%" allowClear />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="支付方式">
            <PaymentTypeEnum v-model="input.params.paymentTypes" multiple @change="onLoad()" />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="交易状态">
            <Enum v-model="input.params.transStatuses" class="w-100%" clazz="TransStatus" multiple @change="onLoad()" />
          </FormItem>
        </Col>
      </Row>
      <Row :gutter="24">
        <Col :span="6" v-if="!accountId && !visitId">
          <FormItem label="付款人">
            <Input v-model:value="input.params.payer" class="w-100%" placeholder="患者姓名" />
          </FormItem>
        </Col>
        <Col :span="6" v-if="!visitId">
          <FormItem label="诊疗流水">
            <InputNumber v-model:value="input.params.visitId" :controls="false" class="w-100%" placeholder="诊疗流水" />
          </FormItem>
        </Col>
        <Col :span="6" v-if="!accountId && !visitId">
          <FormItem label="账户ID">
            <InputNumber v-model:value="input.params.accountId" :controls="false" class="w-100%" />
          </FormItem>
        </Col>
      </Row>
    </Form>
    <Row class="mb-8px">
      <Space>
        <Button html-type="submit" type="primary" @click="onLoad" v-if="showButton && showCond">查询</Button>
        <slot name="button" />
      </Space>
    </Row>
    <Table
      size="small"
      bordered
      :columns="columns.filter(row => row.show !== false)"
      :dataSource="output.list"
      :loading="loading"
      :pagination="{ ...page }"
      :rowKey="row => `${row.accountId}${row.acctNo}`"
    >
      <template #emptyText>
        <div flex-center h-40px>暂无数据</div>
      </template>
      <template #bodyCell="{ column: col, record: row }">
        <template v-if="col.dataIndex === 'paymentType'">
          <Format type="Enum" params="PaymentType" :value="row.paymentType" />
        </template>
        <template v-if="col.dataIndex === 'transStatus'">
          <Format type="Enum" :bordered="false" :component="Tag" params="TransStatus" :value="row.transStatus" />
        </template>
        <template v-if="col.dataIndex === 'amount'">
          <Format type="Currency" :value="row.amount" />
        </template>
        <template v-if="col.dataIndex === 'balance'">
          <Format type="Currency" :value="row.balance" />
        </template>
        <template v-if="col.dataIndex === 'op'">
          <slot name="op" :row="row" :col="col" />
        </template>
      </template>
    </Table>
  </Api>
</template>
