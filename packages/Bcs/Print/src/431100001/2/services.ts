import { Data, format } from '@idmy/core'





export async function getSql1(cashId: number) {
  const sql = `  select
			               (select mc.Diag_Name from microhis_hsd.t_visit_diag vd where v.Visit_ID = vd.Visit_ID order by Time_Diagnosed limit 1 ) diag,
        mc.Psn_Admdvs psnAdmdvs,
        v.Civil_Servant_Flag civilServant,
        ve.Employer_Name company,
        d.Dept_Name dept,
        v.Time_Admission inAt,
        i.Time_Discharged outAt,
        i.Hospitalized_Days days,
        c.Cash_ID cashId,
        t.amount amt,
        c.Casht_Date cashAt,
        c.Visit_ID visitId,
        it.Insurance_Name insurance,
        mt.Med_Type_Name medicalType,
        v.Patient_Name name,
        v.Gender_ID gender,
        p.Birth_Date birthDate,
        pt.Psn_Type_Name personType,
        hip_mpi.decrypt_data(p.IDCert_No) idCertNo,
        hip_mpi.decrypt_data(p.Contactor_Tel) mobile,
        t.Hospital_Part_Amt hospitalPartAmt,
        t.Pay_Line_Amt payLineAmt,
        t.Acct_Pay_Amt acctAmt,
        t.Fund_Pay_Amt fundAmt,
        t.Cash_Pay_Amt cashAmt,
        t.Pre_SelfPay_Amt preSelfAmt,
        t.Multi_Aid_Amt multiAidAmt,
        t.Acct_Balance acctBalance,
        t.In_Scope_Amt scopeAmt
	               from microhis_bcs.t_cash c
		               inner join microhis_hsd.t_visit v
	               on c.Visit_ID = v.Visit_ID
		               inner join microhis_hsd.t_visit_extra ve on v.Visit_ID = ve.Visit_ID
		               left join hip_mdi.t_insurance_type it on c.insurance_type_id = it.Insurance_Type_ID
		               left join hip_mdi.t_medical_type mt on c.Med_Type_ID = mt.Med_Type_ID
		               left join hip_mdi.t_person_type pt on v.Psn_Type_ID = pt.Psn_Type_ID
		               left join hip_mpi.t_patient p on v.Patient_ID = p.Patient_ID
		               left join microhis_hsd.t_inpatient i on v.Visit_ID = i.Visit_ID
		               left join hip_mdi.t_org_dept d on v.Dept_Code = d.Dept_Code and d.Org_ID = c.Org_ID
		               left join microhis_mcisp.t_success_trans t on t.Cash_ID = c.Cash_ID
		               left join microhis_mcisp.t_mi_case mc on mc.Visit_ID = c.Visit_ID
	               where c.Validated_Flag = 1 and c.cash_id = ${cashId}
	`
  return await http.post('/api/bcs/print/getSql', { sql })

}

export async function getCashInfo(cashId: number) {
  const data = await getSql1(cashId)
  data.name = `姓名：${data.name ?? ''} ${data.gender ?? ``}`
  data.clinician = `医生：${data.clinician ?? ''} ${data.dept ?? ''}`
  data.patientNo = `病历号：${data.patientNo ?? ''}`
  data.cashId = `票据号：${cashId}`
  data.payMode = `结算方式：${(data.miFundPay || data.miAccountPay) > 0 ? '医保' : '自费'}`
  data.payee = `收费员：${data.payee ?? ''}`
  data.amount = `合计金额：${format(data.amount ?? 0, 'Currency')}`
  data.miFundPay = `医保统筹：${format(data.miFundPay ?? 0, 'Currency')}`
  data.miAccountPay = `医保个账：${format(data.miAccountPay ?? 0, 'Currency')}`
  data.selfFee = `个人缴费：${format(data.selfFee ?? 0, 'Currency')}`
  data.date = `结算时间：${format(data.date, 'Datetime')}`
  data.printTime = `打印时间：${format(Date.now(), 'Datetime')}`
  if (data.regType) {
    data.regType = `挂号类型：${data.regType}`;
  }
  if (data.reg) {
    data.aptNo = data.reg.aptNo
  }
  if (data.qrcode) {
    data.qrcode = `data:image/png;base64,${data.qrcode}`;
  }
  data.items.forEach((item: Data) => {
    item.amount = format(item.amount ?? 0, 'Currency')
    item.total = format(item.total ?? 0, 'Currency')
    item.price = format(item.price ?? 0, 'Currency', 4)
  })

}
